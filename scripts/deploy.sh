#!/bin/bash

# UISP-Omega Bridge Deployment Script
# This script deploys the complete application stack

set -e

echo "🚀 Starting UISP-Omega Bridge deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from template..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before continuing."
        exit 1
    else
        print_error ".env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Parse command line arguments
ENVIRONMENT=${1:-development}
PROFILE=${2:-}

print_status "Deploying in $ENVIRONMENT mode..."

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose down

# Pull latest images (for production)
if [ "$ENVIRONMENT" = "production" ]; then
    print_status "Pulling latest images..."
    docker-compose pull
fi

# Build and start services
print_status "Building and starting services..."
if [ "$ENVIRONMENT" = "production" ]; then
    if [ -n "$PROFILE" ]; then
        docker-compose --profile $PROFILE up -d --build
    else
        docker-compose up -d --build
    fi
else
    docker-compose up -d --build
fi

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check backend health
print_status "Checking backend health..."
for i in {1..30}; do
    if curl -f http://localhost:3000/api/health &> /dev/null; then
        print_success "Backend is healthy!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend health check failed after 30 attempts"
        docker-compose logs backend
        exit 1
    fi
    sleep 2
done

# Check frontend health
print_status "Checking frontend health..."
for i in {1..30}; do
    if curl -f http://localhost:3001/health &> /dev/null; then
        print_success "Frontend is healthy!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Frontend health check failed after 30 attempts"
        docker-compose logs frontend
        exit 1
    fi
    sleep 2
done

# Run database migrations
print_status "Running database migrations..."
docker-compose exec backend npm run db:deploy

# Show running services
print_status "Running services:"
docker-compose ps

# Show logs
if [ "$ENVIRONMENT" = "development" ]; then
    print_status "Showing recent logs..."
    docker-compose logs --tail=50
fi

print_success "Deployment completed successfully!"
print_status "Services are available at:"
echo "  - Frontend: http://localhost:3001"
echo "  - Backend API: http://localhost:3000"
echo "  - API Documentation: http://localhost:3000/api/docs"

if [ "$ENVIRONMENT" = "development" ]; then
    echo ""
    print_status "Development commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart services: docker-compose restart"
    echo "  - Database studio: docker-compose exec backend npm run db:studio"
fi

print_success "🎉 UISP-Omega Bridge is now running!"
