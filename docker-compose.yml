version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: uisp-omega-db
    environment:
      POSTGRES_DB: uisp_omega_bridge
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - uisp-omega-network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: uisp-omega-backend
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/uisp_omega_bridge
      PORT: 3000
      UISP_API_URL: ${UISP_API_URL}
      UISP_API_KEY: ${UISP_API_KEY}
      OMEGA_IMPORT_PATH: /app/omega-exports
      JWT_SECRET: ${JWT_SECRET:-your-jwt-secret-change-in-production}
    ports:
      - "3000:3000"
    volumes:
      - omega_exports:/app/omega-exports
      - ./logs:/app/logs
    depends_on:
      - postgres
    networks:
      - uisp-omega-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Nginx serving built React app)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: uisp-omega-frontend
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - uisp-omega-network
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: uisp-omega-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - uisp-omega-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: uisp-omega-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - omega_exports:/var/www/omega-exports:ro
    depends_on:
      - backend
      - frontend
    networks:
      - uisp-omega-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  omega_exports:
    driver: local

networks:
  uisp-omega-network:
    driver: bridge
