# UISP-Omega Bridge Frontend

Modern React frontend application for the UISP-Omega Bridge system, built with TypeScript, Material-UI, and Vite.

## Features

- **Modern React Stack**: React 18, TypeScript, Vite
- **Material-UI Design**: Professional UI with Material Design components
- **State Management**: TanStack Query for server state, Zustand for client state
- **Responsive Design**: Mobile-first responsive layout
- **Real-time Updates**: Automatic data refresh and live updates
- **Form Handling**: React Hook Form with validation
- **Charts & Visualization**: Recharts for data visualization
- **File Upload**: Drag & drop file upload with preview
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Testing**: Vitest and React Testing Library

## Tech Stack

### Core
- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Material-UI v5** - Component library

### State Management
- **TanStack Query** - Server state management
- **Zustand** - Client state management

### Forms & Validation
- **React Hook Form** - Form handling
- **Joi** - Schema validation (shared with backend)

### Routing
- **React Router v6** - Client-side routing

### Charts
- **Recharts** - Data visualization

### Testing
- **Vitest** - Test runner
- **React Testing Library** - Component testing
- **@testing-library/jest-dom** - DOM matchers

## Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── ErrorBoundary.tsx
│   │   └── Layout.tsx
│   ├── lib/              # Utilities and API client
│   │   └── api.ts
│   ├── pages/            # Page components
│   │   ├── Dashboard.tsx
│   │   ├── UispPage.tsx
│   │   ├── BankPage.tsx
│   │   ├── PaymentPage.tsx
│   │   ├── OmegaPage.tsx
│   │   ├── ConfigPage.tsx
│   │   └── NotFoundPage.tsx
│   ├── test/             # Test files
│   │   ├── setup.ts
│   │   └── Dashboard.test.tsx
│   ├── types/            # TypeScript type definitions
│   │   └── api.ts
│   ├── App.tsx           # Main app component
│   ├── main.tsx          # App entry point
│   └── theme.ts          # Material-UI theme
├── index.html            # HTML template
├── package.json          # Dependencies and scripts
├── tsconfig.json         # TypeScript configuration
├── vite.config.ts        # Vite configuration
└── README.md            # This file
```

## Getting Started

### Prerequisites

- Node.js 18+ and npm 8+
- Backend API running on `http://localhost:3000`

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open browser:**
   Navigate to `http://localhost:3001`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking
- `npm run format` - Format code with Prettier
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage

## Features Overview

### Dashboard
- **System Overview**: Real-time statistics and health status
- **Interactive Charts**: Payment matching and sync progress visualization
- **Recent Activity**: Latest unmatched transactions and unpaid invoices
- **Quick Actions**: Direct access to common operations

### UISP Integration
- **Connection Management**: Test and configure UISP API connection
- **Invoice Sync**: Automatic and manual invoice synchronization
- **Client Management**: View and manage UISP clients
- **Real-time Status**: Live connection and sync status

### Bank Statement Processing
- **File Upload**: Drag & drop interface for bank statement files
- **Multi-format Support**: XML (SEPA), SWIFT MT940, Excel files
- **File Preview**: Validate and preview before import
- **Transaction Management**: View and filter bank transactions

### Payment Matching
- **Automatic Matching**: AI-powered payment-to-invoice matching
- **Manual Matching**: Interface for manual payment assignment
- **Match Statistics**: Detailed matching performance metrics
- **Suggestion Engine**: Smart suggestions for difficult matches

### Omega Export
- **Batch Export**: Export invoices and payments to Omega format
- **Export Preview**: Preview data before export
- **File Management**: Download and manage export files
- **Configuration Validation**: Ensure proper Omega setup

### Configuration
- **UISP Settings**: API credentials and connection settings
- **Omega Settings**: Export path and format configuration
- **Sync Settings**: Automatic synchronization preferences
- **System Information**: Runtime and environment details

## API Integration

The frontend communicates with the backend API through a centralized API client (`src/lib/api.ts`) that provides:

- **Type-safe requests** with TypeScript interfaces
- **Automatic authentication** with JWT tokens
- **Error handling** with user-friendly messages
- **Request/response interceptors** for logging and debugging

### API Client Features

- Automatic token management
- Request/response logging
- Error boundary integration
- Retry logic for failed requests
- Type-safe response handling

## State Management

### Server State (TanStack Query)
- API data caching and synchronization
- Background refetching
- Optimistic updates
- Error and loading states

### Client State (Zustand)
- UI state management
- User preferences
- Form state persistence
- Navigation state

## Styling and Theming

### Material-UI Theme
- Custom color palette
- Typography scale
- Component overrides
- Responsive breakpoints

### Design Principles
- **Consistency**: Unified design language
- **Accessibility**: WCAG 2.1 compliance
- **Responsiveness**: Mobile-first approach
- **Performance**: Optimized rendering

## Testing Strategy

### Unit Tests
- Component behavior testing
- API client testing
- Utility function testing

### Integration Tests
- User workflow testing
- API integration testing
- Error scenario testing

### Test Utilities
- Custom render functions
- Mock API responses
- Test data factories

## Performance Optimization

### Code Splitting
- Route-based code splitting
- Component lazy loading
- Vendor chunk optimization

### Caching Strategy
- API response caching
- Static asset caching
- Service worker integration

### Bundle Optimization
- Tree shaking
- Dead code elimination
- Asset optimization

## Development Guidelines

### Code Style
- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Consistent naming conventions

### Component Guidelines
- Functional components with hooks
- Props interface definitions
- Error boundary wrapping
- Accessibility considerations

### API Guidelines
- Type-safe API calls
- Error handling patterns
- Loading state management
- Cache invalidation strategies

## Deployment

### Build Process
```bash
npm run build
```

### Environment Variables
- `VITE_API_URL` - Backend API URL
- `VITE_APP_VERSION` - Application version

### Production Considerations
- Asset optimization
- CDN integration
- Error monitoring
- Performance monitoring

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Follow TypeScript and React best practices
2. Write tests for new features
3. Update documentation
4. Follow the established code style
5. Test across supported browsers

## License

This project is part of the UISP-Omega Bridge system.
