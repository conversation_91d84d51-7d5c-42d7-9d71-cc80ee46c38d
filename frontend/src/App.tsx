import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';

import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import { UispPage } from './pages/UispPage';
import { UltraSimplePage } from './pages/UltraSimplePage';
import { PaymentPage } from './pages/PaymentPage';
import { OmegaPage } from './pages/OmegaPage';
import { ConfigPage } from './pages/ConfigPage';
import { NotFoundPage } from './pages/NotFoundPage';
import { SimpleTestPage } from './pages/SimpleTestPage';

function App() {
  console.log('🚀 APP.TSX LOADED!');

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/uisp/*" element={<UispPage />} />
          <Route path="/bank/*" element={<UltraSimplePage />} />
          <Route path="/payment/*" element={<PaymentPage />} />
          <Route path="/omega/*" element={<OmegaPage />} />
          <Route path="/config" element={<ConfigPage />} />
          <Route path="/test" element={<SimpleTestPage />} />
          <Route path="/banktest" element={<UltraSimplePage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Layout>
    </Box>
  );
}

export default App;
