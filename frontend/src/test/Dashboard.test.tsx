import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '@mui/material';

import { Dashboard } from '@/pages/Dashboard';
import { theme } from '@/theme';
import * as api from '@/lib/api';

// Mock the API
vi.mock('@/lib/api', () => ({
  api: {
    getStats: vi.fn(),
    getStatus: vi.fn(),
  },
}));

const mockStats = {
  sync: {
    totalInvoices: 150,
    syncedInvoices: 120,
    totalTransactions: 200,
    matchedTransactions: 180,
    totalPayments: 100,
    exportedPayments: 80,
  },
  unmatched: {
    transactions: 20,
    invoices: 30,
  },
  recent: {
    unmatchedTransactions: [
      {
        id: 1,
        amount: 120.50,
        transactionDate: '2024-01-15',
        counterpartyName: 'Test Company',
        variableSymbol: '12345',
      },
    ],
    unpaidInvoices: [
      {
        id: 1,
        invoiceNumber: 'INV-001',
        totalAmount: 150.00,
        dueDate: '2024-01-20',
        clientId: 1,
      },
    ],
  },
};

const mockStatus = {
  database: {
    connected: true,
  },
};

function renderWithProviders(component: React.ReactElement) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </QueryClientProvider>
  );
}

describe('Dashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', () => {
    vi.mocked(api.api.getStats).mockImplementation(() => new Promise(() => {}));
    vi.mocked(api.api.getStatus).mockImplementation(() => new Promise(() => {}));

    renderWithProviders(<Dashboard />);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders dashboard with stats', async () => {
    vi.mocked(api.api.getStats).mockResolvedValue(mockStats);
    vi.mocked(api.api.getStatus).mockResolvedValue(mockStatus);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    // Check if stats are displayed
    expect(screen.getByText('150')).toBeInTheDocument(); // Total invoices
    expect(screen.getByText('200')).toBeInTheDocument(); // Total transactions
    expect(screen.getByText('100')).toBeInTheDocument(); // Total payments

    // Check if matching rate is calculated and displayed
    expect(screen.getByText('90.0%')).toBeInTheDocument(); // 180/200 * 100
  });

  it('renders recent activity', async () => {
    vi.mocked(api.api.getStats).mockResolvedValue(mockStats);
    vi.mocked(api.api.getStatus).mockResolvedValue(mockStatus);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Recent Unmatched Transactions')).toBeInTheDocument();
    });

    expect(screen.getByText('Test Company')).toBeInTheDocument();
    expect(screen.getByText('€120.50')).toBeInTheDocument();
    expect(screen.getByText('INV-001')).toBeInTheDocument();
    expect(screen.getByText('€150.00')).toBeInTheDocument();
  });

  it('handles error state', async () => {
    vi.mocked(api.api.getStats).mockRejectedValue(new Error('API Error'));
    vi.mocked(api.api.getStatus).mockResolvedValue(mockStatus);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText(/Failed to load dashboard data/)).toBeInTheDocument();
    });
  });

  it('shows system status chip', async () => {
    vi.mocked(api.api.getStats).mockResolvedValue(mockStats);
    vi.mocked(api.api.getStatus).mockResolvedValue(mockStatus);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('System Healthy')).toBeInTheDocument();
    });
  });

  it('handles empty recent activity', async () => {
    const emptyStats = {
      ...mockStats,
      recent: {
        unmatchedTransactions: [],
        unpaidInvoices: [],
      },
    };

    vi.mocked(api.api.getStats).mockResolvedValue(emptyStats);
    vi.mocked(api.api.getStatus).mockResolvedValue(mockStatus);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('No unmatched transactions')).toBeInTheDocument();
    });

    expect(screen.getByText('No unpaid invoices')).toBeInTheDocument();
  });
});
