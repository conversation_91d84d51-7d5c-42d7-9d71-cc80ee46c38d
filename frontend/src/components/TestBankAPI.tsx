import React, { useState, useEffect } from 'react';
import { Box, Typography, But<PERSON>, Card, CardContent, Alert } from '@mui/material';

export default function TestBankAPI() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testAPI = async (endpoint: string) => {
    setLoading(true);
    setError(null);
    try {
      console.log(`Testing endpoint: http://localhost:3001/api/bank/${endpoint}`);
      const response = await fetch(`http://localhost:3001/api/bank/${endpoint}`);
      const data = await response.json();
      console.log('Response:', data);
      setResult({ endpoint, data });
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Auto-test statements endpoint on mount
    testAPI('statements');
  }, []);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Bank API Test
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button 
          variant="contained" 
          onClick={() => testAPI('statements')}
          disabled={loading}
        >
          Test Statements
        </Button>
        <Button 
          variant="contained" 
          onClick={() => testAPI('transactions')}
          disabled={loading}
        >
          Test Transactions
        </Button>
        <Button 
          variant="contained" 
          onClick={() => testAPI('supported-types')}
          disabled={loading}
        >
          Test Supported Types
        </Button>
        <Button 
          variant="contained" 
          onClick={() => testAPI('stats')}
          disabled={loading}
        >
          Test Stats
        </Button>
      </Box>

      {loading && (
        <Alert severity="info">Loading...</Alert>
      )}

      {error && (
        <Alert severity="error">Error: {error}</Alert>
      )}

      {result && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Result for: {result.endpoint}
            </Typography>
            <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}
