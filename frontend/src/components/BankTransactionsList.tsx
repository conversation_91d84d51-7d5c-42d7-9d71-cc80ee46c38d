import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  TextField,
  InputAdornment,
  TablePagination,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
} from '@mui/material';
import {
  Search as SearchIcon,
  SwapHoriz as TransactionIcon,
  Link as LinkIcon,
  LinkOff as UnlinkIcon,
  TrendingUp as CreditIcon,
  TrendingDown as DebitIcon,
  Euro as EuroIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';

interface BankTransaction {
  id: number;
  statementId: number;
  transactionDate: string;
  amount: number;
  currency: string;
  debitCredit: 'DBIT' | 'CRDT';
  variableSymbol?: string;
  specificSymbol?: string;
  constantSymbol?: string;
  counterpartyName?: string;
  counterpartyAccount?: string;
  reference?: string;
  description?: string;
  matchedInvoiceId?: number;
  bankStatement: {
    id: number;
    fileName: string;
    bankName: string;
    accountNumber: string;
  };
  matchedInvoice?: {
    id: number;
    invoiceNumber: string;
    totalAmount: number;
    client: {
      firstName: string;
      lastName: string;
      companyName?: string;
    };
  };
}

interface Invoice {
  id: number;
  invoiceNumber: string;
  totalAmount: number;
  client: {
    firstName: string;
    lastName: string;
    companyName?: string;
  };
}

interface BankTransactionsResponse {
  success: boolean;
  data: {
    transactions: BankTransaction[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export function BankTransactionsList() {
  const [transactions, setTransactions] = useState<BankTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [matchedFilter, setMatchedFilter] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [matchDialogOpen, setMatchDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<BankTransaction | null>(null);
  const [availableInvoices, setAvailableInvoices] = useState<Invoice[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: rowsPerPage.toString(),
        search: search,
      });

      if (matchedFilter) {
        params.append('isMatched', matchedFilter);
      }

      const response = await fetch(`/api/bank/transactions?${params}`);
      const result: BankTransactionsResponse = await response.json();

      if (result.success) {
        setTransactions(result.data.transactions);
        setTotalCount(result.data.pagination.total);
      } else {
        setError('Chyba pri načítavaní bankových transakcií');
      }
    } catch (err) {
      setError('Chyba pri komunikácii so serverom');
    } finally {
      setLoading(false);
    }
  };

  const fetchInvoices = async () => {
    try {
      const response = await fetch('/api/data/invoices?limit=1000&status=UNPAID');
      const result = await response.json();
      
      if (result.success) {
        setAvailableInvoices(result.data.invoices);
      }
    } catch (err) {
      console.error('Error fetching invoices:', err);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [page, rowsPerPage, search, matchedFilter]);

  useEffect(() => {
    fetchInvoices();
  }, []);

  const handleMatch = (transaction: BankTransaction) => {
    setSelectedTransaction(transaction);
    setSelectedInvoice(null);
    setMatchDialogOpen(true);
  };

  const handleMatchConfirm = async () => {
    if (!selectedTransaction || !selectedInvoice) return;

    try {
      const response = await fetch('/api/bank/match', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: selectedTransaction.id,
          invoiceId: selectedInvoice.id,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMatchDialogOpen(false);
        fetchTransactions(); // Refresh the list
        alert('Transakcia bola úspešne spárovaná s faktúrou');
      } else {
        alert(`Chyba pri spárovaní: ${result.message}`);
      }
    } catch (error) {
      alert('Chyba pri spárovaní transakcie');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sk-SK');
  };

  const formatCurrency = (amount: number, currency: string = 'EUR') => {
    return new Intl.NumberFormat('sk-SK', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatClientName = (client: BankTransaction['matchedInvoice']['client']) => {
    if (!client) return '';
    if (client.companyName) {
      return client.companyName;
    }
    return `${client.firstName} ${client.lastName}`.trim();
  };

  const getTransactionTypeIcon = (debitCredit: string) => {
    return debitCredit === 'CRDT' ? (
      <CreditIcon sx={{ color: 'success.main', fontSize: 18 }} />
    ) : (
      <DebitIcon sx={{ color: 'error.main', fontSize: 18 }} />
    );
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h2">
              <TransactionIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Bankové transakcie ({totalCount})
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Spárovanie</InputLabel>
                <Select
                  value={matchedFilter}
                  label="Spárovanie"
                  onChange={(e) => setMatchedFilter(e.target.value)}
                >
                  <MenuItem value="">Všetky</MenuItem>
                  <MenuItem value="true">Spárované</MenuItem>
                  <MenuItem value="false">Nespárované</MenuItem>
                </Select>
              </FormControl>
              
              <TextField
                size="small"
                placeholder="Hľadať transakcie..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 300 }}
              />
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Dátum</TableCell>
                      <TableCell>Typ</TableCell>
                      <TableCell>Suma</TableCell>
                      <TableCell>Protiúčet</TableCell>
                      <TableCell>VS/Popis</TableCell>
                      <TableCell>Spárovaná faktúra</TableCell>
                      <TableCell>Akcie</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <CalendarIcon sx={{ mr: 0.5, color: 'text.secondary', fontSize: 16 }} />
                            <Typography variant="body2">
                              {formatDate(transaction.transactionDate)}
                            </Typography>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getTransactionTypeIcon(transaction.debitCredit)}
                            <Typography variant="body2" sx={{ ml: 0.5 }}>
                              {transaction.debitCredit === 'CRDT' ? 'Príjem' : 'Výdaj'}
                            </Typography>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <EuroIcon sx={{ mr: 0.5, color: 'success.main', fontSize: 16 }} />
                            <Typography variant="body2" fontWeight="bold">
                              {formatCurrency(transaction.amount, transaction.currency)}
                            </Typography>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Box>
                            {transaction.counterpartyName && (
                              <Typography variant="body2" fontWeight="medium">
                                {transaction.counterpartyName}
                              </Typography>
                            )}
                            {transaction.counterpartyAccount && (
                              <Typography variant="caption" color="text.secondary" fontFamily="monospace">
                                {transaction.counterpartyAccount}
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Box>
                            {transaction.variableSymbol && (
                              <Typography variant="body2">
                                VS: {transaction.variableSymbol}
                              </Typography>
                            )}
                            {transaction.description && (
                              <Typography variant="caption" color="text.secondary">
                                {transaction.description.substring(0, 50)}
                                {transaction.description.length > 50 ? '...' : ''}
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          {transaction.matchedInvoice ? (
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {transaction.matchedInvoice.invoiceNumber}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {formatClientName(transaction.matchedInvoice.client)}
                              </Typography>
                              <Chip
                                label="Spárované"
                                color="success"
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Box>
                          ) : (
                            <Chip
                              label="Nespárované"
                              color="warning"
                              size="small"
                            />
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {!transaction.matchedInvoice && (
                            <Tooltip title="Spárovať s faktúrou">
                              <IconButton 
                                size="small" 
                                color="primary"
                                onClick={() => handleMatch(transaction)}
                              >
                                <LinkIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={(event, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(event) => {
                  setRowsPerPage(parseInt(event.target.value, 10));
                  setPage(0);
                }}
                rowsPerPageOptions={[10, 25, 50, 100]}
                labelRowsPerPage="Riadkov na stránku:"
                labelDisplayedRows={({ from, to, count }) =>
                  `${from}–${to} z ${count !== -1 ? count : `viac ako ${to}`}`
                }
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Match Dialog */}
      <Dialog open={matchDialogOpen} onClose={() => setMatchDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Spárovať transakciu s faktúrou</DialogTitle>
        <DialogContent>
          {selectedTransaction && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Transakcia
              </Typography>
              <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Dátum:</strong> {formatDate(selectedTransaction.transactionDate)}
                </Typography>
                <Typography variant="body2">
                  <strong>Suma:</strong> {formatCurrency(selectedTransaction.amount, selectedTransaction.currency)}
                </Typography>
                <Typography variant="body2">
                  <strong>VS:</strong> {selectedTransaction.variableSymbol || 'Nie je'}
                </Typography>
                <Typography variant="body2">
                  <strong>Protiúčet:</strong> {selectedTransaction.counterpartyName || 'Neznámy'}
                </Typography>
              </Box>

              <Typography variant="h6" gutterBottom>
                Vybrať faktúru
              </Typography>
              <Autocomplete
                options={availableInvoices}
                getOptionLabel={(option) => 
                  `${option.invoiceNumber} - ${formatClientName(option.client)} - ${formatCurrency(option.totalAmount)}`
                }
                value={selectedInvoice}
                onChange={(event, newValue) => setSelectedInvoice(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Faktúra"
                    placeholder="Vyhľadať faktúru..."
                    fullWidth
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props}>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {option.invoiceNumber} - {formatCurrency(option.totalAmount)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatClientName(option.client)}
                      </Typography>
                    </Box>
                  </li>
                )}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMatchDialogOpen(false)}>
            Zrušiť
          </Button>
          <Button 
            onClick={handleMatchConfirm} 
            variant="contained" 
            disabled={!selectedInvoice}
          >
            Spárovať
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default BankTransactionsList;
