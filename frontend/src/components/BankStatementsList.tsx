import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Button,
  TablePagination,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  AccountBalance as BankIcon,
  Upload as UploadIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  AutoAwesome as AutoMatchIcon,
  GetApp as DownloadIcon,
} from '@mui/icons-material';

interface BankStatement {
  id: number;
  fileName: string;
  bankName: string;
  accountNumber: string;
  statementDate: string;
  openingBalance: number;
  closingBalance: number;
  currency: string;
  totalTransactions: number;
  processed: boolean;
  importedAt: string;
  _count: {
    transactions: number;
  };
}

interface BankStatementsResponse {
  success: boolean;
  data: {
    statements: BankStatement[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export function BankStatementsList() {
  const [statements, setStatements] = useState<BankStatement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  const fetchStatements = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: rowsPerPage.toString(),
      });

      const response = await fetch(`/api/bank/statements?${params}`);
      const result: BankStatementsResponse = await response.json();

      if (result.success) {
        setStatements(result.data.statements);
        setTotalCount(result.data.pagination.total);
      } else {
        setError('Chyba pri načítavaní bankových výpisov');
      }
    } catch (err) {
      setError('Chyba pri komunikácii so serverom');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatements();
  }, [page, rowsPerPage]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch('/api/bank/import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setUploadDialogOpen(false);
        setSelectedFile(null);
        fetchStatements(); // Refresh the list
        alert(`Úspešne importovaných ${result.data.transactionsImported} transakcií`);
      } else {
        alert(`Chyba pri importe: ${result.message}`);
      }
    } catch (error) {
      alert('Chyba pri nahrávaní súboru');
    } finally {
      setUploading(false);
    }
  };

  const handleAutoMatch = async (statementId: number) => {
    try {
      const response = await fetch('/api/bank/auto-match', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ statementId }),
      });

      const result = await response.json();

      if (result.success) {
        alert(`Automaticky spárovaných ${result.data.matchedCount} z ${result.data.totalProcessed} transakcií`);
        fetchStatements(); // Refresh the list
      } else {
        alert(`Chyba pri automatickom spárovaní: ${result.message}`);
      }
    } catch (error) {
      alert('Chyba pri automatickom spárovaní');
    }
  };

  const handleDelete = async (statementId: number) => {
    if (!confirm('Naozaj chcete odstrániť tento bankový výpis a všetky jeho transakcie?')) {
      return;
    }

    try {
      const response = await fetch(`/api/bank/statements/${statementId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        fetchStatements(); // Refresh the list
        alert('Bankový výpis bol úspešne odstránený');
      } else {
        alert(`Chyba pri odstraňovaní: ${result.message}`);
      }
    } catch (error) {
      alert('Chyba pri odstraňovaní bankového výpisu');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sk-SK');
  };

  const formatCurrency = (amount: number, currency: string = 'EUR') => {
    return new Intl.NumberFormat('sk-SK', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h2">
              <BankIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Bankové výpisy ({totalCount})
            </Typography>
            
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={() => setUploadDialogOpen(true)}
            >
              Nahrať výpis
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Súbor</TableCell>
                      <TableCell>Banka</TableCell>
                      <TableCell>Účet</TableCell>
                      <TableCell>Dátum výpisu</TableCell>
                      <TableCell>Počiatočný zostatok</TableCell>
                      <TableCell>Konečný zostatok</TableCell>
                      <TableCell>Transakcie</TableCell>
                      <TableCell>Stav</TableCell>
                      <TableCell>Akcie</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {statements.map((statement) => (
                      <TableRow key={statement.id} hover>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {statement.fileName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Importované: {formatDate(statement.importedAt)}
                            </Typography>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2">
                            {statement.bankName}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {statement.accountNumber}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(statement.statementDate)}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(statement.openingBalance, statement.currency)}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(statement.closingBalance, statement.currency)}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Chip
                            label={statement._count.transactions}
                            color={statement._count.transactions > 0 ? 'primary' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        
                        <TableCell>
                          <Chip
                            label={statement.processed ? 'Spracovaný' : 'Nespracovaný'}
                            color={statement.processed ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                        
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Zobraziť transakcie">
                              <IconButton size="small" color="primary">
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Automatické spárovanie">
                              <IconButton 
                                size="small" 
                                color="secondary"
                                onClick={() => handleAutoMatch(statement.id)}
                              >
                                <AutoMatchIcon />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Odstrániť">
                              <IconButton 
                                size="small" 
                                color="error"
                                onClick={() => handleDelete(statement.id)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={(event, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(event) => {
                  setRowsPerPage(parseInt(event.target.value, 10));
                  setPage(0);
                }}
                rowsPerPageOptions={[10, 25, 50, 100]}
                labelRowsPerPage="Riadkov na stránku:"
                labelDisplayedRows={({ from, to, count }) =>
                  `${from}–${to} z ${count !== -1 ? count : `viac ako ${to}`}`
                }
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Nahrať bankový výpis</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Podporované formáty: XML (CAMT.053), XLS, XLSX, CSV
            </Typography>
            <input
              type="file"
              accept=".xml,.xls,.xlsx,.csv"
              onChange={handleFileSelect}
              style={{ width: '100%', padding: '8px', marginTop: '16px' }}
            />
            {selectedFile && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                Vybraný súbor: {selectedFile.name}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>
            Zrušiť
          </Button>
          <Button 
            onClick={handleUpload} 
            variant="contained" 
            disabled={!selectedFile || uploading}
          >
            {uploading ? <CircularProgress size={20} /> : 'Nahrať'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default BankStatementsList;
