import React, { useState } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Business as BusinessIcon,
  Receipt as ReceiptIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Sync as SyncIcon,
  AccountBalance as BankIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';

// Import pages
import ClientsPage from './pages/ClientsPage';
import InvoicesPage from './pages/InvoicesPage';

// We'll create a simple dashboard component instead of importing the complex one

const drawerWidth = 240;

interface NavigationItem {
  text: string;
  icon: React.ReactElement;
  path: string;
}

const navigationItems: NavigationItem[] = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
  { text: 'Klienti', icon: <BusinessIcon />, path: '/clients' },
  { text: 'Faktúry', icon: <ReceiptIcon />, path: '/invoices' },
  { text: 'Synchronizácia', icon: <SyncIcon />, path: '/sync' },
  { text: 'Bankové výpisy', icon: <BankIcon />, path: '/bank' },
  { text: 'Platby', icon: <PaymentIcon />, path: '/payments' },
  { text: 'Nastavenia', icon: <SettingsIcon />, path: '/settings' },
];

function NavigationDrawer({ 
  open, 
  onClose, 
  variant = 'temporary' 
}: { 
  open: boolean; 
  onClose: () => void; 
  variant?: 'temporary' | 'permanent';
}) {
  const location = useLocation();

  const drawer = (
    <Box>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          UISP-Omega Bridge
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              component={Link}
              to={item.path}
              selected={location.pathname === item.path}
              onClick={variant === 'temporary' ? onClose : undefined}
            >
              <ListItemIcon>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Drawer
      variant={variant}
      open={open}
      onClose={onClose}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      {drawer}
    </Drawer>
  );
}

function DashboardPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" gutterBottom>
        Vitajte v UISP-Omega Bridge aplikácii!
      </Typography>
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Rýchle akcie:
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Link to="/clients" style={{ textDecoration: 'none' }}>
            <Box sx={{
              p: 2,
              border: 1,
              borderColor: 'primary.main',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': { bgcolor: 'primary.light', color: 'white' }
            }}>
              <BusinessIcon sx={{ mr: 1 }} />
              Zobraziť klientov
            </Box>
          </Link>
          <Link to="/invoices" style={{ textDecoration: 'none' }}>
            <Box sx={{
              p: 2,
              border: 1,
              borderColor: 'primary.main',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': { bgcolor: 'primary.light', color: 'white' }
            }}>
              <ReceiptIcon sx={{ mr: 1 }} />
              Zobraziť faktúry
            </Box>
          </Link>
        </Box>
      </Box>
    </Box>
  );
}

function SyncPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Synchronizácia
      </Typography>
      <Typography variant="body1">
        Stránka pre synchronizáciu dát z UISP do lokálnej databázy.
      </Typography>
    </Box>
  );
}

// Bank Tab Components
function BankOverviewTab() {
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        📊 Prehľad bankových operácií
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Celkový prehľad importovaných bankových výpisov a transakcií.
      </Typography>

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2, mb: 3 }}>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
          <Typography variant="h6" color="primary">Celkom výpisov</Typography>
          <Typography variant="h4">0</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
          <Typography variant="h6" color="primary">Celkom transakcií</Typography>
          <Typography variant="h4">0</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
          <Typography variant="h6" color="primary">Spárované platby</Typography>
          <Typography variant="h4">0</Typography>
        </Box>
      </Box>
    </Box>
  );
}

function BankUploadTab() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string>('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
    setUploadStatus('');
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = Array.from(event.dataTransfer.files);
    setSelectedFiles(files);
    setUploadStatus('');
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setUploadStatus('Vyberte súbor na nahranie');
      return;
    }

    setUploadStatus('Nahrávam súbory...');

    // Simulácia uploadu
    setTimeout(() => {
      setUploadStatus(`Úspešne nahraných ${selectedFiles.length} súborov!`);
      setSelectedFiles([]);
    }, 2000);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(files => files.filter((_, i) => i !== index));
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        📁 Nahrať bankový súbor
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Nahrajte bankové výpisy vo formáte XML, XLS alebo CSV z Tatrabanka.
      </Typography>

      {/* File Input */}
      <input
        type="file"
        multiple
        accept=".xml,.xls,.xlsx,.csv"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        id="file-upload"
      />

      {/* Drop Zone */}
      <Box
        sx={{
          border: '2px dashed',
          borderColor: isDragOver ? 'success.main' : 'primary.main',
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          backgroundColor: isDragOver ? 'success.light' : 'grey.50',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: 'primary.light',
            borderColor: 'primary.dark'
          }
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => document.getElementById('file-upload')?.click()}
      >
        <Typography variant="h6" sx={{ mb: 2 }}>
          {isDragOver ? '📂 Pustite súbory sem' : '📁 Pretiahnite súbory sem alebo kliknite pre výber'}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Podporované formáty: .xml, .xls, .xlsx, .csv
        </Typography>

        <label htmlFor="file-upload">
          <Box
            component="span"
            sx={{
              display: 'inline-block',
              px: 3,
              py: 1.5,
              backgroundColor: 'primary.main',
              color: 'white',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'primary.dark'
              }
            }}
          >
            📎 Vybrať súbory
          </Box>
        </label>
      </Box>

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Vybrané súbory ({selectedFiles.length}):
          </Typography>
          {selectedFiles.map((file, index) => (
            <Box key={index} sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              mb: 1
            }}>
              <Box>
                <Typography variant="body1">{file.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {(file.size / 1024).toFixed(1)} KB
                </Typography>
              </Box>
              <Box
                onClick={() => removeFile(index)}
                sx={{
                  cursor: 'pointer',
                  color: 'error.main',
                  '&:hover': { color: 'error.dark' }
                }}
              >
                ❌
              </Box>
            </Box>
          ))}

          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <Box
              onClick={handleUpload}
              sx={{
                px: 3,
                py: 1.5,
                backgroundColor: 'success.main',
                color: 'white',
                borderRadius: 1,
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'success.dark'
                }
              }}
            >
              🚀 Nahrať súbory
            </Box>
            <Box
              onClick={() => setSelectedFiles([])}
              sx={{
                px: 3,
                py: 1.5,
                backgroundColor: 'grey.500',
                color: 'white',
                borderRadius: 1,
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'grey.700'
                }
              }}
            >
              🗑️ Zrušiť
            </Box>
          </Box>
        </Box>
      )}

      {/* Upload Status */}
      {uploadStatus && (
        <Box sx={{ mt: 3 }}>
          <Typography
            variant="body1"
            color={uploadStatus.includes('Úspešne') ? 'success.main' : 'info.main'}
            sx={{ fontWeight: 'bold' }}
          >
            {uploadStatus}
          </Typography>
        </Box>
      )}
    </Box>
  );
}

function BankStatementsTab() {
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        📊 Bankové výpisy
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Zoznam všetkých importovaných bankových výpisov.
      </Typography>

      <Box sx={{ p: 3, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          Žiadne bankové výpisy neboli zatiaľ importované.
        </Typography>
      </Box>
    </Box>
  );
}

function BankTransactionsTab() {
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        💰 Bankové transakcie
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Zoznam všetkých bankových transakcií a ich stav spárovania.
      </Typography>

      <Box sx={{ p: 3, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          Žiadne transakcie neboli zatiaľ importované.
        </Typography>
      </Box>
    </Box>
  );
}

function BankPageComponent() {
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    { label: '🏠 Prehľad', content: <BankOverviewTab /> },
    { label: '📁 Nahrať súbor', content: <BankUploadTab /> },
    { label: '📊 Výpisy', content: <BankStatementsTab /> },
    { label: '💰 Transakcie', content: <BankTransactionsTab /> },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🏦 Bankové výpisy
      </Typography>

      {/* Tab Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {tabs.map((tab, index) => (
            <Box
              key={index}
              onClick={() => setActiveTab(index)}
              sx={{
                px: 3,
                py: 1.5,
                cursor: 'pointer',
                borderBottom: activeTab === index ? 2 : 0,
                borderColor: 'primary.main',
                backgroundColor: activeTab === index ? 'primary.light' : 'transparent',
                color: activeTab === index ? 'primary.contrastText' : 'text.primary',
                '&:hover': {
                  backgroundColor: activeTab === index ? 'primary.light' : 'grey.100',
                },
              }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                {tab.label}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Tab Content */}
      <Box>
        {tabs[activeTab].content}
      </Box>
    </Box>
  );
}

function PaymentsPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Platby
      </Typography>
      <Typography variant="body1">
        Stránka pre párovanie platieb s faktúrami.
      </Typography>
    </Box>
  );
}

function SettingsPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Nastavenia
      </Typography>
      <Typography variant="body1">
        Stránka pre konfiguráciu aplikácie.
      </Typography>
    </Box>
  );
}

function AppWithNavigation() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Box sx={{ display: 'flex' }}>
        <AppBar
          position="fixed"
          sx={{
            width: { md: `calc(100% - ${drawerWidth}px)` },
            ml: { md: `${drawerWidth}px` },
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div">
              UISP-Omega Bridge
            </Typography>
          </Toolbar>
        </AppBar>

        {/* Mobile drawer */}
        <NavigationDrawer
          open={mobileOpen}
          onClose={handleDrawerToggle}
          variant="temporary"
        />

        {/* Desktop drawer */}
        <NavigationDrawer
          open={true}
          onClose={() => {}}
          variant="permanent"
        />

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { md: `calc(100% - ${drawerWidth}px)` },
          }}
        >
          <Toolbar />
          <Routes>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/clients" element={<ClientsPage />} />
            <Route path="/invoices" element={<InvoicesPage />} />
            <Route path="/sync" element={<SyncPage />} />
            <Route path="/bank" element={<BankPageComponent />} />
            <Route path="/payments" element={<PaymentsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </Box>
      </Box>
  );
}

export default AppWithNavigation;
