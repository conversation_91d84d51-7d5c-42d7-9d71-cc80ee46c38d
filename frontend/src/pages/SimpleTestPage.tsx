import React, { useEffect, useState } from 'react';
import { Box, Typography, Button, Alert } from '@mui/material';

export function SimpleTestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    try {
      console.log('Making API call to http://localhost:3001/api/bank/statements');
      const response = await fetch('http://localhost:3001/api/bank/statements');
      const data = await response.json();
      console.log('API Response:', data);
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('API Error:', error);
      setResult('Error: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('SimpleTestPage mounted');
    testAPI();
  }, []);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Simple API Test
      </Typography>
      
      <Button 
        variant="contained" 
        onClick={testAPI}
        disabled={loading}
        sx={{ mb: 3 }}
      >
        {loading ? 'Loading...' : 'Test API'}
      </Button>

      {result && (
        <Alert severity="info">
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {result}
          </pre>
        </Alert>
      )}
    </Box>
  );
}
