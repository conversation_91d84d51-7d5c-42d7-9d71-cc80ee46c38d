import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  AccountBalance as BankIcon,
  Receipt as TransactionIcon,
  Assessment as StatsIcon,
} from '@mui/icons-material';

function SimpleBankOverview() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Bankové výpisy - Prehľad
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        Vitajte v sekcii bankových výpisov. Tu môžete spravovať importy z Tatrabanka.
      </Alert>

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📁 Nahrať súbor
            </Typography>
            <Typography variant="body2" paragraph>
              Importujte bankový výpis vo formáte XML, XLS alebo CSV
            </Typography>
            <Button variant="contained" href="/bank/upload">
              Nahrať súbor
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📊 Výpisy
            </Typography>
            <Typography variant="body2" paragraph>
              Zobraziť všetky importované bankové výpisy
            </Typography>
            <Button variant="outlined" href="/bank/statements">
              Zobraziť výpisy
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              💰 Transakcie
            </Typography>
            <Typography variant="body2" paragraph>
              Spravovať bankové transakcie a spárovanie
            </Typography>
            <Button variant="outlined" href="/bank/transactions">
              Zobraziť transakcie
            </Button>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
}

function SimpleBankUpload() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Nahrať bankový súbor
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        Podporované formáty: XML (CAMT.053), XLS, XLSX, CSV
      </Alert>

      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <UploadIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Presuňte súbor sem alebo kliknite pre výber
          </Typography>
          <Button variant="contained" component="label">
            Vybrať súbor
            <input type="file" hidden accept=".xml,.xls,.xlsx,.csv" />
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
}

function SimpleBankStatements() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Bankové výpisy
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        Tu sa zobrazia všetky importované bankové výpisy
      </Alert>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Zoznam výpisov
          </Typography>
          <Typography color="text.secondary">
            Zatiaľ neboli importované žiadne výpisy.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}

function SimpleBankTransactions() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Bankové transakcie
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        Tu sa zobrazia všetky bankové transakcie
      </Alert>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Zoznam transakcií
          </Typography>
          <Typography color="text.secondary">
            Zatiaľ neboli importované žiadne transakcie.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}

export function SimpleBankPage() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const tabs = [
    { label: 'Prehľad', value: '/bank', icon: <BankIcon /> },
    { label: 'Nahrať súbor', value: '/bank/upload', icon: <UploadIcon /> },
    { label: 'Výpisy', value: '/bank/statements', icon: <StatsIcon /> },
    { label: 'Transakcie', value: '/bank/transactions', icon: <TransactionIcon /> },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/bank' && location.pathname === '/bank')
  )?.value || '/bank';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box>
      <Typography variant="h3" gutterBottom>
        🏦 Bankové operácie
      </Typography>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      <Routes>
        <Route path="/" element={<SimpleBankOverview />} />
        <Route path="/upload" element={<SimpleBankUpload />} />
        <Route path="/statements" element={<SimpleBankStatements />} />
        <Route path="/transactions" element={<SimpleBankTransactions />} />
      </Routes>
    </Box>
  );
}
