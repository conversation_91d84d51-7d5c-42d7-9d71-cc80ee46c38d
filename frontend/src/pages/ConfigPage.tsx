import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Divider,
  Chip,
  Grid,
} from '@mui/material';
import {
  Save as SaveIcon,
  TestTube as TestIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';

import { api } from '@/lib/api';
import type { UispCredentials, OmegaConfig, SyncConfig } from '@/types/api';

function UispConfiguration() {
  const queryClient = useQueryClient();
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const { data: config } = useQuery({
    queryKey: ['config'],
    queryFn: () => api.getConfig(),
  });

  const { control, handleSubmit, formState: { errors } } = useForm<UispCredentials>({
    defaultValues: {
      apiUrl: '',
      apiKey: '',
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: UispCredentials) => api.updateUispConfig(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['config'] });
    },
  });

  const testMutation = useMutation({
    mutationFn: () => api.testUispConfig(),
    onSuccess: (data) => {
      setTestResult({ success: true, message: data.message || 'Connection successful' });
    },
    onError: (error: any) => {
      setTestResult({ success: false, message: error.response?.data?.error || 'Connection failed' });
    },
  });

  const onSubmit = (data: UispCredentials) => {
    updateMutation.mutate(data);
  };

  const handleTest = () => {
    testMutation.mutate();
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">UISP Configuration</Typography>
          <Chip
            icon={config?.uisp.isConfigured ? <CheckCircleIcon /> : <WarningIcon />}
            label={config?.uisp.isConfigured ? 'Configured' : 'Not Configured'}
            color={config?.uisp.isConfigured ? 'success' : 'error'}
          />
        </Box>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Controller
                name="apiUrl"
                control={control}
                rules={{ 
                  required: 'API URL is required',
                  pattern: {
                    value: /^https?:\/\/.+/,
                    message: 'Please enter a valid URL',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="UISP API URL"
                    placeholder="https://your-uisp-instance.com"
                    error={!!errors.apiUrl}
                    helperText={errors.apiUrl?.message}
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Controller
                name="apiKey"
                control={control}
                rules={{ 
                  required: 'API Key is required',
                  minLength: {
                    value: 10,
                    message: 'API Key must be at least 10 characters',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="UISP API Key"
                    type="password"
                    placeholder="Enter your UISP API key"
                    error={!!errors.apiKey}
                    helperText={errors.apiKey?.message}
                  />
                )}
              />
            </Grid>
          </Grid>

          {testResult && (
            <Alert 
              severity={testResult.success ? 'success' : 'error'} 
              sx={{ mt: 2 }}
              onClose={() => setTestResult(null)}
            >
              {testResult.message}
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
            <Button
              type="submit"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<TestIcon />}
              onClick={handleTest}
              disabled={testMutation.isPending || !config?.uisp.hasApiUrl || !config?.uisp.hasApiKey}
            >
              {testMutation.isPending ? 'Testing...' : 'Test Connection'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

function OmegaConfiguration() {
  const queryClient = useQueryClient();
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const { data: config } = useQuery({
    queryKey: ['config'],
    queryFn: () => api.getConfig(),
  });

  const { control, handleSubmit, formState: { errors } } = useForm<OmegaConfig>({
    defaultValues: {
      importPath: config?.omega.importPath || '',
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: OmegaConfig) => api.updateOmegaConfig(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['config'] });
    },
  });

  const testMutation = useMutation({
    mutationFn: () => api.testOmegaConfig(),
    onSuccess: (data) => {
      setTestResult({ success: true, message: data.message || 'Path is accessible and writable' });
    },
    onError: (error: any) => {
      setTestResult({ success: false, message: error.response?.data?.error || 'Path test failed' });
    },
  });

  const onSubmit = (data: OmegaConfig) => {
    updateMutation.mutate(data);
  };

  const handleTest = () => {
    testMutation.mutate();
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Omega Configuration</Typography>
          <Chip
            icon={config?.omega.hasImportPath ? <CheckCircleIcon /> : <WarningIcon />}
            label={config?.omega.hasImportPath ? 'Configured' : 'Not Configured'}
            color={config?.omega.hasImportPath ? 'success' : 'error'}
          />
        </Box>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Controller
            name="importPath"
            control={control}
            rules={{ required: 'Import path is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Omega Import Path"
                placeholder="/path/to/omega/import/folder"
                error={!!errors.importPath}
                helperText={errors.importPath?.message || 'Path where CSV files will be saved for Omega import'}
                sx={{ mb: 2 }}
              />
            )}
          />

          {testResult && (
            <Alert 
              severity={testResult.success ? 'success' : 'error'} 
              sx={{ mb: 2 }}
              onClose={() => setTestResult(null)}
            >
              {testResult.message}
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              type="submit"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<TestIcon />}
              onClick={handleTest}
              disabled={testMutation.isPending || !config?.omega.hasImportPath}
            >
              {testMutation.isPending ? 'Testing...' : 'Test Path'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

function SyncConfiguration() {
  const queryClient = useQueryClient();

  const { data: config } = useQuery({
    queryKey: ['config'],
    queryFn: () => api.getConfig(),
  });

  const { control, handleSubmit } = useForm<SyncConfig>({
    defaultValues: {
      autoSyncEnabled: config?.sync.autoSyncEnabled || false,
      syncIntervalMinutes: config?.sync.syncIntervalMinutes || 60,
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: SyncConfig) => api.updateSyncConfig(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['config'] });
    },
  });

  const onSubmit = (data: SyncConfig) => {
    updateMutation.mutate(data);
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Sync Configuration
        </Typography>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Controller
                name="autoSyncEnabled"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={field.value}
                        onChange={field.onChange}
                      />
                    }
                    label="Enable automatic synchronization"
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Controller
                name="syncIntervalMinutes"
                control={control}
                rules={{ 
                  required: 'Sync interval is required',
                  min: { value: 5, message: 'Minimum interval is 5 minutes' },
                  max: { value: 1440, message: 'Maximum interval is 24 hours' },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Sync Interval (minutes)"
                    type="number"
                    inputProps={{ min: 5, max: 1440 }}
                    helperText="How often to sync data automatically (5-1440 minutes)"
                  />
                )}
              />
            </Grid>
          </Grid>

          <Button
            type="submit"
            variant="contained"
            startIcon={<SaveIcon />}
            disabled={updateMutation.isPending}
            sx={{ mt: 2 }}
          >
            {updateMutation.isPending ? 'Saving...' : 'Save'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}

export function ConfigPage() {
  const { data: systemInfo, isLoading } = useQuery({
    queryKey: ['system-info'],
    queryFn: () => api.getSystemInfo(),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Configuration
      </Typography>

      <UispConfiguration />
      <OmegaConfiguration />
      <SyncConfiguration />

      {/* System Information */}
      {systemInfo && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              System Information
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Node.js Version
                </Typography>
                <Typography>{systemInfo.node.version}</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Platform
                </Typography>
                <Typography>{systemInfo.node.platform} ({systemInfo.node.arch})</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Environment
                </Typography>
                <Typography>{systemInfo.environment}</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Uptime
                </Typography>
                <Typography>{Math.floor(systemInfo.uptime / 3600)}h {Math.floor((systemInfo.uptime % 3600) / 60)}m</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Memory Usage
                </Typography>
                <Typography>{(systemInfo.memory.heapUsed / 1024 / 1024).toFixed(1)} MB</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Database
                </Typography>
                <Chip
                  label={systemInfo.database.connected ? 'Connected' : 'Disconnected'}
                  color={systemInfo.database.connected ? 'success' : 'error'}
                  size="small"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}
