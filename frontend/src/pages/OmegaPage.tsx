import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  CircularProgress,
  Chip,
  Alert,
} from '@mui/material';
import {
  CloudUpload as ExportIcon,
  Receipt as InvoiceIcon,
  Payment as PaymentIcon,
  Folder as FilesIcon,
  Assessment as StatsIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { api } from '@/lib/api';

function OmegaOverview() {
  const queryClient = useQueryClient();
  
  const { data: stats, isLoading } = useQuery({
    queryKey: ['omega-stats'],
    queryFn: () => api.getOmegaStats(),
  });

  const { data: validation } = useQuery({
    queryKey: ['omega-validation'],
    queryFn: () => api.validateOmegaConfig(),
  });

  const batchExportMutation = useMutation({
    mutationFn: (options: any) => api.batchExport(options),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['omega-stats'] });
    },
  });

  const handleBatchExport = () => {
    batchExportMutation.mutate({
      exportInvoices: true,
      exportPayments: true,
      cleanupOldFiles: true,
    });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Omega Export Overview
      </Typography>

      {/* Validation Status */}
      {validation && !validation.isValid && (
        <Alert severity="error" sx={{ mb: 3 }}>
          Omega export is not properly configured. Please check the configuration.
        </Alert>
      )}

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Export Statistics</Typography>
            <Chip
              label={validation?.isValid ? 'Ready' : 'Not Configured'}
              color={validation?.isValid ? 'success' : 'error'}
            />
          </Box>
          
          {stats && (
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
              <Box>
                <Typography variant="h4">{stats.totalExports}</Typography>
                <Typography color="text.secondary">Total Exports</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{stats.exportsByType.invoices || 0}</Typography>
                <Typography color="text.secondary">Invoice Exports</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{stats.exportsByType.payments || 0}</Typography>
                <Typography color="text.secondary">Payment Exports</Typography>
              </Box>
            </Box>
          )}

          <Button
            variant="contained"
            startIcon={<ExportIcon />}
            onClick={handleBatchExport}
            disabled={!validation?.isValid || batchExportMutation.isPending}
          >
            {batchExportMutation.isPending ? 'Exporting...' : 'Batch Export'}
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
}

function OmegaInvoices() {
  const { data: preview, isLoading } = useQuery({
    queryKey: ['invoice-export-preview'],
    queryFn: () => api.getInvoiceExportPreview(),
  });

  const exportMutation = useMutation({
    mutationFn: (options: any) => api.exportInvoices(options),
  });

  const handleExport = () => {
    exportMutation.mutate({ onlyUnexported: true });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Invoice Export
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Export Preview
          </Typography>
          
          {preview && (
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
              <Box>
                <Typography variant="h4">{preview.invoiceCount}</Typography>
                <Typography color="text.secondary">Invoices to Export</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{preview.recordCount}</Typography>
                <Typography color="text.secondary">Total Records</Typography>
              </Box>
              <Box>
                <Typography variant="h4">€{preview.totalAmount?.toFixed(2) || '0.00'}</Typography>
                <Typography color="text.secondary">Total Amount</Typography>
              </Box>
            </Box>
          )}

          <Button
            variant="contained"
            startIcon={<InvoiceIcon />}
            onClick={handleExport}
            disabled={exportMutation.isPending || !preview?.invoiceCount}
          >
            {exportMutation.isPending ? 'Exporting...' : 'Export Invoices'}
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
}

function OmegaPayments() {
  const { data: preview, isLoading } = useQuery({
    queryKey: ['payment-export-preview'],
    queryFn: () => api.getPaymentExportPreview(),
  });

  const exportMutation = useMutation({
    mutationFn: (options: any) => api.exportPayments(options),
  });

  const handleExport = () => {
    exportMutation.mutate({ onlyUnexported: true });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Payment Export
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Export Preview
          </Typography>
          
          {preview && (
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
              <Box>
                <Typography variant="h4">{preview.paymentCount}</Typography>
                <Typography color="text.secondary">Payments to Export</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{preview.invoiceCount}</Typography>
                <Typography color="text.secondary">Related Invoices</Typography>
              </Box>
              <Box>
                <Typography variant="h4">€{preview.totalAmount?.toFixed(2) || '0.00'}</Typography>
                <Typography color="text.secondary">Total Amount</Typography>
              </Box>
            </Box>
          )}

          <Button
            variant="contained"
            startIcon={<PaymentIcon />}
            onClick={handleExport}
            disabled={exportMutation.isPending || !preview?.paymentCount}
          >
            {exportMutation.isPending ? 'Exporting...' : 'Export Payments'}
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
}

function OmegaFiles() {
  const { data: files, isLoading } = useQuery({
    queryKey: ['export-files'],
    queryFn: () => api.getExportFiles(),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Export Files
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            Found {files?.length || 0} export files
          </Typography>
          {/* TODO: Add files table */}
        </CardContent>
      </Card>
    </Box>
  );
}

export function OmegaPage() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const tabs = [
    { label: 'Overview', value: '/omega', icon: <StatsIcon /> },
    { label: 'Invoices', value: '/omega/invoices', icon: <InvoiceIcon /> },
    { label: 'Payments', value: '/omega/payments', icon: <PaymentIcon /> },
    { label: 'Files', value: '/omega/files', icon: <FilesIcon /> },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/omega' && location.pathname === '/omega')
  )?.value || '/omega';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      <Routes>
        <Route path="/" element={<OmegaOverview />} />
        <Route path="/invoices" element={<OmegaInvoices />} />
        <Route path="/payments" element={<OmegaPayments />} />
        <Route path="/files" element={<OmegaFiles />} />
      </Routes>
    </Box>
  );
}
