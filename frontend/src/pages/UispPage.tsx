import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Sync as SyncIcon,
  Business as BusinessIcon,
  Receipt as InvoiceIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { api } from '@/lib/api';

function UispOverview() {
  const queryClient = useQueryClient();
  
  const { data: status, isLoading: statusLoading } = useQuery({
    queryKey: ['uisp-status'],
    queryFn: () => api.getUispStatus(),
  });

  const { data: syncStats, isLoading: statsLoading } = useQuery({
    queryKey: ['sync-stats'],
    queryFn: () => api.getSyncStats(),
  });

  const syncMutation = useMutation({
    mutationFn: (options: any) => api.syncInvoices(options),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sync-stats'] });
    },
  });

  const handleSync = () => {
    syncMutation.mutate({ dryRun: false });
  };

  const handleTestConnection = async () => {
    try {
      await api.testUispConnection();
      queryClient.invalidateQueries({ queryKey: ['uisp-status'] });
    } catch (error) {
      console.error('Connection test failed:', error);
    }
  };

  if (statusLoading || statsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        UISP Integration Overview
      </Typography>

      {/* Connection Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Connection Status</Typography>
            <Chip
              icon={<BusinessIcon />}
              label={status?.connected ? 'Connected' : 'Disconnected'}
              color={status?.connected ? 'success' : 'error'}
            />
          </Box>
          
          {!status?.apiStatus?.isConfigured && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              UISP API is not configured. Please configure the API credentials in settings.
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handleTestConnection}
              disabled={!status?.apiStatus?.isConfigured}
            >
              Test Connection
            </Button>
            <Button
              variant="contained"
              startIcon={<SyncIcon />}
              onClick={handleSync}
              disabled={!status?.connected || syncMutation.isPending}
            >
              {syncMutation.isPending ? 'Syncing...' : 'Sync Invoices'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Sync Statistics */}
      {syncStats && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Sync Statistics
            </Typography>
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
              <Box>
                <Typography variant="h4">{syncStats.totalInvoices || 0}</Typography>
                <Typography color="text.secondary">Total Invoices</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{syncStats.syncedInvoices || 0}</Typography>
                <Typography color="text.secondary">Synced Invoices</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{syncStats.lastSyncDate ? 'Recent' : 'Never'}</Typography>
                <Typography color="text.secondary">Last Sync</Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}

function UispInvoices() {
  const { data: invoices, isLoading } = useQuery({
    queryKey: ['uisp-invoices'],
    queryFn: () => api.getUispInvoices({ limit: 50 }),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        UISP Invoices
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            Found {invoices?.length || 0} invoices
          </Typography>
          {/* TODO: Add invoice table */}
        </CardContent>
      </Card>
    </Box>
  );
}

function UispClients() {
  const { data: clients, isLoading } = useQuery({
    queryKey: ['uisp-clients'],
    queryFn: () => api.getUispClients({ limit: 50 }),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        UISP Clients
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            Found {clients?.length || 0} clients
          </Typography>
          {/* TODO: Add client table */}
        </CardContent>
      </Card>
    </Box>
  );
}

function UispSettings() {
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        UISP Settings
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            UISP API configuration and settings will be available here.
          </Typography>
          {/* TODO: Add settings form */}
        </CardContent>
      </Card>
    </Box>
  );
}

export function UispPage() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const tabs = [
    { label: 'Overview', value: '/uisp', icon: <BusinessIcon /> },
    { label: 'Invoices', value: '/uisp/invoices', icon: <InvoiceIcon /> },
    { label: 'Clients', value: '/uisp/clients', icon: <PeopleIcon /> },
    { label: 'Settings', value: '/uisp/settings', icon: <SettingsIcon /> },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/uisp' && location.pathname === '/uisp')
  )?.value || '/uisp';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      <Routes>
        <Route path="/" element={<UispOverview />} />
        <Route path="/invoices" element={<UispInvoices />} />
        <Route path="/clients" element={<UispClients />} />
        <Route path="/settings" element={<UispSettings />} />
      </Routes>
    </Box>
  );
}
