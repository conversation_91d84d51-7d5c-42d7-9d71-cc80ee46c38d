import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Payment as PaymentIcon,
  AutoAwesome as MatchIcon,
  List as ListIcon,
  Assessment as StatsIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { api } from '@/lib/api';

function PaymentOverview() {
  const queryClient = useQueryClient();
  
  const { data: stats, isLoading } = useQuery({
    queryKey: ['matching-stats'],
    queryFn: () => api.getMatchingStats(),
  });

  const matchMutation = useMutation({
    mutationFn: (options: any) => api.matchPayments(options),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['matching-stats'] });
    },
  });

  const handleAutoMatch = () => {
    matchMutation.mutate({ dryRun: false });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Payment Matching Overview
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Matching Statistics</Typography>
            <Chip
              label={`${stats?.matchingRate?.toFixed(1) || 0}% Match Rate`}
              color={stats && stats.matchingRate > 80 ? 'success' : 'warning'}
            />
          </Box>
          
          {stats && (
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
              <Box>
                <Typography variant="h4">{stats.totalTransactions}</Typography>
                <Typography color="text.secondary">Total Transactions</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{stats.matchedTransactions}</Typography>
                <Typography color="text.secondary">Matched</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{stats.unmatchedTransactions}</Typography>
                <Typography color="text.secondary">Unmatched</Typography>
              </Box>
            </Box>
          )}

          <Button
            variant="contained"
            startIcon={<MatchIcon />}
            onClick={handleAutoMatch}
            disabled={matchMutation.isPending}
          >
            {matchMutation.isPending ? 'Matching...' : 'Run Auto Match'}
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
}

function PaymentMatching() {
  const { data: unmatched, isLoading } = useQuery({
    queryKey: ['unmatched-transactions'],
    queryFn: () => api.getUnmatchedTransactions(),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Payment Matching
      </Typography>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Unmatched Transactions
          </Typography>
          <Typography>
            Found {unmatched?.length || 0} unmatched transactions
          </Typography>
          {/* TODO: Add matching interface */}
        </CardContent>
      </Card>
    </Box>
  );
}

function PaymentList() {
  const { data: payments, isLoading } = useQuery({
    queryKey: ['payments'],
    queryFn: () => api.getPayments({ limit: 50 }),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Payment List
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            Found {payments?.length || 0} payments
          </Typography>
          {/* TODO: Add payments table */}
        </CardContent>
      </Card>
    </Box>
  );
}

export function PaymentPage() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const tabs = [
    { label: 'Overview', value: '/payment', icon: <StatsIcon /> },
    { label: 'Matching', value: '/payment/matching', icon: <MatchIcon /> },
    { label: 'Payments', value: '/payment/list', icon: <ListIcon /> },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/payment' && location.pathname === '/payment')
  )?.value || '/payment';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      <Routes>
        <Route path="/" element={<PaymentOverview />} />
        <Route path="/matching" element={<PaymentMatching />} />
        <Route path="/list" element={<PaymentList />} />
      </Routes>
    </Box>
  );
}
