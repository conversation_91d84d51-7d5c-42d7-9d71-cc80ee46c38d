import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
} from '@mui/material';

function TestOverview() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        ✅ PREHĽAD FUNGUJE!
      </Typography>
      <Alert severity="success">
        Toto je prehľad bankových operácií
      </Alert>
    </Box>
  );
}

function TestUpload() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        ✅ UPLOAD FUNGUJE!
      </Typography>
      <Alert severity="info">
        Tu môžete nahrať bankové súbory
      </Alert>
    </Box>
  );
}

function TestStatements() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        ✅ VÝPISY FUNGUJÚ!
      </Typography>
      <Alert severity="warning">
        Tu sa zobrazia bankové výpisy
      </Alert>
    </Box>
  );
}

function TestTransactions() {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        ✅ TRANSAKCIE FUNGUJÚ!
      </Typography>
      <Alert severity="error">
        Tu sa zobrazia bankové transakcie
      </Alert>
    </Box>
  );
}

export function TestBankMenu() {
  const navigate = useNavigate();
  const location = useLocation();
  
  console.log('🏦 TestBankMenu rendered!');
  console.log('📍 Current location:', location.pathname);
  
  const tabs = [
    { label: '🏠 Prehľad', value: '/bank' },
    { label: '📁 Upload', value: '/bank/upload' },
    { label: '📊 Výpisy', value: '/bank/statements' },
    { label: '💰 Transakcie', value: '/bank/transactions' },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/bank' && location.pathname === '/bank')
  )?.value || '/bank';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    console.log('🔄 Tab change to:', newValue);
    navigate(newValue);
  };

  return (
    <Box>
      <Typography variant="h2" gutterBottom sx={{ color: 'primary.main' }}>
        🏦 TEST BANKOVÉ MENU
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <strong>Debug info:</strong><br/>
        Aktuálna cesta: {location.pathname}<br/>
        Vybraný tab: {currentTab}
      </Alert>
      
      <Box sx={{ borderBottom: 2, borderColor: 'primary.main', mb: 3 }}>
        <Tabs 
          value={currentTab} 
          onChange={handleTabChange}
          textColor="primary"
          indicatorColor="primary"
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              sx={{ fontSize: '1.1rem', fontWeight: 'bold' }}
            />
          ))}
        </Tabs>
      </Box>

      <Box sx={{ p: 2, border: '2px solid', borderColor: 'secondary.main', borderRadius: 2 }}>
        <Routes>
          <Route path="/" element={<TestOverview />} />
          <Route path="/upload" element={<TestUpload />} />
          <Route path="/statements" element={<TestStatements />} />
          <Route path="/transactions" element={<TestTransactions />} />
        </Routes>
      </Box>
    </Box>
  );
}
