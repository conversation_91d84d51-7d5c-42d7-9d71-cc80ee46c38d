import React, { useState } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  InputAdornment
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';

// Mock data
const mockClients = [
  { id: 1, name: '<PERSON> AB-STAV', email: '<EMAIL>', city: 'Veľký Cetín', isActive: true, invoiceCount: 5 },
  { id: 2, name: 'Atlantis - dovolenka s.r.o.', email: '<EMAIL>', city: 'Malý Cetín', isActive: false, invoiceCount: 2 },
  { id: 3, name: 'BETA ELEKTRO s.r.o.', email: '<EMAIL>', city: 'Veľký Cetín', isActive: true, invoiceCount: 8 },
  { id: 4, name: 'DANNY-K s.r.o.', email: '<EMAIL>', city: '<PERSON>ý Cetín', isActive: true, invoiceCount: 3 },
  { id: 5, name: 'Ecaza, s.r.o.', email: '<EMAIL>', city: 'Nitra', isActive: false, invoiceCount: 1 },
];

export default function ClientsPage() {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredClients = mockClients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.city.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        👥 Klienti z UISP
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Zoznam všetkých klientov importovaných z UISP systému.
      </Typography>

      {/* Search */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder="Hľadať klientov..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {/* Stats */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="primary">{mockClients.length}</Typography>
          <Typography variant="body2">Celkom klientov</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="success.main">{mockClients.filter(c => c.isActive).length}</Typography>
          <Typography variant="body2">Aktívni klienti</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="info.main">{mockClients.reduce((sum, c) => sum + c.invoiceCount, 0)}</Typography>
          <Typography variant="body2">Celkom faktúr</Typography>
        </Box>
      </Box>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>ID</strong></TableCell>
              <TableCell><strong>Názov</strong></TableCell>
              <TableCell><strong>Email</strong></TableCell>
              <TableCell><strong>Mesto</strong></TableCell>
              <TableCell><strong>Status</strong></TableCell>
              <TableCell><strong>Faktúry</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredClients.map((client) => (
              <TableRow key={client.id} hover>
                <TableCell>{client.id}</TableCell>
                <TableCell>
                  <Typography variant="body1" fontWeight="bold">
                    {client.name}
                  </Typography>
                </TableCell>
                <TableCell>{client.email}</TableCell>
                <TableCell>{client.city}</TableCell>
                <TableCell>
                  <Chip
                    label={client.isActive ? 'Aktívny' : 'Neaktívny'}
                    color={client.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={client.invoiceCount}
                    color="info"
                    size="small"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredClients.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            Žiadni klienti neboli nájdení pre hľadaný výraz "{searchTerm}"
          </Typography>
        </Box>
      )}
    </Box>
  );
}
