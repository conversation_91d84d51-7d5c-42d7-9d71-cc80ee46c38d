import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  AccountBalance as BankIcon,
  Receipt as TransactionIcon,
  Assessment as StatsIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';

import { api } from '@/lib/api';

function BankOverview() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['bank-stats'],
    queryFn: () => api.getBankImportStats(),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Bank Import Overview
      </Typography>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Import Statistics
          </Typography>
          
          {stats && (
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
              <Box>
                <Typography variant="h4">{stats.totalStatements}</Typography>
                <Typography color="text.secondary">Bank Statements</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{stats.totalTransactions}</Typography>
                <Typography color="text.secondary">Transactions</Typography>
              </Box>
              <Box>
                <Typography variant="h4">{stats.processedToday}</Typography>
                <Typography color="text.secondary">Processed Today</Typography>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
}

function BankUpload() {
  const [dragOver, setDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { data: supportedTypes } = useQuery({
    queryKey: ['supported-file-types'],
    queryFn: () => api.getSupportedFileTypes(),
  });

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Upload Bank Statement
      </Typography>

      {/* Supported File Types */}
      {supportedTypes && (
        <Alert severity="info" sx={{ mb: 3 }}>
          Supported file types: {supportedTypes.map((type: any) => type.extensions.join(', ')).join(', ')}
        </Alert>
      )}

      {/* File Upload Area */}
      <Card
        sx={{
          mb: 3,
          border: dragOver ? '2px dashed' : '2px solid',
          borderColor: dragOver ? 'primary.main' : 'divider',
          bgcolor: dragOver ? 'action.hover' : 'background.paper',
          cursor: 'pointer',
          transition: 'all 0.2s',
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <UploadIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Drop your bank statement file here
          </Typography>
          <Typography color="text.secondary" paragraph>
            or click to select a file
          </Typography>
          <input
            type="file"
            hidden
            id="file-upload"
            onChange={handleFileSelect}
            accept=".xml,.sta,.swift,.xls,.xlsx"
          />
          <label htmlFor="file-upload">
            <Button variant="contained" component="span">
              Select File
            </Button>
          </label>
        </CardContent>
      </Card>

      {/* Selected File */}
      {selectedFile && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Selected File
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="body1">{selectedFile.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button variant="outlined">
                  Preview
                </Button>
                <Button variant="contained">
                  Upload
                </Button>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}

function BankStatements() {
  const { data: statements, isLoading } = useQuery({
    queryKey: ['bank-statements'],
    queryFn: () => api.getBankStatements({ limit: 50 }),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Bank Statements
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            Found {statements?.length || 0} bank statements
          </Typography>
          {/* TODO: Add statements table */}
        </CardContent>
      </Card>
    </Box>
  );
}

function BankTransactions() {
  const { data: transactions, isLoading } = useQuery({
    queryKey: ['bank-transactions'],
    queryFn: () => api.getBankTransactions({ limit: 50 }),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Bank Transactions
      </Typography>
      
      <Card>
        <CardContent>
          <Typography>
            Found {transactions?.length || 0} transactions
          </Typography>
          {/* TODO: Add transactions table */}
        </CardContent>
      </Card>
    </Box>
  );
}

export function BankPage() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const tabs = [
    { label: 'Overview', value: '/bank', icon: <BankIcon /> },
    { label: 'Upload', value: '/bank/upload', icon: <UploadIcon /> },
    { label: 'Statements', value: '/bank/statements', icon: <StatsIcon /> },
    { label: 'Transactions', value: '/bank/transactions', icon: <TransactionIcon /> },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/bank' && location.pathname === '/bank')
  )?.value || '/bank';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      <Routes>
        <Route path="/" element={<BankOverview />} />
        <Route path="/upload" element={<BankUpload />} />
        <Route path="/statements" element={<BankStatements />} />
        <Route path="/transactions" element={<BankTransactions />} />
      </Routes>
    </Box>
  );
}
