import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  AccountBalance as BankIcon,
  Receipt as TransactionIcon,
  Assessment as StatsIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';

import { api } from '@/lib/api';
import BankStatementsList from '../components/BankStatementsList';
import BankTransactionsList from '../components/BankTransactionsList';
import TestBankAPI from '../components/TestBankAPI';

function BankOverview() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['bank-stats'],
    queryFn: () => api.getBankImportStats(),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Prehľad bankových operácií
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Správa importu a spárovania bankových transakcií z Tatrabanka
      </Typography>

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 3, mb: 3 }}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <UploadIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">
                Import súborov
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" paragraph>
              Podporované formáty: XML (CAMT.053), XLS, XLSX, CSV
            </Typography>
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              href="/bank/upload"
            >
              Nahrať bankový výpis
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <BankIcon sx={{ mr: 1, color: 'success.main' }} />
              <Typography variant="h6">
                Bankové výpisy
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" paragraph>
              Prehľad všetkých importovaných bankových výpisov
            </Typography>
            <Button
              variant="outlined"
              startIcon={<StatsIcon />}
              href="/bank/statements"
            >
              Zobraziť výpisy
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <TransactionIcon sx={{ mr: 1, color: 'info.main' }} />
              <Typography variant="h6">
                Transakcie
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" paragraph>
              Spárovanie transakcií s faktúrami
            </Typography>
            <Button
              variant="outlined"
              startIcon={<TransactionIcon />}
              href="/bank/transactions"
            >
              Zobraziť transakcie
            </Button>
          </CardContent>
        </Card>
      </Box>

      <Alert severity="info">
        <Typography variant="subtitle2" gutterBottom>
          Ako používať bankové operácie:
        </Typography>
        <Typography variant="body2" component="div">
          1. <strong>Nahrať súbor</strong> - Importujte bankový výpis vo formáte XML, XLS alebo CSV<br/>
          2. <strong>Automatické spárovanie</strong> - Systém automaticky spáruje transakcie s faktúrami podľa variabilného symbolu<br/>
          3. <strong>Manuálne spárovanie</strong> - Zostávajúce transakcie môžete spárovať manuálne<br/>
          4. <strong>Kontrola</strong> - Skontrolujte spárované platby v sekcii transakcií
        </Typography>
      </Alert>
    </Box>
  );
}

function BankUpload() {
  const [dragOver, setDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { data: supportedTypes } = useQuery({
    queryKey: ['supported-file-types'],
    queryFn: () => api.getSupportedFileTypes(),
  });

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Nahrať bankový výpis
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Nahrajte súbor s bankovým výpisom z Tatrabanka
      </Typography>

      {/* Supported File Types */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Podporované formáty súborov:
        </Typography>
        <Typography variant="body2">
          • <strong>XML</strong> - CAMT.053 štandard (odporúčané)<br/>
          • <strong>XLS/XLSX</strong> - Excel súbory<br/>
          • <strong>CSV</strong> - Textové súbory s oddeľovačmi
        </Typography>
      </Alert>

      {/* File Upload Area */}
      <Card
        sx={{
          mb: 3,
          border: dragOver ? '2px dashed' : '2px solid',
          borderColor: dragOver ? 'primary.main' : 'divider',
          bgcolor: dragOver ? 'action.hover' : 'background.paper',
          cursor: 'pointer',
          transition: 'all 0.2s',
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <UploadIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Presuňte súbor sem
          </Typography>
          <Typography color="text.secondary" paragraph>
            alebo kliknite pre výber súboru
          </Typography>
          <input
            type="file"
            hidden
            id="file-upload"
            onChange={handleFileSelect}
            accept=".xml,.sta,.swift,.xls,.xlsx,.csv"
          />
          <label htmlFor="file-upload">
            <Button variant="contained" component="span">
              Vybrať súbor
            </Button>
          </label>
        </CardContent>
      </Card>

      {/* Selected File */}
      {selectedFile && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Selected File
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="body1">{selectedFile.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button variant="outlined">
                  Preview
                </Button>
                <Button variant="contained">
                  Upload
                </Button>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}

function BankStatements() {
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Bankové výpisy
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Správa importovaných bankových výpisov z Tatrabanka
      </Typography>

      <TestBankAPI />
      <BankStatementsList />
    </Box>
  );
}

function BankTransactions() {
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Bankové transakcie
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Prehľad všetkých bankových transakcií s možnosťou spárovania s faktúrami
      </Typography>

      <BankTransactionsList />
    </Box>
  );
}

export function BankPage() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const tabs = [
    { label: 'Prehľad', value: '/bank', icon: <BankIcon /> },
    { label: 'Nahrať súbor', value: '/bank/upload', icon: <UploadIcon /> },
    { label: 'Výpisy', value: '/bank/statements', icon: <StatsIcon /> },
    { label: 'Transakcie', value: '/bank/transactions', icon: <TransactionIcon /> },
  ];

  const currentTab = tabs.find(tab => 
    tab.value === location.pathname || 
    (tab.value === '/bank' && location.pathname === '/bank')
  )?.value || '/bank';

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      <Routes>
        <Route path="/" element={<BankOverview />} />
        <Route path="/upload" element={<BankUpload />} />
        <Route path="/statements" element={<BankStatements />} />
        <Route path="/transactions" element={<BankTransactions />} />
      </Routes>
    </Box>
  );
}
