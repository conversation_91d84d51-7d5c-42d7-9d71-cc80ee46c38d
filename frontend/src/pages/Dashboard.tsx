import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AccountBalance as BankIcon,
  Payment as PaymentIcon,
  Receipt as InvoiceIcon,
  CloudUpload as ExportIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend } from 'recharts';

import { api } from '@/lib/api';
import type { GlobalStats } from '@/types/api';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

interface StatCardProps {
  title: string;
  value: number | string;
  subtitle?: string;
  icon: React.ReactElement;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  progress?: number;
}

function StatCard({ title, value, subtitle, icon, color = 'primary', progress }: StatCardProps) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: 2,
              bgcolor: `${color}.light`,
              color: `${color}.contrastText`,
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        
        {subtitle && (
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {subtitle}
          </Typography>
        )}
        
        {progress !== undefined && (
          <Box sx={{ mt: 2 }}>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {progress.toFixed(1)}% complete
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

export function Dashboard() {
  console.log('🎯 DASHBOARD LOADED!');

  const {
    data: stats,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: () => api.getStats(),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const {
    data: systemStatus,
    isLoading: statusLoading,
  } = useQuery({
    queryKey: ['system-status'],
    queryFn: () => api.getStatus(),
    refetchInterval: 60000, // Refresh every minute
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load dashboard data. Please try again.
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ mb: 2 }}>
        No data available.
      </Alert>
    );
  }

  const matchingRate = stats.sync.totalTransactions > 0 
    ? (stats.sync.matchedTransactions / stats.sync.totalTransactions) * 100 
    : 0;

  const syncRate = stats.sync.totalInvoices > 0 
    ? (stats.sync.syncedInvoices / stats.sync.totalInvoices) * 100 
    : 0;

  const exportRate = stats.sync.totalPayments > 0 
    ? (stats.sync.exportedPayments / stats.sync.totalPayments) * 100 
    : 0;

  // Data for charts
  const pieData = [
    { name: 'Matched', value: stats.sync.matchedTransactions },
    { name: 'Unmatched', value: stats.unmatched.transactions },
  ];

  const barData = [
    {
      name: 'Invoices',
      synced: stats.sync.syncedInvoices,
      total: stats.sync.totalInvoices,
    },
    {
      name: 'Transactions',
      synced: stats.sync.matchedTransactions,
      total: stats.sync.totalTransactions,
    },
    {
      name: 'Payments',
      synced: stats.sync.exportedPayments,
      total: stats.sync.totalPayments,
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {systemStatus && (
            <Chip
              icon={systemStatus.database.connected ? <CheckCircleIcon /> : <WarningIcon />}
              label={systemStatus.database.connected ? 'System Healthy' : 'System Issues'}
              color={systemStatus.database.connected ? 'success' : 'error'}
              variant="outlined"
            />
          )}
          <Tooltip title="Refresh Data">
            <IconButton onClick={() => refetch()}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Main Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Invoices"
            value={stats.sync.totalInvoices.toLocaleString()}
            subtitle={`${stats.sync.syncedInvoices} synced`}
            icon={<InvoiceIcon />}
            color="primary"
            progress={syncRate}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Bank Transactions"
            value={stats.sync.totalTransactions.toLocaleString()}
            subtitle={`${stats.sync.matchedTransactions} matched`}
            icon={<BankIcon />}
            color="secondary"
            progress={matchingRate}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Payments"
            value={stats.sync.totalPayments.toLocaleString()}
            subtitle={`${stats.sync.exportedPayments} exported`}
            icon={<PaymentIcon />}
            color="success"
            progress={exportRate}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Matching Rate"
            value={`${matchingRate.toFixed(1)}%`}
            subtitle="Payment matching accuracy"
            icon={<TrendingUpIcon />}
            color={matchingRate > 80 ? 'success' : matchingRate > 60 ? 'warning' : 'error'}
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Payment Matching Overview
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sync Progress
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={barData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Bar dataKey="synced" fill="#8884d8" name="Processed" />
                  <Bar dataKey="total" fill="#82ca9d" name="Total" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Unmatched Transactions
              </Typography>
              {stats.recent.unmatchedTransactions.length === 0 ? (
                <Typography color="text.secondary">
                  No unmatched transactions
                </Typography>
              ) : (
                <Box>
                  {stats.recent.unmatchedTransactions.map((transaction) => (
                    <Box
                      key={transaction.id}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        py: 1,
                        borderBottom: '1px solid',
                        borderColor: 'divider',
                        '&:last-child': { borderBottom: 'none' },
                      }}
                    >
                      <Box>
                        <Typography variant="body2">
                          {transaction.counterpartyName || 'Unknown'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          VS: {transaction.variableSymbol || 'N/A'}
                        </Typography>
                      </Box>
                      <Typography variant="body2" fontWeight="bold">
                        €{transaction.amount.toFixed(2)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Unpaid Invoices
              </Typography>
              {stats.recent.unpaidInvoices.length === 0 ? (
                <Typography color="text.secondary">
                  No unpaid invoices
                </Typography>
              ) : (
                <Box>
                  {stats.recent.unpaidInvoices.map((invoice) => (
                    <Box
                      key={invoice.id}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        py: 1,
                        borderBottom: '1px solid',
                        borderColor: 'divider',
                        '&:last-child': { borderBottom: 'none' },
                      }}
                    >
                      <Box>
                        <Typography variant="body2">
                          {invoice.invoiceNumber}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Due: {new Date(invoice.dueDate).toLocaleDateString()}
                        </Typography>
                      </Box>
                      <Typography variant="body2" fontWeight="bold">
                        €{invoice.totalAmount.toFixed(2)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
