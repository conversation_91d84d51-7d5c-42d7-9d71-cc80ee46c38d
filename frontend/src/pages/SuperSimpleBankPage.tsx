import React from 'react';
import { Box, Typography, Button, Card, CardContent } from '@mui/material';

export function SuperSimpleBankPage() {
  console.log('SuperSimpleBankPage loaded!');
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h2" gutterBottom sx={{ color: 'red', fontWeight: 'bold' }}>
        🏦 BANKOVÉ VÝPISY - FUNGUJE!
      </Typography>
      
      <Typography variant="h4" sx={{ mb: 3 }}>
        Toto je najjednoduchšia verzia bankovej stránky
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button 
          variant="contained" 
          size="large"
          onClick={() => window.location.href = '/bank/upload'}
        >
          📁 UPLOAD
        </Button>
        <Button 
          variant="contained" 
          size="large"
          onClick={() => window.location.href = '/bank/statements'}
        >
          📊 VÝPISY
        </Button>
        <Button 
          variant="contained" 
          size="large"
          onClick={() => window.location.href = '/bank/transactions'}
        >
          💰 TRANSAKCIE
        </Button>
      </Box>
      
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Aktuálna URL: {window.location.pathname}
          </Typography>
          <Typography variant="body1">
            Ak vidíš túto stránku, routing funguje!
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}
