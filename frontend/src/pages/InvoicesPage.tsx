import React, { useState } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  InputAdornment
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';

// Mock data
const mockInvoices = [
  { id: 1, number: 'INV-2024-001', clientName: '<PERSON> AB-STAV', amount: 1250.50, status: 'paid', dueDate: '2024-01-15', createdDate: '2024-01-01' },
  { id: 2, number: 'INV-2024-002', clientName: 'BETA ELEKTRO s.r.o.', amount: 890.00, status: 'unpaid', dueDate: '2024-02-10', createdDate: '2024-01-25' },
  { id: 3, number: 'INV-2024-003', clientName: 'DANNY-K s.r.o.', amount: 2100.75, status: 'overdue', dueDate: '2024-01-20', createdDate: '2024-01-05' },
  { id: 4, number: 'INV-2024-004', clientName: 'Atlantis - dovolenka s.r.o.', amount: 650.25, status: 'paid', dueDate: '2024-02-15', createdDate: '2024-02-01' },
  { id: 5, number: 'INV-2024-005', clientName: 'Ecaza, s.r.o.', amount: 1800.00, status: 'unpaid', dueDate: '2024-02-28', createdDate: '2024-02-10' },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'paid': return 'success';
    case 'unpaid': return 'warning';
    case 'overdue': return 'error';
    default: return 'default';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'paid': return 'Zaplatené';
    case 'unpaid': return 'Nezaplatené';
    case 'overdue': return 'Po splatnosti';
    default: return status;
  }
};

export default function InvoicesPage() {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredInvoices = mockInvoices.filter(invoice =>
    invoice.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invoice.clientName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalAmount = mockInvoices.reduce((sum, inv) => sum + inv.amount, 0);
  const paidAmount = mockInvoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.amount, 0);
  const unpaidAmount = totalAmount - paidAmount;

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🧾 Faktúry z UISP
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Zoznam všetkých faktúr importovaných z UISP systému.
      </Typography>

      {/* Search */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder="Hľadať faktúry..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {/* Stats */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="primary">{mockInvoices.length}</Typography>
          <Typography variant="body2">Celkom faktúr</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="success.main">€{paidAmount.toFixed(2)}</Typography>
          <Typography variant="body2">Zaplatené</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="error.main">€{unpaidAmount.toFixed(2)}</Typography>
          <Typography variant="body2">Nezaplatené</Typography>
        </Box>
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="h4" color="info.main">€{totalAmount.toFixed(2)}</Typography>
          <Typography variant="body2">Celková suma</Typography>
        </Box>
      </Box>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Číslo faktúry</strong></TableCell>
              <TableCell><strong>Klient</strong></TableCell>
              <TableCell><strong>Suma</strong></TableCell>
              <TableCell><strong>Status</strong></TableCell>
              <TableCell><strong>Splatnosť</strong></TableCell>
              <TableCell><strong>Vytvorené</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredInvoices.map((invoice) => (
              <TableRow key={invoice.id} hover>
                <TableCell>
                  <Typography variant="body1" fontWeight="bold">
                    {invoice.number}
                  </Typography>
                </TableCell>
                <TableCell>{invoice.clientName}</TableCell>
                <TableCell>
                  <Typography variant="body1" fontWeight="bold">
                    €{invoice.amount.toFixed(2)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getStatusLabel(invoice.status)}
                    color={getStatusColor(invoice.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>{invoice.dueDate}</TableCell>
                <TableCell>{invoice.createdDate}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredInvoices.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            Žiadne faktúry neboli nájdené pre hľadaný výraz "{searchTerm}"
          </Typography>
        </Box>
      )}
    </Box>
  );
}
