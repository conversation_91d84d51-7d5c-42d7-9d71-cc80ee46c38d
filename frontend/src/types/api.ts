// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  error?: string;
}

// Common types
export type BankFileType = 'XML' | 'SWIFT' | 'XLS';
export type VATRate = '0' | 'N' | 'V' | 'X' | 'Y';
export type InvoiceStatus = 'draft' | 'sent' | 'overdue' | 'paid' | 'cancelled';
export type PaymentType = 'bank_transfer' | 'cash' | 'card' | 'check' | 'other';
export type DebitCredit = 'DBIT' | 'CRDT';

// UISP types
export interface UispClient {
  id: number;
  firstName: string;
  lastName: string;
  companyName?: string;
  email: string;
  phone?: string;
  isActive: boolean;
  organizationId: number;
  createdDate: string;
  modifiedDate: string;
}

export interface UispInvoice {
  id: number;
  number: string;
  clientId: number;
  organizationId: number;
  status: number;
  createdDate: string;
  dueDate: string;
  total: number;
  currency: string;
  items: UispInvoiceItem[];
}

export interface UispInvoiceItem {
  id: number;
  label: string;
  quantity: number;
  price: number;
  total: number;
  tax1: number;
}

export interface UispOrganization {
  id: number;
  name: string;
  registrationNumber?: string;
  taxId?: string;
  website?: string;
  email?: string;
  phone?: string;
}

// Bank types
export interface BankStatement {
  id: number;
  accountIban: string;
  statementDate: string;
  openingBalance: number;
  closingBalance: number;
  fileName: string;
  fileType: BankFileType;
  processed: boolean;
  omegaImported: boolean;
  createdAt: string;
  updatedAt: string;
  transactions?: BankTransaction[];
  _count?: {
    transactions: number;
  };
}

export interface BankTransaction {
  id: number;
  statementId: number;
  transactionDate: string;
  amount: number;
  currency: string;
  debitCredit: DebitCredit;
  variableSymbol?: string;
  specificSymbol?: string;
  constantSymbol?: string;
  counterpartyName?: string;
  counterpartyAccount?: string;
  counterpartyBankCode?: string;
  reference?: string;
  description: string;
  matchedInvoiceId?: number;
  createdAt: string;
  statement?: {
    accountIban: string;
    statementDate: string;
  };
  matchedInvoice?: {
    id: number;
    invoiceNumber: string;
    totalAmount: number;
  };
}

// Payment types
export interface Payment {
  id: number;
  invoiceId: number;
  bankTransactionId?: number;
  amount: number;
  paymentDate: string;
  paymentType: PaymentType;
  omegaExported: boolean;
  omegaExportedAt?: string;
  createdAt: string;
  invoice?: Invoice;
  bankTransaction?: BankTransaction;
}

export interface MatchResult {
  transaction: BankTransaction;
  invoice?: Invoice;
  matchType: 'exact' | 'fuzzy' | 'manual' | 'none';
  confidence: number;
  reasons: string[];
}

// Invoice types
export interface Invoice {
  id: number;
  uispInvoiceId: number;
  clientId: number;
  invoiceNumber: string;
  createdDate: string;
  dueDate: string;
  totalAmount: number;
  currency: string;
  status: InvoiceStatus;
  paidDate?: string;
  omegaImported: boolean;
  omegaImportedAt?: string;
  createdAt: string;
  updatedAt: string;
  items?: InvoiceItem[];
  clientMapping?: ClientMapping;
}

export interface InvoiceItem {
  id: number;
  invoiceId: number;
  label: string;
  quantity: number;
  unitPrice: number;
  vatRate: VATRate;
  totalAmount: number;
}

export interface ClientMapping {
  id: number;
  uispClientId: number;
  omegaPartnerName?: string;
  omegaPartnerIco?: string;
  omegaPartnerDic?: string;
  omegaPartnerAddress?: string;
  omegaPartnerCity?: string;
  omegaPartnerZip?: string;
  createdAt: string;
  updatedAt: string;
}

// Statistics types
export interface GlobalStats {
  sync: {
    totalInvoices: number;
    syncedInvoices: number;
    totalTransactions: number;
    matchedTransactions: number;
    totalPayments: number;
    exportedPayments: number;
  };
  unmatched: {
    transactions: number;
    invoices: number;
  };
  recent: {
    unmatchedTransactions: Array<{
      id: number;
      amount: number;
      transactionDate: string;
      counterpartyName?: string;
      variableSymbol?: string;
    }>;
    unpaidInvoices: Array<{
      id: number;
      invoiceNumber: string;
      totalAmount: number;
      dueDate: string;
      clientId: number;
    }>;
  };
}

export interface MatchingStats {
  totalTransactions: number;
  matchedTransactions: number;
  exactMatches: number;
  fuzzyMatches: number;
  manualMatches: number;
  unmatchedTransactions: number;
  matchingRate: number;
}

export interface ImportStats {
  totalStatements: number;
  totalTransactions: number;
  processedToday: number;
  lastImportDate: string | null;
  fileTypes: Array<{ type: string; count: number }>;
}

export interface ExportStats {
  totalExports: number;
  lastExportDate: string | null;
  exportsByType: Record<string, number>;
  totalFileSize: number;
  exportPath: string;
}

// Configuration types
export interface AppConfig {
  uisp: {
    hasApiUrl: boolean;
    hasApiKey: boolean;
    isConfigured: boolean;
  };
  omega: {
    importPath: string;
    hasImportPath: boolean;
  };
  sync: {
    autoSyncEnabled: boolean;
    syncIntervalMinutes: number;
  };
  database: {
    connected: boolean;
  };
  environment: string;
}

// System types
export interface SystemInfo {
  node: {
    version: string;
    platform: string;
    arch: string;
  };
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  uptime: number;
  environment: string;
  database: {
    connected: boolean;
    stats: any;
  };
}

export interface OperationLog {
  id: number;
  operationType: string;
  status: 'start' | 'success' | 'error';
  metadata?: any;
  error?: string;
  duration?: number;
  createdAt: string;
}

// File types
export interface ExportFile {
  fileName: string;
  filePath: string;
  size: number;
  createdDate: string;
  type: 'invoices' | 'payments' | 'other';
}

export interface FilePreview {
  fileType: BankFileType;
  accountIban?: string;
  statementDate?: string;
  transactionCount?: number;
  fileSize: number;
  isValid: boolean;
  errors?: string[];
}

// Form types
export interface UispCredentials {
  apiUrl: string;
  apiKey: string;
}

export interface OmegaConfig {
  importPath: string;
}

export interface SyncConfig {
  autoSyncEnabled: boolean;
  syncIntervalMinutes: number;
}

export interface SyncOptions {
  fromDate?: string;
  toDate?: string;
  clientId?: number;
  force?: boolean;
  dryRun?: boolean;
}

export interface ExportOptions {
  fromDate?: string;
  toDate?: string;
  invoiceIds?: number[];
  clientIds?: number[];
  onlyPaid?: boolean;
  onlyUnexported?: boolean;
}

export interface MatchingOptions {
  statementId?: number;
  transactionId?: number;
  invoiceId?: number;
  force?: boolean;
  dryRun?: boolean;
  maxDaysDifference?: number;
  fuzzyNameMatching?: boolean;
  allowPartialMatches?: boolean;
}
