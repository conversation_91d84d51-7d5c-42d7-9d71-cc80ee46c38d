import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type {
  ApiResponse,
  UispClient,
  UispInvoice,
  UispOrganization,
  BankStatement,
  BankTransaction,
  Payment,
  Invoice,
  GlobalStats,
  MatchingStats,
  ImportStats,
  ExportStats,
  AppConfig,
  SystemInfo,
  OperationLog,
  ExportFile,
  FilePreview,
  MatchResult,
  UispCredentials,
  OmegaConfig,
  SyncConfig,
  SyncOptions,
  ExportOptions,
  MatchingOptions,
} from '@/types/api';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for auth
    this.client.interceptors.request.use((config) => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Helper method to extract data from response
  private extractData<T>(response: AxiosResponse<ApiResponse<T>>): T {
    return response.data.data;
  }

  // Health and Status
  async getHealth() {
    const response = await this.client.get<ApiResponse<any>>('/health');
    return this.extractData(response);
  }

  async getStatus() {
    const response = await this.client.get<ApiResponse<any>>('/status');
    return this.extractData(response);
  }

  async getInfo() {
    const response = await this.client.get<ApiResponse<any>>('/info');
    return this.extractData(response);
  }

  async getStats(): Promise<GlobalStats> {
    const response = await this.client.get<ApiResponse<GlobalStats>>('/stats');
    return this.extractData(response);
  }

  async getLogs(params?: {
    operationType?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<OperationLog[]> {
    const response = await this.client.get<ApiResponse<OperationLog[]>>('/logs', { params });
    return this.extractData(response);
  }

  async testDatabase() {
    const response = await this.client.get<ApiResponse<any>>('/test-db');
    return this.extractData(response);
  }

  // UISP API
  async testUispConnection() {
    const response = await this.client.get<ApiResponse<any>>('/uisp/test-connection');
    return this.extractData(response);
  }

  async getUispOrganizations(): Promise<UispOrganization[]> {
    const response = await this.client.get<ApiResponse<UispOrganization[]>>('/uisp/organizations');
    return this.extractData(response);
  }

  async getUispClients(params?: {
    organizationId?: number;
    isActive?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<UispClient[]> {
    const response = await this.client.get<ApiResponse<UispClient[]>>('/uisp/clients', { params });
    return this.extractData(response);
  }

  async getUispClient(id: number): Promise<UispClient> {
    const response = await this.client.get<ApiResponse<UispClient>>(`/uisp/clients/${id}`);
    return this.extractData(response);
  }

  async getUispInvoices(params?: {
    clientId?: number;
    status?: number;
    createdDateFrom?: string;
    createdDateTo?: string;
    limit?: number;
    offset?: number;
  }): Promise<UispInvoice[]> {
    const response = await this.client.get<ApiResponse<UispInvoice[]>>('/uisp/invoices', { params });
    return this.extractData(response);
  }

  async getUispInvoice(id: number): Promise<UispInvoice> {
    const response = await this.client.get<ApiResponse<UispInvoice>>(`/uisp/invoices/${id}`);
    return this.extractData(response);
  }

  async getRecentUispInvoices(days: number = 30): Promise<UispInvoice[]> {
    const response = await this.client.get<ApiResponse<UispInvoice[]>>('/uisp/invoices/recent', {
      params: { days },
    });
    return this.extractData(response);
  }

  async getUnpaidUispInvoices(): Promise<UispInvoice[]> {
    const response = await this.client.get<ApiResponse<UispInvoice[]>>('/uisp/invoices/unpaid');
    return this.extractData(response);
  }

  async syncInvoices(options: SyncOptions = {}) {
    const response = await this.client.post<ApiResponse<any>>('/uisp/sync/invoices', options);
    return this.extractData(response);
  }

  async syncInvoice(uispInvoiceId: number, force: boolean = false) {
    const response = await this.client.post<ApiResponse<any>>('/uisp/sync/invoice', {
      uispInvoiceId,
      force,
    });
    return this.extractData(response);
  }

  async getSyncStats() {
    const response = await this.client.get<ApiResponse<any>>('/uisp/sync/stats');
    return this.extractData(response);
  }

  async updateUispCredentials(credentials: UispCredentials) {
    const response = await this.client.put<ApiResponse<any>>('/uisp/credentials', credentials);
    return this.extractData(response);
  }

  async getUispStatus() {
    const response = await this.client.get<ApiResponse<any>>('/uisp/status');
    return this.extractData(response);
  }

  // Bank API
  async getSupportedFileTypes() {
    const response = await this.client.get<ApiResponse<any>>('/bank/supported-types');
    return this.extractData(response);
  }

  async uploadBankFile(file: File, options?: {
    fileType?: string;
    force?: boolean;
    dryRun?: boolean;
  }) {
    const formData = new FormData();
    formData.append('file', file);
    if (options?.fileType) formData.append('fileType', options.fileType);
    if (options?.force) formData.append('force', 'true');
    if (options?.dryRun) formData.append('dryRun', 'true');

    const response = await this.client.post<ApiResponse<any>>('/bank/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return this.extractData(response);
  }

  async previewBankFile(file: File, fileType?: string): Promise<FilePreview> {
    const formData = new FormData();
    formData.append('file', file);
    if (fileType) formData.append('fileType', fileType);

    const response = await this.client.post<ApiResponse<FilePreview>>('/bank/preview', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return this.extractData(response);
  }

  async validateBankFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post<ApiResponse<any>>('/bank/validate', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return this.extractData(response);
  }

  async getBankStatements(params?: {
    accountIban?: string;
    fromDate?: string;
    toDate?: string;
    processed?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<BankStatement[]> {
    const response = await this.client.get<ApiResponse<BankStatement[]>>('/bank/statements', { params });
    return this.extractData(response);
  }

  async getBankStatement(id: number): Promise<BankStatement> {
    const response = await this.client.get<ApiResponse<BankStatement>>(`/bank/statements/${id}`);
    return this.extractData(response);
  }

  async deleteBankStatement(id: number) {
    const response = await this.client.delete<ApiResponse<any>>(`/bank/statements/${id}`);
    return this.extractData(response);
  }

  async getBankTransactions(params?: {
    statementId?: number;
    debitCredit?: string;
    matched?: boolean;
    fromDate?: string;
    toDate?: string;
    minAmount?: number;
    maxAmount?: number;
    counterpartyName?: string;
    variableSymbol?: string;
    limit?: number;
    offset?: number;
  }): Promise<BankTransaction[]> {
    const response = await this.client.get<ApiResponse<BankTransaction[]>>('/bank/transactions', { params });
    return this.extractData(response);
  }

  async getBankImportStats(): Promise<ImportStats> {
    const response = await this.client.get<ApiResponse<ImportStats>>('/bank/stats');
    return this.extractData(response);
  }

  // Payment API
  async matchPayments(options: MatchingOptions = {}): Promise<MatchResult[]> {
    const response = await this.client.post<ApiResponse<MatchResult[]>>('/payment/match', options);
    return this.extractData(response);
  }

  async manualMatch(transactionId: number, invoiceId: number, amount?: number): Promise<Payment> {
    const response = await this.client.post<ApiResponse<Payment>>('/payment/match/manual', {
      transactionId,
      invoiceId,
      amount,
    });
    return this.extractData(response);
  }

  async unmatchPayment(transactionId: number) {
    const response = await this.client.post<ApiResponse<any>>('/payment/unmatch', { transactionId });
    return this.extractData(response);
  }

  async getMatchingStats(): Promise<MatchingStats> {
    const response = await this.client.get<ApiResponse<MatchingStats>>('/payment/stats');
    return this.extractData(response);
  }

  async getUnmatchedTransactions(): Promise<BankTransaction[]> {
    const response = await this.client.get<ApiResponse<BankTransaction[]>>('/payment/unmatched');
    return this.extractData(response);
  }

  async getMatchedPayments(): Promise<Payment[]> {
    const response = await this.client.get<ApiResponse<Payment[]>>('/payment/matched');
    return this.extractData(response);
  }

  async getPayments(params?: {
    invoiceId?: number;
    bankTransactionId?: number;
    fromDate?: string;
    toDate?: string;
    paymentType?: string;
    omegaExported?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<Payment[]> {
    const response = await this.client.get<ApiResponse<Payment[]>>('/payment', { params });
    return this.extractData(response);
  }

  async getPayment(id: number): Promise<Payment> {
    const response = await this.client.get<ApiResponse<Payment>>(`/payment/${id}`);
    return this.extractData(response);
  }

  async deletePayment(id: number) {
    const response = await this.client.delete<ApiResponse<any>>(`/payment/${id}`);
    return this.extractData(response);
  }

  async getPaymentSuggestions(transactionId: number, maxSuggestions: number = 5) {
    const response = await this.client.get<ApiResponse<any>>(`/payment/suggestions/${transactionId}`, {
      params: { maxSuggestions },
    });
    return this.extractData(response);
  }

  // Omega API
  async exportInvoices(options: ExportOptions = {}) {
    const response = await this.client.post<ApiResponse<any>>('/omega/export/invoices', options);
    return this.extractData(response);
  }

  async exportPayments(options: ExportOptions = {}) {
    const response = await this.client.post<ApiResponse<any>>('/omega/export/payments', options);
    return this.extractData(response);
  }

  async batchExport(options: {
    invoiceOptions?: ExportOptions;
    paymentOptions?: ExportOptions;
    exportInvoices?: boolean;
    exportPayments?: boolean;
    cleanupOldFiles?: boolean;
    daysToKeep?: number;
  } = {}) {
    const response = await this.client.post<ApiResponse<any>>('/omega/export/batch', options);
    return this.extractData(response);
  }

  async getInvoiceExportPreview(options: ExportOptions = {}) {
    const response = await this.client.post<ApiResponse<any>>('/omega/preview/invoices', options);
    return this.extractData(response);
  }

  async getPaymentExportPreview(options: ExportOptions = {}) {
    const response = await this.client.post<ApiResponse<any>>('/omega/preview/payments', options);
    return this.extractData(response);
  }

  async getOmegaStats(): Promise<ExportStats> {
    const response = await this.client.get<ApiResponse<ExportStats>>('/omega/stats');
    return this.extractData(response);
  }

  async getExportFiles(): Promise<ExportFile[]> {
    const response = await this.client.get<ApiResponse<ExportFile[]>>('/omega/files');
    return this.extractData(response);
  }

  async downloadExportFile(fileName: string): Promise<Blob> {
    const response = await this.client.get(`/omega/files/${fileName}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async deleteExportFile(fileName: string) {
    const response = await this.client.delete<ApiResponse<any>>(`/omega/files/${fileName}`);
    return this.extractData(response);
  }

  async validateOmegaConfig() {
    const response = await this.client.get<ApiResponse<any>>('/omega/validate');
    return this.extractData(response);
  }

  async getOmegaStatus() {
    const response = await this.client.get<ApiResponse<any>>('/omega/status');
    return this.extractData(response);
  }

  // Config API
  async getConfig(): Promise<AppConfig> {
    const response = await this.client.get<ApiResponse<AppConfig>>('/config');
    return this.extractData(response);
  }

  async updateUispConfig(config: UispCredentials) {
    const response = await this.client.put<ApiResponse<any>>('/config/uisp', config);
    return this.extractData(response);
  }

  async updateOmegaConfig(config: OmegaConfig) {
    const response = await this.client.put<ApiResponse<any>>('/config/omega', config);
    return this.extractData(response);
  }

  async updateSyncConfig(config: SyncConfig) {
    const response = await this.client.put<ApiResponse<any>>('/config/sync', config);
    return this.extractData(response);
  }

  async testUispConfig() {
    const response = await this.client.post<ApiResponse<any>>('/config/uisp/test');
    return this.extractData(response);
  }

  async testOmegaConfig() {
    const response = await this.client.post<ApiResponse<any>>('/config/omega/test');
    return this.extractData(response);
  }

  async getSystemInfo(): Promise<SystemInfo> {
    const response = await this.client.get<ApiResponse<SystemInfo>>('/config/system');
    return this.extractData(response);
  }

  async resetConfig() {
    const response = await this.client.post<ApiResponse<any>>('/config/reset');
    return this.extractData(response);
  }

  // Invoices API (local database)
  async getInvoices(params?: {
    clientId?: number;
    status?: string;
    fromDate?: string;
    toDate?: string;
    omegaImported?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<Invoice[]> {
    // This would be implemented if we had local invoice endpoints
    // For now, we use UISP invoices
    return [];
  }
}

// Create singleton instance
export const api = new ApiClient();
export default api;
