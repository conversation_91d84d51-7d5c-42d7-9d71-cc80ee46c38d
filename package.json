{"name": "uisp-omega-bridge", "version": "1.0.0", "description": "Bridge application for synchronizing invoices and payments between UISP CRM and KROS Omega", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc && tsc-alias", "start": "node dist/server.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "tsx src/database/seed.ts", "db:studio": "prisma studio", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["uisp", "omega", "accounting", "invoices", "payments", "bridge", "api"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@prisma/client": "^5.7.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "winston": "^3.11.0", "xml2js": "^0.6.2", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/supertest": "^2.0.16", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.8", "tsx": "^4.6.0", "typescript": "^5.3.2"}}