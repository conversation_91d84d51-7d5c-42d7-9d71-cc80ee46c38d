FORMAT: 1A
HOST: https://your-domain.example.com/crm/api/v1.0

# API doc for UISP CRM
This is the API documentation for CRM module of the UISP v1 app.

## Authentication

In order to use this API, you will need an app key.

### Generating an App Key
Go to **App keys** (Settings => Security => App keys) section of your UISP CRM module
and generate a new app key of required type (**Read** or **Write**).

| Request Method | Type        |
|----------------|-------------|
| GET            | Read, Write |
| POST           | Write       |
| PUT            | Write       |
| PATCH          | Write       |
| DELETE         | Write       |

### Using an App Key
Once you have generated an app key, pass it in `X-Auth-App-Key` header in each request.

If an app key is not passed or invalid, you will get a `401 Unauthorized` response.
If an app key is of wrong type, you will get a `403 Forbidden` response.


# Group Clients

# /clients{?organizationId,userIdent,customAttributeKey,customAttributeValue,lead,email,phone,username,isArchived,query,clientTagIds%5B%5D,limit,offset,order,direction}

## GET
Retrieve collection of **Clients**.

+ Parameters
    + organizationId: `1` (number, optional) - ID of an **Organization** to filter by.
    + userIdent: `123456` (string, optional) - Search by userIdent (client's custom ID).
    + customAttributeKey: `myCustomAttribute` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeValue.
    + customAttributeValue: `value` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeKey.
    + lead: `1` (number, optional) - Filter client leads.
    + email: `<EMAIL>` (string, optional) - Filter by email address.
    + phone: `************ x73323` (string, optional) - Filter by phone number.
    + username: `fooFromExampleOrg` (string, optional) - Filter by username.
    + isArchived: `1` (number, optional) - Return archived clients.
        + Default: `0`
    + query: `John Doe` (string, optional) - A search query of at least two characters.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `user.lastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `client.id`
        + Members
            + `user.firstName`
            + `user.lastName`
            + `client.registrationDate`
            + `client.id`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`
    + uninvoicedFeeTypes: `uninvoicedFeeTypes[]=1&uninvoicedFeeTypes[]=2` (enum[number], optional) - Uninvoiced fee types of a **Client** to filter by.
        + Members
            + 1: late fee
            + 2: setup fee
            + 3: early termination fee
    + clientTagIds%5B%5D: `1` (number, optional) - Filter by client tags.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientReadOnly])

+ Response 400 (application/json)
    + Attributes (Error400)

## POST /clients
Create **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientWritable)

+ Response 201 (application/json)
    + Headers
            Location: /clients/1001
    + Attributes (ClientReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /clients/{id}

## GET
Retrieve **Client**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Client**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientWritable)

+ Response 200 (application/json)
    + Attributes (ClientReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /clients/{id}/add-tag/{tagId}

Add **Client Tag** to **Client**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to add tag to.
    + tagId: `1000` (number, required) - ID of a **Client Tag** to add to the client.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /clients/{id}/remove-tag/{tagId}

Remove **Client Tag** from **Client**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to remove tag from.
    + tagId: `1000` (number, required) - ID of a **Client Tag** to remove from the client.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /clients/{id}/send-invitation

Send an invitation email to **Client**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to send the invitation email to.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /clients/{id}/archive

Archive **Client**

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to archive.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /clients/{id}/restore

Restore archived **Client**

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to restore.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE /clients/{id}

Permanently delete **Client**

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to permanently delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /clients/authenticated

## POST
Find **Client** by valid credentials. You should only use this endpoint over secured (HTTPS) connection.

+ Attributes
    + username: `<EMAIL>` (string) - Client's username.
    + password: `mySecurePassword` (string) - Client's password.


+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /clients/{id}/geocode

Automatically geocode **Client's** address and update GPS coordinates with the result.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client** to geocode.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Bank Accounts

# /clients/bank-accounts/{id}
## GET

Retrieve **Client Bank Account**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client Bank Account** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientBankAccountReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Client Bank Account**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client Bank Account** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientBankAccount)

+ Response 200 (application/json)
    + Attributes (ClientBankAccountReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Client Bank Account**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client Bank Account** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

# /clients/{id}/bank-accounts
## GET

Retrieve collection of **Client Bank Accounts** filtered by **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientBankAccountReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST

Create **Client Bank Account** for **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientBankAccount)

+ Response 201 (application/json)
    + Headers
            Location: /clients/bank-accounts/2
    + Attributes (ClientBankAccountReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# Group Client Contacts

# /clients/contacts/{id}
## GET

Retrieve **Client Contact**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client Contact** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientContactReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Client Contact**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client Contact** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientContactWritable)

+ Response 200 (application/json)
    + Attributes (ClientContactReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Client Contact**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client Contact** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

# /clients/{id}/contacts
## GET

Retrieve collection of **Client Contacts** filtered by **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientContactReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST

Create **Client Contact** for **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientContactWritable)

+ Response 201 (application/json)
    + Headers
            Location: /clients/contacts/2
    + Attributes (ClientContactReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# Group Client Logs

# /client-logs{?clientId,createdDateFrom,createdDateTo}
## GET

Retrieve collection of **ClientLogs**.

+ Parameters
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.

    + createdDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.

    + createdDateTo: `2017-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientLogsReadOnly])

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /client-logs

Create **ClientLogs**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientLogsWritable)

+ Response 201 (application/json)
    + Headers
            Location: /client-logs/1001
    + Attributes (ClientLogsReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/client-logs/{id}

## GET

Retrieve **ClientLog**.

+ Parameters
    + id: `1000` (number, required) - ID of a **ClientLog** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientLogsReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **ClientLog**.

+ Parameters
    + id: `1000` (number, required) - ID of a **ClientLog** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientLogsWritable)

+ Response 200 (application/json)
    + Attributes (ClientLogsReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **ClientLoge**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client log** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Tags

# /client-tags

## GET

Retrieve collection of **Client Tags**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientTagReadOnly])

## POST

Create **Client Tag**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientTag)

+ Response 201 (application/json)
    + Headers
            Location: /client-tags/2
    + Attributes (ClientTagReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/client-tags/{id}

## PATCH

Update **Client Tag**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client Tag** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientTag)

+ Response 200 (application/json)
    + Attributes (ClientTagReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Client Tag**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client Tag** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientTagReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Client Tag**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Client Tag** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Credit Note Templates

## GET /credit-note-templates
Retrieve collection of **Credit Note Templates**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[CreditNoteTemplateReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /credit-note-templates/{id}
Retrieve **Credit Note Template**.

+ Parameters
    + id: `1` (number, required) - ID of an **Credit Note Template** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CreditNoteTemplateReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Credit Notes

## GET /credit-notes{?organizationId,clientId,createdDateFrom,createdDateTo,number,query,limit,offset,order,direction}
Retrieve collection of **Credit Notes**.

+ Parameters
    + organizationId: `1` (number, optional) - ID of an **Organization** to filter by.
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + number: `IN20171030` (string, optional) - Search by **Credit Note** number.
    + query: `Wrong type of service.` (string, optional) - A search query of at least two characters.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `clientLastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `clientFirstName`
            + `clientLastName`
            + `createdDate`
            + `number`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[CreditNoteReadOnly])

+ Response 422 (application/json)
    + Attributes (Error422)

+ Response 404 (application/json)
    + Attributes (Error404)

# /credit-notes/{id}
## GET
Retrieve **Credit Note**.

+ Parameters
    + id: `1` (number, required) - ID of an **Credit Note** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CreditNoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH
Update **Credit Note**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Credit Note** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (CreditNoteUpdate)

+ Response 200 (application/json)
    + Attributes (CreditNoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE
Permanently delete **Credit Note**

+ Parameters
    + id: `1000` (number, required) - ID of an **Credit Note** to permanently delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /clients/{id}/credit-note
Create **Credit Note** for **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client** to create **Credit Note** for.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (CreditNoteNew)

+ Response 201 (application/json)
    + Headers
            Location: /credit-notes/2
    + Attributes (CreditNoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /credit-notes/{id}/send
Send **Credit Note** to client.

+ Parameters
    + id: `1` (number, required) - ID of an **Credit Note**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CreditNoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /credit-notes/{id}/regenerate-pdf{?refreshData}
Regenerate PDF of **Credit Note**.

+ Parameters
    + id: `1` (number, required) - ID of an **Credit Note**.
    + refreshData: `1` (number, optional) - Refresh the client and organization data.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CreditNoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## GET /credit-notes/{id}/pdf

Retrieve **Credit Note** PDF.

+ Parameters
    + id: `1000` (number, required) - ID of a **Credit Note** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/pdf)


+ Response 404 (application/json)
    + Attributes (Error404)


# Group Custom Attributes

# /custom-attributes{?attributeType}
## GET

Retrieve collection of **Custom Attributes**.

+ Parameters
    + attributeType: `client` (enum[string], optional) - Select only attributes of given type.
        + Members
            + `client`
            + `invoice`
            + `payment`
            + `service`
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[CustomAttributeReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

# /custom-attributes
## POST

Create **Custom Attribute**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (CustomAttribute)

+ Response 201 (application/json)
    + Headers
            Location: /custom-attributes/1001
    + Attributes (CustomAttributeReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/custom-attributes/{id}
## GET

Retrieve **Custom Attribute**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Custom Attribute** to fetch / update

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CustomAttributeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Custom Attribute**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Custom Attribute** to fetch / update

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (CustomAttribute)

+ Response 200 (application/json)
    + Attributes (CustomAttributeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## DELETE

Delete **Custom Attribute**.

+ Parameters
    + id: `1` (number, required) - ID of a **Custom Attribute** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Document Templates

## GET /document-templates
Retrieve collection of **Document Templates**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[DocumentTemplateReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /document-templates/{id}
Retrieve **Document Template**.

+ Parameters
    + id: `1` (number, required) - ID of an **Document Template** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (DocumentTemplateReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Documents

# /documents{?clientId,types,limit,offset}

## GET

Retrieve collection of **Documents**.

+ Parameters
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + types: `types[]=document&types[]=image` (enum[string], optional) - Types of a **Document** to filter by.
        + Members
            + `document`
            + `image`
            + `other`
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[DocumentReadOnly])

+ Response 400 (application/json)
    + Attributes (Error400)

## POST

Create **Document**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (DocumentWritable)

+ Response 201 (application/json)
    + Headers
        Location: /documents/1
    + Attributes (DocumentReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/documents/{id}

## GET

Retrieve **Document**.

+ Parameters
    + id: `1` (number, required) - ID of a **Document** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (DocumentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Document**.

+ Parameters
    + id: `1` (number, required) - ID of a **Document** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

#/documents/{id}/file

## GET

Retrieve **Document** file resource.

+ Parameters
    + id: `1` (number, required) - ID of a **Document** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (*/*)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Geocoding

# /geocode{?address,lat,lon}

## GET
Geocode the provided address. If user location is provided, nearby results are preferred.

+ Parameters
    + address: `685 3rd ave, nyc` (string) - address to look up
    + lat: `24.80718` (string, optional) - user location - latitude
    + lon: `-81.12305` (string, optional) - user location - longitude

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (LocationData)

+ Response 404 (application/json)
    + Attributes (Error404)

# /geocode/suggest{?query,lat,lon,sessionToken}

## GET

Suggests addresses for the provided query. If user location is provided, nearby results are preferred.

+ Parameters
    + query: `685 3rd ave, nyc` (string) - address to look up
    + lat: `24.80718` (string, optional) - user location - latitude
    + lon: `-81.12305` (string, optional) - user location - longitude
    + sessionToken: `add4649c-379f-476d-b8a4-5fd2ad525bca` (string, optional)
        Session tokens are only used if you have Google API key configured in UISP.
        You should generate unique session token for suggest requests and then regenerate it, as soon as you use given `id` to geocode the suggested address.
        You can read more at https://developers.google.com/places/web-service/session-tokens

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[AddressSuggestionData])

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Email

## POST /email/{id}/enqueue
Enqueues an email message to be sent by given **Organization**.

Please note, that if the email fails to send and contains file attachments, it will be only possible to resend it for 3 days or until CRM container restarts. This is because the attachments are only saved to the temp directory.

+ Parameters
    + id: `1000` (number, required) - ID of an **Organization**

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Email)

+ Response 201 (application/json)

+ Response 422 (application/json)
    + Attributes (Error422)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Fees

#/fees{?limit,offset,clientId,type,invoiced}
##GET

Retrieve collection of **Fees**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + type: `1` (enum[number], optional) - type of a **Fee** to filter by.
        + Members
            + 1: late fee
            + 2: setup fee
            + 3: early termination fee
    + invoiced: `1` (number, optional) - Filter invoiced **Fees**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[Fee])

+ Response 404 (application/json)
    + Attributes (Error404)

#/fees/{id}
## GET

Retrieve **Fee**.

+ Parameters
    + id: `1` (number, required) - ID of a **Fee** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (Fee)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Fee**. Only uninvoiced fees and fees not present on a quote can be deleted.

+ Parameters
    + id: `1` (number, required) - ID of a **Fee** to delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

<!--
# Group Inventory Products

## GET /inventory/products{?limit,offset}

Retrieve collection of **Inventory Product**s.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[InventoryProductReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /inventory/products/{id}

Retrieve **Inventory Product**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Product** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InventoryProductReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /inventory/products

Create an **Inventory Product**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    
    + Attributes (InventoryProduct)

+ Response 201 (application/json)
    + Headers
            Location: /inventory/products/0fd4db59-f76e-41cb-a44f-786ec32c25e4
    + Attributes (InventoryProductReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /inventory/products/{id}

Update an **Inventory Product**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Product** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    
    + Attributes (InventoryProduct)

+ Response 200 (application/json)
    + Attributes (InventoryProductReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE /inventory/products/{id}

Delete an **Inventory Product**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Product** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)



# Group Inventory Units

## GET /inventory/units{?productId,limit,offset}

Retrieve collection of **Inventory Unit**s.

+ Parameters
    + productId: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, optional) - UUID of an **Inventory Product** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[InventoryUnitReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /inventory/units/{id}

Retrieve **Inventory Unit**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Unit** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InventoryUnitReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /inventory/units

Create an **Inventory Unit**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    
    + Attributes (InventoryUnit)

+ Response 201 (application/json)
    + Headers
            Location: /inventory/units/0fd4db59-f76e-41cb-a44f-786ec32c25e4
    + Attributes (InventoryUnitReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /inventory/units/{id}

Move an **Inventory Unit**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Unit** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    
    + Attributes (InventoryMoveUnit)

+ Response 200 (application/json)
    + Attributes (InventoryUnitReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)
  
## DELETE /inventory/units/{id}

Delete an **Inventory Unit**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Unit** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Inventory Warehouses

## GET /inventory/warehouses{?limit,offset}

Retrieve collection of **Inventory Warehouse**s.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[InventoryWarehouseReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /inventory/warehouses/{id}

Retrieve **Inventory Warehouse**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Warehouse** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InventoryWarehouseReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /inventory/warehouses

Create an **Inventory Warehouse**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    
    + Attributes (InventoryWarehouse)

+ Response 201 (application/json)
    + Headers
            Location: /inventory/warehouses/0fd4db59-f76e-41cb-a44f-786ec32c25e4
    + Attributes (InventoryWarehouseReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /inventory/warehouses/{id}

Update an **Inventory Warehouse**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Warehouse** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    
    + Attributes (InventoryWarehouse)

+ Response 200 (application/json)
    + Attributes (InventoryWarehouseReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)
  
## DELETE /inventory/warehouses/{id}

Delete an **Inventory Warehouse**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of an **Inventory Warehouse** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)
-->

# Group Invoice Templates

## GET /invoice-templates
Retrieve collection of **Invoice Templates**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[InvoiceTemplateReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /invoice-templates/{id}
Retrieve **Invoice Template**.

+ Parameters
    + id: `1` (number, required) - ID of an **Invoice Template** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InvoiceTemplateReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Invoices

## GET /invoices{?organizationId,clientId,createdDateFrom,createdDateTo,statuses%5B%5D,number,overdue,proforma,customAttributeKey,customAttributeValue,query,limit,offset,order,direction}
Retrieve collection of **Invoices**.

+ Parameters
    + organizationId: `1` (number, optional) - ID of an **Organization** to filter by.
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + statuses%5B%5D: `1` (array[InvoiceStatus], optional) - Filter by invoice statuses.
    + number: `IN20171030` (string, optional) - Search by invoice number.
    + overdue: `1` (number, optional) - Filter overdue invoices.
    + proforma: `1` (number, optional) - Filter proforma invoices.
    + customAttributeKey: `myCustomAttribute` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeValue.
    + customAttributeValue: `value` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeKey.
    + query: `John Doe` (string, optional) - A search query of at least two characters.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `clientLastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `clientFirstName`
            + `clientLastName`
            + `createdDate`
            + `number`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[InvoiceReadOnly])

+ Response 422 (application/json)
    + Attributes (Error422)

+ Response 404 (application/json)
    + Attributes (Error404)

# /invoices/{id}
## GET
Retrieve **Invoice**.

+ Parameters
    + id: `1` (number, required) - ID of an **Invoice** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH
Update **Invoice**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Invoice** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (InvoiceUpdate)

+ Response 200 (application/json)
    + Attributes (InvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /invoices/{id}/approve

Approve draft **Invoice**.

+ Parameters
    + id: `5` (number, required) - ID of a **Invoice** to approve.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE
Permanently delete **Invoice**

+ Parameters
    + id: `1000` (number, required) - ID of an **Invoice** to permanently delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


## POST /clients/{id}/invoices
Create **Invoice** for **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client** to create **Invoice** for.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (InvoiceNew)

+ Response 201 (application/json)
    + Headers
            Location: /clients/invoices/2
    + Attributes (InvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## POST /clients/{id}/invoice-preview
Generates preview for **Invoice**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client** to create **Invoice** for.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (InvoiceUpdate)

+ Response 200 (application/json)
    + Attributes (InvoiceReadOnlyPreview)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## PATCH /invoices/{id}/send
Send invoice to client.

+ Parameters
    + id: `1` (number, required) - ID of an **Invoice**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /invoices/{id}/regenerate-pdf{?refreshData}
Regenerate PDF of invoice.

+ Parameters
    + id: `1` (number, required) - ID of an **Invoice**.
    + refreshData: `1` (number, optional) - Refresh the client and organization data.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /invoices/{id}/void
Void **Invoice**

+ Parameters
    + id: `1` (number, required) - ID of an **Invoice** to void.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /invoices/{id}/pay-with-credit-card/{creditCardId}
Pay **Invoice** with a **Credit Card**

+ Parameters
    + id: `1` (number, required) - ID of an **Invoice** to pay.
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to use.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET /invoices/{id}/pdf

Retrieve **Invoice** PDF.

+ Parameters
    + id: `1000` (number, required) - ID of a **Invoice** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/pdf)


+ Response 404 (application/json)
    + Attributes (Error404)


# Group Invoice Items

# /invoices/items/{id}

To create a new **Invoice Item** see [Update Invoice](#reference/invoices/invoicesid/patch).

## GET

Retrieve **Invoice Item**.

+ Parameters
    + id: `1` (number, required) - ID of a **Invoice Item** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (InvoiceItemReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update product or custom **Invoice Item**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Invoice Item** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (InvoiceItemWritable)

+ Response 200 (application/json)
    + Attributes (InvoiceItemReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Invoice Item**.

+ Parameters
    + id: `1` (number, required) - ID of a **Invoice Item** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

# /invoices/{id}/items
## GET

Retrieve collection of **Invoice Items** filtered by **Invoice**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Invoice**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[InvoiceItemReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Job Attachments

## GET /scheduling/jobs/attachments{?jobId}

Retrieve collection of **Job Attachments**.

+ Parameters
    + jobId: `1` (number, optional) - ID of a **Job** to filter by.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[JobAttachmentReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /scheduling/jobs/attachments

Create **Job Attachment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobAttachmentWritable)

+ Response 201 (application/json)
    + Headers
            Location: /scheduling/jobs/attachments/1001
    + Attributes (JobAttachmentReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /scheduling/jobs/attachments/{id}

## GET

Retrieve **Job Attachment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Attachment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (JobAttachmentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Job Attachment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Attachment** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobAttachmentWritable)

+ Response 200 (application/json)
    + Attributes (JobAttachmentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Job Attachment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Attachment** to delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /scheduling/jobs/attachments/{id}/file

Retrieve **Job Attachment** file.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Attachment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (*/*)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Job Comments

## GET /scheduling/jobs/comments{?jobId,userId,createdDateFrom,createdDateTo}

Retrieve collection of **Job Comments**.

+ Parameters
    + jobId: `1` (number, optional) - ID of a **Job** to filter by.

    + userId: `1` (number, optional) - ID of a **User** to filter by.

    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.

    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[JobCommentReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /scheduling/jobs/comments

Create **Job Comment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobCommentWritable)

+ Response 201 (application/json)
    + Headers
            Location: /scheduling/jobs/comments/1001
    + Attributes (JobCommentReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /scheduling/jobs/comments/{id}
## GET

Retrieve **Job Comment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Comment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (JobCommentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Job Comment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Comment** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobCommentWritable)

+ Response 200 (application/json)
    + Attributes (JobCommentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Job Comment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Comment** to delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Job Tasks

## GET /scheduling/jobs/tasks{?jobId}

Retrieve collection of **Job Tasks**.
+ Parameters
    + jobId: `1` (number, required) - ID of a **Job** to filter by

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[JobTaskReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /scheduling/jobs/tasks

Create **Job Task**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobTaskWritable)

+ Response 201 (application/json)
    + Headers
            Location: /scheduling/jobs/tasks/1001
    + Attributes (JobTaskReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /scheduling/jobs/tasks/{id}
## GET

Retrieve **Job Task**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Task** to fetch

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (JobTaskReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Job Task**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Task** to update

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobTaskWritable)

+ Response 200 (application/json)
    + Attributes (JobTaskReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Job Task**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job Task** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Jobs

## GET /scheduling/jobs{?clientId,assignedUserId,ticketId,dateFrom,dateTo,statuses%5B%5D,query,limit,offset}

Retrieve collection of **Jobs**.

+ Parameters
    + clientId: `1` (number | string, optional) - ID of a **Client** to filter by. Use string "null" to filter jobs with unassigned **Client**.
    + assignedUserId: `1` (number | string, optional) - ID of a **User** to filter by. Use string "null" to filter jobs with unassigned **User**.
    + ticketId: `1` (number | string, optional) - ID of a **Ticket** to filter by. Use string "null" to filter jobs with unassigned **Ticket**.
    + dateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + dateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + statuses%5B%5D: `1` (array[JobStatus], optional) - Filter by job status.
    + query: `John Doe` (string, optional) - A search query of at least two characters.
    + limit: `15` (number, optional) - Result limit.
    + offset: `30` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[JobReadOnly])

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /scheduling/jobs

Create **Job**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobWritable)

+ Response 201 (application/json)
    + Headers
            Location: /scheduling/jobs/1001
    + Attributes (JobReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## /scheduling/jobs/{id}
## GET

Retrieve **Job**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (JobReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Job**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (JobWritable)

+ Response 200 (application/json)
    + Attributes (JobReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Job**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Job** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Organizations

## GET /organizations

Retrieve collection of **Organizations**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[OrganizationReadOnly])

## POST /organizations
Create **Organization**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Organization)

+ Response 201 (application/json)
    + Headers
            Location: /organizations/1001
    + Attributes (OrganizationReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET /organizations/{id}

Retrieve **Organization**.

+ Parameters
    + id: `1` (number, required) - ID of an **Organization** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (OrganizationReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /organizations/{id}

Update **Organization**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Organization** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Organization)

+ Response 200 (application/json)
    + Attributes (OrganizationReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE /organizations/{id}

Delete **Organization**

+ Parameters
    + id: `1000` (number, required) - ID of a **Organization** to permanently delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /organizations/{id}/next-invoice-number

Retrieve **next invoice number** for the given organization.

+ Parameters
    + id: `1` (number, required) - ID of an **Organization** to use.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + invoiceNumber: `1234` (string) - number of the next invoice

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /organizations/{id}/next-proforma-invoice-number

Retrieve **next proforma invoice number** for the given organization.

+ Parameters
    + id: `1` (number, required) - ID of an **Organization** to use.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + proformaInvoiceNumber: `4567` (string) - number of the next proforma invoice

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /organizations/{id}/next-quote-number

Retrieve **next quote number** for the given organization.

+ Parameters
    + id: `1` (number, required) - ID of an **Organization** to use.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + quoteNumber: `3141` (string) - number of the next quote

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Payment Methods

# /payment-methods{?visible,isSystem}

## GET

Retrieve collection of **Payment Methods**.

+ Parameters
    + visible: `1` (number, optional) - Filter visible payment methods.
    + isSystem: `1` (number, optional) - Filter system payment methods.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[PaymentMethodReadOnly])

# /payment-methods

## POST

Create **Payment Method**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PaymentMethod)

+ Response 201 (application/json)
    + Headers
            Location: /payment-methods/2
    + Attributes (PaymentMethodReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /payment-methods/{id}


## GET

Retrieve **Payment Method**.

+ Parameters
    + id: `6efe0fa8-36b2-4dd1-b049-427bffc7d369` (string, required) - UUID of a **Payment Method** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentMethodReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


## PATCH

Update **Payment Method**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Payment Method** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PaymentMethod)

+ Response 200 (application/json)
    + Attributes (PaymentMethodReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## DELETE

Delete **Payment Method**.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment Method** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Payment Plans

# /payment-plans

## GET

Retrieve collection of **Payment Plans**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + active: `1` (number, optional) - Filter active plans.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[PaymentPlanReadOnly])

## POST

Create **Payment Plan**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PaymentPlan)

+ Response 201 (application/json)
    + Headers
            Location: /payment-plans/2
    + Attributes (PaymentPlanReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /payment-plans/{id}

## GET

Retrieve **Payment Plan**.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment Plan** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentPlanReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

# /payment-plans/{id}/cancel
## PATCH

Cancel **Payment Plan**.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment Plan** to cancel.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentPlanReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# Group Payments

## GET /payments{?createdDateFrom,createdDateTo,clientId,unattached,query,customAttributeKey,customAttributeValue,limit,offset,order,direction}
Retrieve collection of **Payments**.

+ Parameters
    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + unattached: `1` (number, optional) - Filter unattached payments.
    + customAttributeKey: `myCustomAttribute` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeValue.
    + customAttributeValue: `value` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeKey.
    + query: `John Doe` (string, optional) - A search query of at least two characters.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `createdDate` (enum[string], optional) - By what property is the result ordered by.
        + Default: `createdDate`
        + Members
            + `id`
            + `createdDate`
            + `amount`
    + direction: `ASC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[PaymentReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /payments
Create **Payment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PaymentWritable)

+ Response 201 (application/json)
    + Headers
            Location: /payments/2
    + Attributes (PaymentReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /payments/{id}
## GET
Retrieve **Payment**.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE
Delete **Payment**.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Payment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Payment** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PaymentUpdate)

+ Response 200 (application/json)
    + Attributes (PaymentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /payments/{id}/send-receipt
Send payment receipt to client.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /payments/{id}/attach
Attach **Payment** to a client.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PaymentMatch)

+ Response 200 (application/json)
    + Attributes (PaymentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /payments/{id}/detach
Detach **Payment** from a client.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET /payments/{id}/pdf

Retrieve **Payment** Receipt PDF.

+ Parameters
    + id: `1000` (number, required) - ID of a **Payment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/pdf)


+ Response 404 (application/json)
    + Attributes (Error404)


# Group Payment Tokens

## GET /payment-tokens/{token}

Retrieve **PaymentToken**.

+ Parameters
    + token: `01b0ae1bd2998ce7e5d65e4a33b7955d` (required) - token string of a **PaymentToken** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PaymentTokenReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

# Group Credit Cards

#/credit-cards{?limit,offset,clientId}
##GET

Retrieve collection of **Credit Cards**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[CreditCardReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/credit-cards/{id}
## GET

Retrieve **Credit Card**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CreditCardReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Remove **Credit Card**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to remove.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes
        + code: `200` (number)
        + message: `Credit card removed.` (string)

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

#/credit-cards/{id}/settings
## PATCH

Update settings of **Credit Card**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to update.

+ Request (application/json)
    + Attributes
        + useForAutopay: `true` (boolean)
        + monthlyLimit: `1000.0` (number)

+ Response 200 (application/json)
    + Attributes (CreditCardReadOnly)

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Permission Groups

## GET /permission-groups

Retrieve collection of **Permission Groups**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[PermissionGroup])

## GET /permission-groups/{id}

Retrieve **Permission Group**.

+ Parameters
    + id: `1` (number, required) - ID of a **Permission Group** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PermissionGroup)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Products

# /products
## GET

Retrieve collection of **Products**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ProductReadOnly])

## POST

Create **Product**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Product)

+ Response 201 (application/json)
    + Headers
            Location: /products/2
    + Attributes (ProductReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /products/{id}
## PATCH

Update **Product**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Product** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Product)

+ Response 200 (application/json)
    + Attributes (ProductReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Product**.

+ Parameters
    + id: `1` (number, required) - ID of a **Product** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ProductReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Product**.

+ Parameters
    + id: `1` (number, required) - ID of a **Product** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Proforma Invoice Templates

## GET /proforma-invoice-templates
Retrieve collection of **Proforma Invoice Templates**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ProformaInvoiceTemplateReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /proforma-invoice-templates/{id}
Retrieve **Proforma Invoice Template**.

+ Parameters
    + id: `1` (number, required) - ID of an **Proforma Invoice Template** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ProformaInvoiceTemplateReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Quote Templates

## GET /quote-templates
Retrieve collection of **Quote Templates**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[QuoteTemplateReadOnly])

## GET /quote-templates/{id}
Retrieve **Quote Template**.

+ Parameters
    + id: `1` (number, required) - ID of an **Quote Template** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (QuoteTemplateReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Quotes

## GET /quotes{?clientId,createdDateFrom,createdDateTo,statuses%5B%5D,number,query,limit,offset,order,direction}
Retrieve collection of **Quotes**.

+ Parameters
    + clientId: `1` (number, optional) - ID of a **Client** to filter by.
    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + statuses%5B%5D: `0` (array[QuoteStatus], optional) - Filter by quote status.
    + number: `QN20171030` (string, optional) - Search by quote number.
    + query: `John Doe` (string, optional) - A search query of at least two characters.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `clientLastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `clientFirstName`
            + `clientLastName`
            + `createdDate`
            + `number`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[QuoteReadOnly])

+ Response 400 (application/json)
    + Attributes (Error400)

# /quotes/{id}
## GET
Retrieve **Quote**.

+ Parameters
    + id: `1` (number, required) - ID of an **Quote** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (QuoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH
Update **Quote**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Quote** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (QuoteUpdate)

+ Response 200 (application/json)
    + Attributes (QuoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## POST /clients/{id}/quotes
Create **Quote** for **Client**.

+ Parameters
    + id: `1` (number, required) - ID of a **Client** to create **Quote** for.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (QuoteNew)

+ Response 201 (application/json)
    + Headers
            Location: /clients/quotes/2
    + Attributes (QuoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET /quotes/{id}/pdf

Retrieve **Quote** PDF.

+ Parameters
    + id: `1000` (number, required) - ID of a **Quote** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/pdf)


+ Response 404 (application/json)
    + Attributes (Error404)


# Group Refund

## GET /refunds{?createdDateFrom,createdDateTo,clientId,query,limit,offset}
Retrieve collection of **Refunds**.

+ Parameters
    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + clientId: `1` (number, optional) - ID of an **Client** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[RefundReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /refunds
Create **Refund**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Refund)

+ Response 201 (application/json)
    + Headers
            Location: /refunds/2
    + Attributes (RefundReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET /refunds/{id}
Retrieve **Refund**.

+ Parameters
    + id: `1` (number, required) - ID of a **Refund** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (RefundReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE /refunds/{id}
Delete **Refund**.

+ Parameters
    + id: `1` (number, required) - ID of a **Refund** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Service Plans

# /service-plans{?public,servicePlanType,servicePlanGroupId}

## GET

Retrieve collection of **Service Plans**.

+ Parameters
    + public: `1` (number, optional) - Filter public service plans.
    + prepaid: `1` (number, optional) - Filter prepaid service plans.
    + servicePlanType: `internet` (enum[string], optional) - Select only service plans of given type.
        + Members
            + `internet`
            + `general`
    + servicePlanGroupId: `4d9e206c-ba3b-4893-9cf7-fb08a778f00d` (string, optional) - UUID of a **Service Plan Group** to filter by.
    + archived: `1` (number, optional) - Return archived service plans.
        + Default: `0`
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TariffReadOnly])

## POST

Create **Service Plan**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Tariff)

+ Response 201 (application/json)
    + Headers
            Location: /tariffs/2
    + Attributes (TariffReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/service-plans/{id}

## PATCH

Update **Service Plan**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service Plan** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Tariff)

+ Response 200 (application/json)
    + Attributes (TariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Service Plan**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service Plan** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Service Plan**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service Plan** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)
    
## PATCH /service-plans/{id}/add-service-plan-group/{groupId}

Add **Service Plan Group** to **Service Plan**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service Plan** to add service plan group to.
    + groupId: `4d9e206c-ba3b-4893-9cf7-fb08a778f00d` (string, required) - UUID of a **Service Plan Group** to add to the service plan.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /service-plans/{id}/remove-service-plan-group/{groupId}

Remove **Service Plan Group** from **Service Plan**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service Plan** to remove tag from.
    + groupId: `4d9e206c-ba3b-4893-9cf7-fb08a778f00d` (string, required) - UUID of a **Service Plan Group** to remove from the service plan.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


## GET /service-plans/by-period-id/{periodId}

Retrieve **Service Plan** by period ID.

+ Parameters
    + periodId: `1` (number, required) - ID of a **Service Plan Period**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

# /service-plans/statistics

## GET

Retrieve Service Plans statistics.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TariffStatistics])


# Group Service Plan Groups

## GET /service-plan-groups{?limit,offset}

Retrieve collection of **Service Plan Group**s.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    
+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ServicePlanGroupReadOnly])

## POST /service-plan-groups

Create **Service Plan Group**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServicePlanGroup)

+ Response 201 (application/json)
    + Headers
            Location: /service-plan-groups/faafe1e3-0258-424d-a0ce-e85252bb9756
    + Attributes (ServicePlanGroupReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/service-plan-groups/{id}

## PATCH

Update **Service Plan Group**.

+ Parameters
    + id: `faafe1e3-0258-424d-a0ce-e85252bb9756` (string, required) - UUID of a **Service Plan Group** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServicePlanGroup)

+ Response 200 (application/json)
    + Attributes (ServicePlanGroupReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Service Plan Group**.

+ Parameters
    + id: `faafe1e3-0258-424d-a0ce-e85252bb9756` (string, required) - UUID of a **Service Plan Group** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServicePlanGroupReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Service Plan Group**.

+ Parameters
    + id: `faafe1e3-0258-424d-a0ce-e85252bb9756` (string, required) - UUID of a **Service Plan Group** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Service Surcharges

# /clients/services/{id}/service-surcharges

## GET

Retrieve collection of **Service Surcharges** filtered by **Service**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Service**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ServiceSurchargeReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST
Create **Service Surcharge** for **Service**.

+ Parameters
    + id: `1` (number, required) - ID of a related **Service**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceSurcharge)

+ Response 201 (application/json)
    + Headers
            Location: /clients/services/service-surcharges/2
    + Attributes (ServiceSurchargeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /clients/services/service-surcharges/{id}
## PATCH

Update **Service Surcharge**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service Surcharge** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceSurcharge)

+ Response 200 (application/json)
    + Attributes (ServiceSurchargeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Service Surcharge**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service Surcharge** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceSurchargeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Service Surcharge**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service Surcharge** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Service Suspension

# PATCH /clients/services/{id}/suspend

Suspend **Service** for **Suspend Reason**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to suspend.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceSuspend)

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# PATCH /clients/services/{id}/cancel-suspend

Stop **Service** suspension.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to cancel suspend.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /service-suspension-reasons
## GET

Retrieve collection of **Service Suspension Reasons**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ServiceSuspensionReasonReadOnly])

## POST

Create **Service Suspension Reason**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceSuspensionReason)

+ Response 201 (application/json)
    + Headers
            Location: /service-suspension-reasons/2
    + Attributes (ServiceSuspensionReasonReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /service-suspension-reasons/{id}
## PATCH

Update **Service Suspension Reason**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service Suspension Reason** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceSuspensionReason)

+ Response 200 (application/json)
    + Attributes (ServiceSuspensionReasonReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Service Suspension Reason**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service Suspension Reason** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceSuspensionReasonReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Service Suspension Reason**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service Suspension Reason** to delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Service UISP Location

# /clients/services/{id}/unmsclientsite/bind
## PATCH

Connect **Service** to UISP Location.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceUnmsClientSite)

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /clients/services/{id}/unmsclientsite/unbind
## PATCH

Remove connection of **Service** to UISP Location.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)



# Group Services

# GET /clients/services{?organizationId,clientId,statuses%5B%5D,prepaid,hasOutage,customAttributeId,customAttributeValue,limit,offset}

Retrieve collection of **Services**.

+ Parameters
    + organizationId: `1` (number, optional) - ID of an **Organization** to filter by.
    + clientId: `1` (number, optional) - ID of an **Client** to filter by.
    + statuses%5B%5D: `1` (array[ServiceStatus], optional) - Filter by service status.
    + prepaid: `1` (number, optional) - Filter prepaid services.
    + hasOutage: `1` (number, optional) - Filter services by the outage.
    + customAttributeId: `1` (number, optional) - ID of an **Custom attribute**. Only services that have some value for this attribute will be returned. See [Custom Attributes](#reference/custom-attributes/custom-attributesattributetype/get). **This field is deprecated**. Use combination of customAttributeKey and customAttributeValue instead.
    + customAttributeKey: `myCustomAttribute` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeValue.
    + customAttributeValue: `1` (string, optional) - Search by custom attribute. Have to be used alongside with customAttributeId or customAttributeKey.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ServiceReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)


# /clients/services/{id}
## GET

Retrieve **Service**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


## PATCH

Update **Service**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (ServiceUpdate)

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## DELETE

Delete **Service**

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## /clients/{id}/services
## POST

Create **Service** for **Client**.

+ Parameters
    + id: `1000` (number, required) - ID of a related **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceWritable)

+ Response 201 (application/json)
    + Headers
            Location: /clients/services/2
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


## /clients/services/{id}/data-usage/{datetime}
## GET

Retrieve **Service** usage data for the period starting on the day given in date.
Available only for *recurring* services.

+ Parameters
    + id: `1000` (number, required) - ID of the **Service**.
    + datetime: `2017-09-12T00:00:00+0000` (string, required) - Date string in the following format (see example): starting day of period.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceUsageReadonly)

+ Response 404 (application/json)
    + Attributes (Error404)

## /clients/services/{id}/geocode
## PATCH

Automatically geocode **Service's** address and update GPS coordinates with the result.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to geocode.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## /clients/services/{id}/end
## PATCH

End **Service** immediately. Current day will not be invoiced.
Available only for *recurring* services.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to end.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## /clients/services/{id}/activate-quoted
## PATCH

Activates quoted **Service**. Client lead will be automatically converted to regular client (if not already).
Available only for *recurring* services.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to activate.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (ServiceActivate)

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## /clients/services/{id}/traffic-shaping-override
## PATCH

Enables traffic shaping override on a **Service**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString
    + Attributes (ServiceTrafficShapingOverride)

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Disables traffic shaping override on a **Service**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

# PATCH /clients/services/{id}/pause

Pause **Service** for a given date range. The pause can be canceled by [Cancel deferred change](#reference/services/clientsservicesidcancel-deferred-change/patch).

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to pause.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServicePause)

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# PATCH /clients/services/{id}/cancel-deferred-change

Cancel **Service** deferred change.

+ Parameters
    + id: `1000` (number, required) - ID of a **Service** to cancel deferred change.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# Group Service Change Requests

## GET /service-change-requests{?limit,offset}

Retrieve collection of **Service Change Requests**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ServiceChangeRequestReadOnly])

## POST /service-change-requests

Create **Service Change Request**. Available only for *recurring* services.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ServiceChangeRequest)

+ Response 201 (application/json)
    + Headers
            Location: /service-change-requests/d750c31c-339d-4291-b199-762c187871f8
    + Attributes (ServiceChangeRequestReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /service-change-requests/{id}
## GET

Retrieve **Service Change Request**.

+ Parameters
    + id: `d750c31c-339d-4291-b199-762c187871f8` (string, required) - UUID of a **Service Change Request** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ServiceChangeRequestReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Service Change Request**.

+ Parameters
    + id: `d750c31c-339d-4291-b199-762c187871f8` (string, required) - UUID of a **Service Change Request** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /service-change-requests/{id}/accept

Accept **Service Change Request**.
This will delete the change request and create a deferred change for the service to be applied at the start of next uninvoiced period in the future.
For example when handling a backward invoicing type, the current month can be uninvoiced, but the deferred change will be created for the next month.

+ Parameters
    + id: `d750c31c-339d-4291-b199-762c187871f8` (string, required) - UUID of a **Service Change Request** to accept.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)


# Group Prepaid Service Periods

## GET /prepaid-service-periods{?serviceId,limit,offset,order,direction,createdDateFrom,createdDateTo,startDateFrom,startDateTo,endDateFrom,endDateTo}

Retrieve collection of **Prepaid Service Periods**.

+ Parameters
    + serviceId: `1` (number, optional) - ID of a **Service** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + createdDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
        Use `YYYY-MM-DD` format.
    + createdDateTo: `2017-01-01` (string, optional) -  Limit collection to date (including).
        Use `YYYY-MM-DD` format.
    + startDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
        Use `YYYY-MM-DD` format.
    + startDateTo: `2017-01-01` (string, optional) -  Limit collection to date (including).
        Use `YYYY-MM-DD` format.
    + endDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
        Use `YYYY-MM-DD` format.
    + endDateTo: `2017-01-01` (string, optional) -  Limit collection to date (including).
        Use `YYYY-MM-DD` format.
    + order: `createdDate` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `createdDate`
            + `startDate`
            + `endDate`
    + direction: `ASC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[PrepaidServicePeriodReadOnly])

## POST /prepaid-service-periods

Create **Prepaid Service Period**. Available only for *prepaid* services.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (PrepaidServicePeriodCreate)

+ Response 201 (application/json)
    + Headers
            Location: /prepaid-service-periods/04a80595-0109-4ca4-9230-afd8d584d761
    + Attributes (PrepaidServicePeriodReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /prepaid-service-periods/{id}
## GET

Retrieve **Prepaid Service Period**.

+ Parameters
    + id: `04a80595-0109-4ca4-9230-afd8d584d761` (string, required) - UUID of a **Prepaid Service Period** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (PrepaidServicePeriodReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Prepaid Service Period**.

+ Parameters
    + id: `04a80595-0109-4ca4-9230-afd8d584d761` (string, required) - UUID of a **Prepaid Service Period** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Surcharges

# /surcharges
## GET

Retrieve collection of **Surcharges**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[SurchargeReadOnly])

## POST

Create **Surcharge**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Surcharge)

+ Response 201 (application/json)
    + Headers
            Location: /surcharges/2
    + Attributes (SurchargeReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /surcharges/{id}
## PATCH

Update **Surcharge**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Surcharge** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Surcharge)

+ Response 200 (application/json)
    + Attributes (SurchargeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Surcharge**.

+ Parameters
    + id: `1` (number, required) - ID of a **Surcharge** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (SurchargeReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Surcharge**.

+ Parameters
    + id: `1` (number, required) - ID of a **Surcharge** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Taxes

# /taxes
## GET

Retrieve collection of **Taxes**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TaxReadOnly])

## POST

Create **Tax**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Tax)

+ Response 201 (application/json)
    + Headers
            Location: /taxes/2
    + Attributes (TaxReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/taxes/{id}
## GET

Retrieve **Tax**.

+ Parameters
    + id: `1` (number, required) - ID of a **Tax** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TaxReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Tax**.

+ Parameters
    + id: `1` (number, required) - ID of a **Tax** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)



# Group Products

# /products
## GET

Retrieve collection of **Products**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ProductReadOnly])

## POST

Create **Product**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Product)

+ Response 201 (application/json)
    + Headers
            Location: /products/2
    + Attributes (ProductReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /products/{id}
## PATCH

Update **Product**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Product** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (Product)

+ Response 200 (application/json)
    + Attributes (ProductReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Product**.

+ Parameters
    + id: `1` (number, required) - ID of a **Product** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ProductReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Product**.

+ Parameters
    + id: `1` (number, required) - ID of a **Product** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Tickets

## GET /ticketing/tickets{?clientId,assignedUserId,assignedGroupId,dateFrom,dateTo,statuses%5B%5D,public,query,ticketTagIds%5B%5D,limit,offset,order,direction}

Retrieve collection of **Tickets**.

+ Parameters
    + clientId: `1` (number | string, optional) - ID of a **Client** to filter by. Use string "null" to filter tickets with unassigned **Client**.
    + assignedUserId: `1` (number | string, optional) - ID of a **User** to filter by. Use string "null" to filter tickets with unassigned **User**.
    + assignedGroupId: `1` (number | string, optional) - ID of a **TicketGroup** to filter by. Use string "null" to filter tickets with unassigned **TicketGroup**.
    + dateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.
    + dateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.
    + statuses%5B%5D: `1` (enum[TicketStatus], optional) - Filter by ticket status.
    + public: `1` (number, optional) - Filter public ticket.
    + query: `John Doe` (string, optional) - A search query of at least two characters.
    + ticketTagIds%5B%5D: `b868188b-264e-4c42-bc90-e75f4b1a129f` (string, optional) - Filter by ticket tags.
    + limit: `15` (number, optional) - Result limit.
    + offset: `30` (number, optional) - Index of first result.
    + order: `lastActivity` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdAt`
        + Members
            + `createdAt`
            + `id`
            + `lastActivity`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TicketReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /ticketing/tickets

Create **Ticket**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketWritable)

+ Response 201 (application/json)
    + Headers
            Location: /ticketing/tickets/1001
    + Attributes (TicketReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)


# /ticketing/tickets/{id}
## GET

Retrieve **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketPatch)

+ Response 200 (application/json)
    + Attributes (TicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /ticketing/tickets/{id}/add-job/{jobId}

Add **Job** to **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to add job to.
    + jobId: `1000` (number, required) - ID of a **Job** to add to the ticket.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /ticketing/tickets/{id}/remove-job/{jobId}

Remove **Job** from **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to remove job from.
    + jobId: `1000` (number, required) - ID of a **Job** to remove from the client.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /ticketing/tickets/{id}/add-tag/{tagId}

Add **Ticket Tag** to **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to add tag to.
    + tagId: `b793d381-640f-4654-a24d-735b3058c516` (string, required) - UUID of a **Ticket Tag** to add to the ticket.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH /ticketing/tickets/{id}/remove-tag/{tagId}

Remove **Ticket Tag** from **Ticket**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket** to remove tag from.
    + tagId: `c7548c47-f61f-4e81-aa44-d5921282eba7` (string, required) - UUID of a **Ticket Tag** to remove from the ticket.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Ticket Activity

## GET /ticketing/tickets/activities{?ticketId,userId,createdDateFrom,createdDateTo}

Retrieve collection of **Ticket Activities**.

+ Parameters
    + ticketId: `1` (number, optional) - ID of a **Ticket** to filter by.

    + userId: `1` (number, optional) - ID of a **User** to filter by.

    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.

    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TicketActivityReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /ticketing/tickets/activities/{id}

Retrieve **Ticket Activity**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket Activity** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketActivityReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Ticket Comment Attachments

## GET /ticketing/tickets/comments/attachments/{id}/file

Retrieve **Ticket Comment Attachment** file resource.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket Comment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (*/*)


+ Response 404 (application/json)
    + Attributes (Error404)


# Group Ticket Comments

## GET /ticketing/tickets/comments{?ticketId,userId,createdDateFrom,createdDateTo}

Retrieve collection of **Ticket Comments**.

+ Parameters
    + ticketId: `1` (number, optional) - ID of a **Ticket** to filter by.

    + userId: `1` (number, optional) - ID of a **User** to filter by.

    + createdDateFrom: `2016-01-01` (string, optional) -  Limit collection from date (including).
    Use `YYYY-MM-DD` format.

    + createdDateTo: `2016-12-31` (string, optional) -  Limit collection to date (including).
    Use `YYYY-MM-DD` format.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TicketCommentReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST /ticketing/tickets/comments

Create **Ticket Comment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketCommentWritable)

+ Response 201 (application/json)
    + Headers
            Location: /ticketing/tickets/comments/1001
    + Attributes (TicketCommentReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET /ticketing/tickets/comments/{id}

Retrieve **Ticket Comment**.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket Comment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketCommentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Ticket Groups

# /ticketing/ticket-groups

## GET

Retrieve collection of **Ticket Groups**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TicketGroupReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

## POST

Create **Ticket Group**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketGroup)

+ Response 201 (application/json)
    + Headers
            Location: /ticketing/ticket-groups/1001
    + Attributes (TicketGroupReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

# /ticketing/ticket-groups/{id}

## GET

Retrieve **Ticket Group**.

+ Parameters
    + id: `5` (number, required) - ID of a **Ticket Group** to fetch / update

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketGroupReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## PATCH

Update **Ticket Group**.

+ Parameters
    + id: `5` (number, required) - ID of a **Ticket Group** to fetch / update

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketGroup)

+ Response 200 (application/json)
    + Attributes (TicketGroupReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## DELETE

Delete **Ticket Group**.

+ Parameters
    + id: `5` (number, required) - ID of a **Ticket Group** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Ticket Tags

# /ticket-tags

## GET

Retrieve collection of **Ticket Tags**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[TicketTagReadOnly])

## POST

Create **Ticket Tag**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketTag)

+ Response 201 (application/json)
    + Headers
            Location: /ticket-tags/2
    + Attributes (TicketTagReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/ticket-tags/{id}

## PATCH

Update **Ticket Tag**.

+ Parameters
    + id: `cd089e0f-e332-4427-b7cf-f1a9bf593b02` (string, required) - UUID of a **Ticket Tag** to fetch / update.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (TicketTag)

+ Response 200 (application/json)
    + Attributes (TicketTagReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

## GET

Retrieve **Ticket Tag**.

+ Parameters
    + id: `cd089e0f-e332-4427-b7cf-f1a9bf593b02` (string, required) - UUID of a **Ticket Tag** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (TicketTagReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Ticket Tag**.

+ Parameters
    + id: `cd089e0f-e332-4427-b7cf-f1a9bf593b02` (string, required) - UUID of a **Ticket Tag** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Users

## GET /users/admins/me

Retrieve the current **User**. Returns 404, when the **User** cannot be determined from used app key.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (User)
    
+ Response 404 (application/json)
    + Attributes (Error404)

## GET /users/admins

Retrieve collection of **Users** with admin role.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[User])

## GET /users/admins/{id}

Retrieve **User**.

+ Parameters
    + id: `1` (number, required) - ID of a **User** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (User)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE /users/admins/{unmsId}

Delete **User**.

+ Parameters
    + unmsId: `2c6984d7-03ae-4e48-a212-7b99378837a2` (string, required) - UISP UUID of a **User** to delete.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Webhook Events

## GET /webhook-events/{uuid}

Retrieve **Webhook Event**.

+ Parameters
    + uuid: `2c6984d7-03ae-4e48-a212-7b99378837a2` (string, required) - UUID of a **Webhook Event** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (WebhookEvent)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group General

Resources related to general questions in the API.

## GET /version

Retrieve currently installed CRM version.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (Version)

## GET /options

Retrieve some global CRM options.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (Options)

## GET /countries

Retrieve collection of **Countries**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[CountryReadOnly])

## GET /countries/{id}

Retrieve **Country**.

+ Parameters
    + id: `1` (number, required) - ID of a **Country** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CountryReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /currencies

Retrieve collection of **Currencies**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[CurrencyReadOnly])

## GET /currencies/{id}

Retrieve **Currency**.

+ Parameters
    + id: `1` (number, required) - ID of a **Currency** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (CurrencyReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /countries/states{?countryId}
Retrieve collection of **States**.

Available for **United States** and **Canada** only.

+ Parameters
    + countryId: `249` (number, optional) - ID of a **Country** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[StateReadOnly])

## GET /countries/states/{id}

Retrieve **State**.

+ Parameters
    + id: `1` (number, required) - ID of a **State** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (StateReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Mobile

## GET /mobile/dashboard/overview

Retrieve overview data for the default organization.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (object)
        + organization (object)
            + id: `1` (number)
            + name: `UBNT` (string)
            + currencyCode: `USD` (string)
        + overview (object)
            + clientCount: `10` (number)
            + clientLeadCount: `10` (number)
            + clientSuspendedCount: `10` (number, nullable) Null if suspension feature is disabled, int otherwise.
            + clientOverdueCount: `10` (number)
        + invoicingOverview (object)
            + totalDue: `850.1` (number)
            + totalOverdue: `243.0` (number)
            + invoicedThisMonth: `547.8` (number)
            + invoicedThisMonthUnpaid: `257.4` (number)


## GET /mobile/dashboard/last-week-traffic

Retrieve traffic data for last 7 days.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (object)
        + downloadTotal: `46746846` (number)
        + uploadTotal: `1586453` (number)
        + chart (array[TrafficItem])

## GET /mobile/dashboard/payment-summary{?month,year,organizationId}

Retrieve payment summary for given month of year - calculated sum of accumulated payments for each day of month.
Accumulation of payments means the sum of payments from the first day of the month till current day. I.e. each day is
calculated like sum of payments in this day plus sum of previous payments from the start of the month. Future days
values are null. If organization identifier is defined, payment summary is calculated only for payments attached to
this organization. Otherwise both attached payments of all organizations and unattached payments are included.

+ Parameters
    + month: `9` (number, required) - Payment summary month period.
    + year: `2019` (number, required) - Payment summary year period.
    + organizationId: `2` (number, optional) - ID of an **Organization** to filter by.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[PaymentSummary])

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /mobile/invoices{?statuses%5B%5D,overdue,limit,offset,order,direction}

Retrieve summary data about invoices.

+ Parameters
    + statuses%5B%5D: `1` (array[InvoiceStatus], optional) - Filter by invoice status
    + overdue: `1` (number, optional) - If set to 1, only overdue invoices are returned
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `clientLastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `clientFirstName`
            + `clientLastName`
            + `createdDate`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Response 200 (application/json)
    + Attributes (array[InvoiceSummary])

+ Response 400 (application/json)
    + Attributes (Error400)

## GET /mobile/invoices/counts-by-status

Retrieves counts of all invoices divided by status.

+ Response 200 (application/json)
    + Attributes (array[InvoicesStatusCounts])

## GET /mobile/clients{?overdue,suspended,outage,lead,limit,offset,order,direction}

Retrieve summary data about clients.

+ Parameters
    + overdue: `1` (number, optional) - Filter clients with overdue invoice.
    + suspended: `1` (number, optional) - Filter clients with suspended service.
    + outage: `1` (number, optional) - Filter clients with ongoing outage.
    + lead: `1` (number, optional) - Filter client leads.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + order: `user.lastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `client.id`
        + Members
            + `user.firstName`
            + `user.lastName`
            + `client.registrationDate`
            + `client.id`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`


+ Response 200 (application/json)
    + Attributes (array[ClientSummary])

## GET /mobile/clients/counts-by-status

Retrieves counts of clients grouped by their attributes.

+ Response 200 (application/json)
    + Attributes (array[ClientsStatusCounts])

## GET /mobile/clients/search{?query}

Search clients.

+ Parameters
    + query: `John Doe` (string) - A search query of at least two characters.

+ Response 200 (application/json)
    + Attributes (array[ClientSearchResult])


## GET /mobile/clients/{id}/activity

Get activity log for client.

+ Parameters
    + id: `1` (number) - Client ID.

+ Response 200 (application/json)
    + Attributes (array[ClientActivity])


## Data Structures

### TrafficItem (object)
+ date: `2017-07-11` (string)
Format `YYYY-MM-DD`.
+ upload: `3674686` (number) Number of bytes uploaded
+ download: `158653` (number) Number of bytes download

### InvoiceSummary (object)
+ id: `1` (number)
+ number: `**********`
+ status: `3` (InvoiceStatus)
+ clientFirstName: `John` (string)
+ clientLastName: `Doe` (string)
+ clientCompanyName: `John Doe Company inc.` (string)
+ total: `10.0` (number)
+ amountPaid: `10.0` (number)
+ currencyCode: `USD` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ dueDate: `2016-09-12T00:00:00+0000` (string, nullable) - Date string in the following format (see example).

### ClientSummary (object)
+ id: `1` (number)
+ firstName: `John` (string)
+ lastName: `Doe` (string)
+ companyName: `John Doe Company inc.` (string, nullable)
+ balance: `10.0` (number)
+ currencyCode: `USD` (string)
+ activeServices: (array[ClientServiceSummary])
+ overdueInvoices: (array[ClientInvoiceSummary])
+ hasOverdueInvoice: `false` (boolean)
+ hasSuspendedService: `false` (boolean)
+ hasOutage: `false` (boolean)
+ isLead: `false` (boolean) - if true, this is a lead, otherwise active client

### ClientServiceSummary (object)
+ id: `1` (number)
+ name: `Max` (string)

### ClientInvoiceSummary (object)
+ id: `1` (number)
+ status: `3` (InvoiceStatus)
+ total: `10.0` (number)
+ amountPaid: `10.0` (number)
+ currencyCode: `USD` (string)
+ dueDate: `2016-09-26T00:00:00+0000` (string) - Date string in the following format (see example).

### ClientSearchResult (object)
+ id: `1` (number)
+ firstName: `John` (string)
+ lastName: `Doe` (string)
+ companyName: `John Doe Company inc.` (string, nullable)

### ClientActivity (object)
+ type: `1` (enum[number])
    + Members
        + `1` - Invoice
        + `2` - Payment
        + `3` - Refund
+ id: `1` (number)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ dueDate: `2016-09-12T00:00:00+0000` (string, nullable) - Date string in the following format (see example).
+ amount: `10.0` (number)
+ currencyCode: `USD` (string)
+ invoiceStatus: `1` (InvoiceStatus, nullable)
+ paymentMethod: `1` (PaymentMethod, nullable)
+ refundMethod: `6efe0fa8-36b2-4dd1-b049-427bffc7d369` (string, nullable) - UUID
    See [Retrieve Payment Methods](#reference/payment-methods/payment-methods/get)
+ invoiceNumber: `**********` (string, nullable) - Returns string type if activity is related to invoice otherwise null

### ClientsStatusCounts (object)
+ withOverdueInvoice: `1` (number) - Count of clients with overdue invoice.
+ withOutage: `1` (number) - Count of clients with outage of service.
+ withSuspendedService: `1` (number) - Count of clients with suspended service.
+ clientLeads: `1` (number) - Count of client leads.
+ totalCount: `10` (number) - Count of all clients and leads.

### InvoicesStatusCounts (object)
+ status: `3` (InvoiceStatus) - Invoice status
+ invoicesCount: `1` (number) - Count of invoices with this status.

### PaymentSummary (object)
+ day: `1` (number)
+ value: `100.00` (number)

# Group Client Zone Credit Cards

#/client-zone/credit-cards{?limit,offset}
##GET

Retrieve collection of client's **Credit Cards**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneCreditCardReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/credit-cards/{id}
## GET

Retrieve client's **Credit Card**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneCreditCardReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Remove client's **Credit Card**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to remove.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes
        + code: `200` (number)
        + message: `Credit card removed.` (string)

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/credit-cards/{id}/settings
## PATCH

Update settings of client's **Credit Card**.

+ Parameters
    + id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID of a **Credit Card** to update.

+ Request (application/json)
    + Attributes
        + useForAutopay: `true` (boolean)
        + monthlyLimit: `1000.0` (number)

+ Response 200 (application/json)
    + Attributes (ClientZoneCreditCardReadOnly)

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Dashboard

## GET /client-zone/dashboard
Retrieve overview for authenticated client.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes
        + currencyCode: `USD` (string)
        + accountBalance: `307.8` (number)
        + nextInvoicingDay: `2017-11-13T15:30:27+0000` (string) - Date string in the following format (see example).
        + averageMonthlyPayment: `79.9` (number)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Invoices

#/client-zone/invoices{?limit,offset,overdue,statuses%5B%5D}
##GET

Retrieve collection of client's **Invoices**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + overdue: `1` (number, optional) - If set to 1, only overdue invoices are returned.
    + statuses%5B%5D: `1` (array[InvoiceStatus], optional) - Filter by invoice statuses.
    + order: `clientLastName` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `clientFirstName`
            + `clientLastName`
            + `createdDate`
            + `number`
    + direction: `DESC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneInvoiceReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/invoices/{id}
## GET

Retrieve client's **Invoice**.

+ Parameters
    + id: `1` (number, required) - ID of a **Invoice** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneInvoiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## GET /client-zone/invoices/{id}/pdf

Retrieve **Invoice** PDF.

+ Parameters
    + id: `1000` (number, required) - ID of a **Invoice** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/pdf)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Payments

#/client-zone/payments{?limit,offset}
##GET

Retrieve collection of client's **Payments**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZonePaymentReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/payments/{id}
## GET

Retrieve client's **Payment**.

+ Parameters
    + id: `1` (number, required) - ID of a **Payment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZonePaymentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Quotes

#/client-zone/quotes{?limit,offset}
##GET

Retrieve collection of client's **Quotes**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneQuoteReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/quotes/{id}
## GET

Retrieve client's **Quote**.

+ Parameters
    + id: `1` (number, required) - ID of a **Quote** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneQuoteReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Services

#/client-zone/services{?limit,offset}
##GET

Retrieve collection of client's **Services**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneServiceReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)


#/client-zone/services/{id}
## GET

Retrieve client's **Service**.

+ Parameters
    + id: `1` (number, required) - ID of a **Service** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneServiceReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Service Plans

#/client-zone/service-plans{?serviceId,limit,offset}
## GET

Retrieve collection of public **Service Plans**.

+ Parameters
    + serviceId: `1` (number, optional) - ID of a **Service** to filter by. When given, only service plans available for the service change request on this service will be returned.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneTariffReadOnly])


#/client-zone/service-plans/{id}
## GET

Retrieve public **Service Plan**.

+ Parameters
    + id: `1` (number, required) - ID of a public **Service Plan** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneTariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/service-plans/period/{id}
## GET

Retrieve public **Service Plan** by service plan period ID.

+ Parameters
    + id: `1` (number, required) - ID of service plan period of a public **Service Plan** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneTariffReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Tickets

#/client-zone/tickets{?limit,offset}
##GET

Retrieve collection of client's **Tickets**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneTicketReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/tickets
## POST

Create **Ticket**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientZoneTicketWritable)

+ Response 201 (application/json)
    + Headers
            Location: /ticketing/tickets/1
    + Attributes (ClientZoneTicketReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/client-zone/tickets/{id}
## GET

Retrieve client's **Ticket**.

+ Parameters
    + id: `1` (number, required) - ID of a **Ticket** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneTicketReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Tickets Activities

#/client-zone/tickets/activities{?ticketId,limit,offset}
##GET

Retrieve collection of **Tickets Activities**.

+ Parameters
    + ticketId: `1` (number, optional) - ID of a **Ticket** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneTicketActivityReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/tickets/activities/{id}
## GET

Retrieve **Ticket Activity**.

+ Parameters
    + id: `1` (number, required) - ID of a **Ticket Activity** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneTicketActivityReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Tickets Comments

#/client-zone/tickets/comments{?ticketId,limit,offset}
##GET

Retrieve collection of **Tickets Comments**.

+ Parameters
    + ticketId: `1` (number, optional) - ID of a **Ticket** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneTicketCommentReadOnly])

+ Response 404 (application/json)
    + Attributes (Error404)

#/client-zone/tickets/comments
## POST

Create **Ticket Comment**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientZoneTicketCommentWritable)

+ Response 201 (application/json)
    + Headers
            Location: /ticketing/tickets/comments/1
    + Attributes (ClientZoneTicketCommentReadOnly)

+ Response 422 (application/json)
    + Attributes (Error422)

#/client-zone/tickets/comments/{id}
## GET

Retrieve **Ticket Comment**.

+ Parameters
    + id: `1` (number, required) - ID of a **Ticket Comment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneTicketCommentReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Tickets Comments Attachments

#/client-zone/tickets/comments/attachments/{id}/file
## GET

Retrieve **Tickets Comments Attachments** files resource.

+ Parameters
    + id: `1000` (number, required) - ID of a **Ticket Comment Attachment** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (*/*)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Authentication

## POST /client-zone/login

Get authentication token for client.

+ Request (application/json)
    + Attributes
        + username: `client` (string)
        + password: `pa$$word` (string)
        + expiration: `604800` (number) - Expiration time in seconds, max 604800 (7 days).
        + sliding: `0` (number, optional) - 1 to prolong expiration each time the key is used, 0 otherwise.
        + deviceName: `My Phone Model` (string, optional)
        + twoFactorCode: `123456` (number, optional) - Two-factor code from the authentication app

+ Response 200 (application/json)
    + Attributes
        + authenticationKey: `client-key-01199226-22bc-4767-8f84-fb36e4026c07` (string) - Use it as "X-Auth-App-Key" header to call other client-zone endpoints.

+ Response 401 (application/json)
    + Attributes (Error401)

+ Response 429 (application/json)
    + Attributes (Error429)

## PATCH /client-zone/change-password

Change client password.

+ Request (application/json)
    + Attributes
        + oldPassword: `mySecureOldPa$$word` (string)
        + newPassword: `mySecurePa$$word` (string)

+ Response 200 (application/json)
    + Attributes
        + code: `200` (number)
        + message: `Password has been changed.` (string)

+ Response 422 (application/json)
    + Attributes (Error422)

## PATCH /client-zone/reset-password

Request password reset for client.

+ Request (application/json)
    + Attributes
        + username: `client` (string)

+ Response 200 (application/json)
    + Attributes
        + code: `200` (number)
        + message: `Password reset instructions were sent to client email.` (string)

+ Response 400 (application/json)
    + Attributes (Error400)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 429 (application/json)
    + Attributes (Error429)

## DELETE /client-zone/logout

Deactivate client authentication key.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes
        + code: `200` (number)
        + message: `Authentication key was removed.` (string)


# Group Client Zone Client

## GET /client-zone/client
Retrieve authenticated **Client**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneClient)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Service Change Requests

## GET /client-zone/service-change-requests{?limit,offset}

Retrieve collection of **Service Change Requests**.

+ Parameters
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZoneServiceChangeRequestReadOnly])

## POST /client-zone/service-change-requests

Create **Service Change Request**.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

    + Attributes (ClientZoneServiceChangeRequest)

+ Response 201 (application/json)
    + Headers
            Location: /client-zone/service-change-requests/d750c31c-339d-4291-b199-762c187871f8
    + Attributes (ClientZoneServiceChangeRequestReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# /client-zone/service-change-requests/{id}
## GET

Retrieve **Service Change Request**.

+ Parameters
    + id: `d750c31c-339d-4291-b199-762c187871f8` (string, required) - UUID of a **Service Change Request** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneServiceChangeRequestReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

## DELETE

Delete **Service Change Request**.

+ Parameters
    + id: `d750c31c-339d-4291-b199-762c187871f8` (string, required) - UUID of a **Service Change Request** to delete

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)

+ Response 404 (application/json)
    + Attributes (Error404)


# Group Client Zone Prepaid Service Periods

## GET /client-zone/prepaid-service-periods{?serviceId,limit,offset,order,direction,createdDateFrom,createdDateTo,startDateFrom,startDateTo,endDateFrom,endDateTo}

Retrieve collection of **Prepaid Service Periods**.

+ Parameters
    + serviceId: `1` (number, optional) - ID of a **Service** to filter by.
    + limit: `10` (number, optional) - Result limit.
    + offset: `10` (number, optional) - Index of first result.
    + createdDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
        Use `YYYY-MM-DD` format.
    + createdDateTo: `2017-01-01` (string, optional) -  Limit collection to date (including).
        Use `YYYY-MM-DD` format.
    + startDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
        Use `YYYY-MM-DD` format.
    + startDateTo: `2017-01-01` (string, optional) -  Limit collection to date (including).
        Use `YYYY-MM-DD` format.
    + endDateFrom: `2017-01-01` (string, optional) -  Limit collection from date (including).
        Use `YYYY-MM-DD` format.
    + endDateTo: `2017-01-01` (string, optional) -  Limit collection to date (including).
        Use `YYYY-MM-DD` format.
    + order: `createdDate` (enum[string], optional) - By what property is result ordered by.
        + Default: `createdDate`
        + Members
            + `createdDate`
            + `startDate`
            + `endDate`
    + direction: `ASC` (enum[string], optional) -  Ordering direction.
        + Default: `ASC`
        + Members
            + `ASC`
            + `DESC`

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (array[ClientZonePrepaidServicePeriodReadOnly])

# /client-zone/prepaid-service-periods/{id}
## GET

Retrieve **Prepaid Service Period**.

+ Parameters
    + id: `d750c31c-339d-4291-b199-762c187871f8` (string, required) - UUID of a **Prepaid Service Period** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZonePrepaidServicePeriodReadOnly)

+ Response 404 (application/json)
    + Attributes (Error404)

# Group Client Zone Overview

## GET /client-zone/unifi-overview/{id}
Retrieve overview data for Unifi app.

+ Parameters
    + id: `1` (number, required) - ID of a **Service** to fetch.

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 200 (application/json)
    + Attributes (ClientZoneUnifiOverview)

+ Response 404 (application/json)
    + Attributes (Error404)

# Group Client Zone Login Token

## POST /client-zone/login-token

Creates a client zone login token, which can be used to log into the client zone as currently authenticated client. The token is valid for 30 minutes after creation. 

+ Request (application/json)
    + Headers
            X-Auth-App-Key: appKeyString

+ Response 201 (application/json)
    + Attributes
        + token: `e6a50a70de504450a9cc8452f3c2bdd86b38be6d82d2f84b` (string)
        + url: `https://uisp.example.com/crm/login/token/e6a50a70de504450a9cc8452f3c2bdd86b38be6d82d2f84b.` (string)

+ Response 404 (application/json)
    + Attributes (Error404)

+ Response 422 (application/json)
    + Attributes (Error422)

# Data Structures

## Error400 (object)
+ code: `400` (number) - Error code
+ message: `Bad request.` - Error description message

## Error401 (object)
+ code: `401` (number) - Error code
+ message: `User authentication failed.` - Error description message

## Error404 (object)
+ code: `404` (number) - Error code
+ message: `Object is not found.` - Error description message

## Error422 (object)
+ code: `422` (number) - Error code
+ message: `Validation failed.` - Error description message
+ errors
    + someField: `Some validation error description message.` (array[string]) - Array of validation errors

## Error429 (object)
+ code: `429` (number) - Error code
+ message: `Too Many Requests.` - Error description message

## Version (object)
+ version: `1.0.0`

## Country (object)
+ name: `United States`
+ code: `US` - ISO code

## CountryReadOnly (object)
+ id: `249` (number)
+ Include Country

## Currency (object)
+ name: `Dollars`
+ code: `USD`
+ symbol: `$`

## CurrencyReadOnly (object)
+ id: `33` (number)
+ Include Currency

## State (object)
+ name: `California`
+ code: `CA`

## StateReadOnly (object)
+ id: `5` (number)
+ countryId: `249` (number)
+ Include State

## Client (object)
+ userIdent: `ABC1000` (string) - "Custom ID" in CRM, not to be confused with entity "ID" used in URL
+ previousIsp
+ isLead (boolean) - if true, this is a lead, otherwise active client
    + Default: false
+ clientType (enum[number])
    + Default: `1`
    + Members
        + `1` - Residential
        + `2` - Company
+ companyName - Required in case of type **Company**
+ companyRegistrationNumber
+ companyTaxId
+ companyWebsite
+ companyContactFirstName
+ companyContactLastName
+ firstName: `John` - Required in case of type **Residential**
+ lastName: `Doe` - Required in case of type **Residential**
+ street1: `2580 Orchard Pkwy`
+ street2
+ city: `San Jose`
+ countryId: `249` (number) - If not specified, taken from default organization
    See [Retrieve Countries](#reference/general/countries/get)
+ stateId: `1` (number) - Applicable in case of **United States** and **Canada** only. If not specified, taken from default organization
    See [Retrieve States](#reference/general/countriesidstates/get)
+ zipCode: `95131`
+ fullAddress: `2580 Orchard Pkwy, San Jose, CA, United States of America` (string) - address as resolved by geolocation
+ invoiceStreet1: `2580 Orchard Pkwy`
+ invoiceStreet2
+ invoiceCity: `San Jose`
+ invoiceStateId: `1` (number) - Applicable in case of **United States** and **Canada** only
    See [Retrieve States](#reference/general/countriesidstates/get)
+ invoiceCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ invoiceZipCode: `95131`
+ invoiceAddressSameAsContact (boolean)
    + Default: `false`
+ note
+ sendInvoiceByPost (boolean) - If `null`, system default is used
    Mark client's invoices as to be sent by post
    + Default: `false`
+ invoiceMaturityDays (number) - If `null`, system default is used
    + Default: `14`
+ stopServiceDue (boolean) - If `null`, system default is used
    Suspend client's service in case of overdue invoice
    + Default: `true`
+ stopServiceDueDays (number) - If `null`, system default is used
    Number of days for which suspend is deferred
    For example, if 3 days are set and invoice due date is 17th March suspend will start from 20th March
    + Default: `0`
+ organizationId: `1` (number) - If not specified, default organization will be used
    See [Retrieve Organizations](#reference/organizations/organizations/get)
+ tax1Id: `1` (number) - Will be added by default to each client's taxable services and products
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - Will be added by default to each client's taxable services and products
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - Will be added by default to each client's taxable services and products
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ registrationDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.
+ username: `john.doe` - If null, client zone is disabled
+ avatarColor: `#FFC107` - Color in hexadecimal format. If not specified, will be assigned randomly.
+ addressGpsLat: `37.*************` (number) - Latitude of address location
+ addressGpsLon: `-121.*********113` (number) - Longitude of address location
+ generateProformaInvoices (boolean) - If `null`, system default is used.
    If `true` proforma invoice will be generated.
    + Default: `null`
+ referral: `Stevie Ray Vaughan` (string) - Referral note

## ClientWritable (object)
+ Include Client
+ contacts (array[ClientContactWritable])
+ attributes (array[ClientAttribute])
+ password: `correct horse battery staple` (string, optional) - if not empty, client's password will be changed to this. Note that the password is write-only.
+ addressData: (GeocoderResultData, optional) - if filled, this geocoder result will be parsed into the address fields. If you have just made a geocoding request, this avoids two identical calls to the geocoder service.

## ClientReadOnly (object)
+ id: `1000` (number)
+ Include Client
+ isActive (boolean) - If `true`, this client has access to Client Zone
+ contacts (array[ClientContactReadOnly])
+ attributes (array[ClientAttributeReadOnly])
+ accountBalance: `-347.94` (number) - The positive number means that the client has a credit, otherwise a debit.
+ accountCredit: `0` (number)
+ accountOutstanding: `347.94` (number)
+ currencyCode: `USD` (string)
+ organizationName: `Your ISP inc.` (string)
+ bankAccounts: (array[ClientBankAccountReadOnly])
+ tags (array[ClientTagReadOnly])
+ invitationEmailSentDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). Null if the user was not invited yet.
+ isArchived (boolean) - If `true`, client is archived.
+ usesProforma (boolean) - If `true` proforma invoice will be generated.
+ hasOverdueInvoice: `false` (boolean) - If `true`, this client has some overdue invoice.
+ hasOutage: `false` (boolean) - If `true`, there's an internet connection outage of the devices in the Location related to this client.
+ hasSuspendedService: `false` (boolean) - If `true`, this client has some suspended service.
+ hasServiceWithoutDevices: `false` (boolean) -  If `true`, client service is not linked to Network or is linked to an inactive Location.
+ leadConvertedAt: `2016-09-12T00:00:00+0000` (string, nullable) - Only filled if a client was converted from lead to regular client. Date string in the following format (see example).
+ hasPaymentSubscription: `false` (boolean) - If `true`, client has a payment subscription.
+ hasAutopayCreditCard: `false` (boolean) - If `true`, client has a credit card with enabled autopay

## ClientLog (object)
+ message: `Client approved installation` (required, string)
+ clientId: `156` (required, number)
    See [Retrieve Clients](#reference/clients/clients/get)
+ userId: `1` (number)
    See [Retrieve Users](#reference/users/usersadmins/get)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.

## ClientLogsWritable (object)
+ Include ClientLog

## ClientLogsReadOnly (object)
+ id: `1000` (number)
+ Include ClientLog

## ClientContact (object)
+ email: `<EMAIL>`
+ phone
+ name (string)
+ isBilling (boolean) - Contact is used for Billing notifications
+ isContact (boolean) - Contact is used for other notifications

## ClientContactReadOnly (object)
+ id: `2000` (number)
+ clientId: `1000` (number)
+ Include ClientContact
+ types (array[ContactTypeReadOnly])

## ClientContactWritable (object)
+ Include ClientContact
+ types (array[ContactType])

## ClientBankAccount (object)
+ accountNumber: `0********0********90123` (string) - Bank account number

## ClientBankAccountReadOnly (object)
+ id: `1` (number)
+ Include ClientBankAccount

## ClientAttribute (object)
+ value: `lorem ipsum` (string) - integer custom attribute type is a string representation of the number; boolean custom attribute type is a string representation of the 0 (i.e. false) or 1 (i.e. true); date custom attribute type is a string representation of the date in the format 2020-12-16
+ customAttributeId: `1` (number)
    See [Retrieve Custom Attributes](#reference/custom-attributes/custom-attributes/get)

## ClientAttributeReadOnly (object)
+ id: `2000` (number)
+ clientId: `1000` (number)
+ name: `Personal Code` (string)
+ key: `personalCode` (string)
+ clientZoneVisible: `true` (boolean) - if false, attribute is hidden in Client Zone
+ Include ClientAttribute

## ClientTag (object)
+ name: `VIP` (string)
+ colorBackground: `#000000` (string)
+ colorText: `#ffffff` (string)

## ClientTagReadOnly (object)
+ id: `2000` (number)
+ Include ClientTag

## TicketTag (object)
+ name: `VIP` (string)
+ colorBackground: `#000000` (string)
+ colorText: `#ffffff` (string)

## TicketTagReadOnly (object)
+ id: `cd089e0f-e332-4427-b7cf-f1a9bf593b02` (string) - UUID
+ Include TicketTag

## ClientZoneClient (object)
+ id: `1000` (number)
+ userIdent: `ABC1000` (string) - "Custom ID" in CRM, not to be confused with entity "ID" used in URL
+ clientType (enum[number])
    + Default: `1`
    + Members
        + `1` - Residential
        + `2` - Company
+ companyName - Required in case of type **Company**
+ companyRegistrationNumber
+ companyTaxId
+ companyWebsite
+ companyContactFirstName
+ companyContactLastName
+ firstName: `John` - Required in case of type **Residential**
+ lastName: `Doe` - Required in case of type **Residential**
+ street1: `2580 Orchard Pkwy`
+ street2
+ city: `San Jose`
+ countryId: `249` (number) - If not specified, taken from default organization
    See [Retrieve Countries](#reference/general/countries/get)
+ stateId: `1` (number) - Applicable in case of **United States** and **Canada** only. If not specified, taken from default organization
    See [Retrieve States](#reference/general/countriesidstates/get)
+ zipCode: `95131`
+ fullAddress: `2580 Orchard Pkwy, San Jose, CA, United States of America` (string) - address as resolved by geolocation
+ invoiceStreet1: `2580 Orchard Pkwy`
+ invoiceStreet2
+ invoiceCity: `San Jose`
+ invoiceStateId: `1` (number) - Applicable in case of **United States** and **Canada** only
    See [Retrieve States](#reference/general/countriesidstates/get)
+ invoiceCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ invoiceZipCode: `95131`
+ invoiceAddressSameAsContact (boolean)
    + Default: `false`
+ username: `john.doe` - If null, client zone is disabled
+ addressGpsLat: `37.*************` (number) - Latitude of address location
+ addressGpsLon: `-121.*********113` (number) - Longitude of address location
+ contacts (array[ClientZoneClientContact])
+ attributes (array[ClientZoneClientAttribute])
+ accountBalance: `-347.94` (number) - The positive number means that the client has a credit, otherwise a debit.
+ accountCredit: `0` (number)
+ accountOutstanding: `347.94` (number)
+ currencyCode: `USD` (string)
+ organizationName: `Your ISP inc.` (string)
+ bankAccounts: (array[ClientZoneClientBankAccount])

## ClientZoneClientContact (object)
+ id: `2000` (number)
+ email: `<EMAIL>`
+ phone
+ name (string)
+ isBilling (boolean) - Contact is used for Billing notifications
+ isContact (boolean) - Contact is used for other notifications
+ types (array[ClientZoneContactType])

## ClientZoneContactType
+ id: `3000` (number)
+ name: `Home contact` (string)

## ClientZoneClientAttribute (object)
+ id: `2000` (number)
+ name: `Personal Code` (string)
+ key: `personalCode` (string)
+ value: `lorem ipsum` (string)

## ClientZoneClientBankAccount (object)
+ id: `1` (number)
+ accountNumber: `0********0********90123` (string) - Bank account number
+ Include ClientBankAccount

## ClientZoneServiceChangeRequest (object)
+ serviceId: `1` (number, required)
+ targetServicePlanPeriodId: `11` (number, required)

## ClientZoneServiceChangeRequestReadOnly (object)
+ id: `cafeb00f-ecca-4331-9e2b-ea98a1c706f6` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ Include ServiceChangeRequest

## ClientZonePrepaidServicePeriodReadOnly (object)
+ id: `cafeb00f-ecca-4331-9e2b-ea98a1c706f6` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ startDate: `2020-03-23` (string) - Date in `YYYY-MM-DD` format.
+ endDate: `2020-03-25` (string) - Date in `YYYY-MM-DD` format.
+ serviceId: `1` (number)
    See [Retrieve Service](#reference/client-zone-services/client-zoneservicesid/get)

## ClientZoneCreditCardReadOnly (object)
+ id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string) - UUID
+ providerId: `card_1GVv3l2eZvKYlo2CoZfEkF6B` (string)
+ brand: `mastercard` (string)
+ brandForView: `Mastercard®` (string)
+ expirationMonth: `8` (number)
+ expirationYear: `2025` (number)
+ last4: `5821` (string)
+ createdDate: `2020-04-08T00:00:00+0000` (string) - Date string in the following format (see example).
+ lastSuccessfulPaymentDate: `2020-04-08T00:00:00+0000` (string) - Date string in the following format (see example).
+ useForAutopay: `false` (boolean)
+ monthlyLimit: `1000.0` (number)

## ClientZoneInvoiceReadOnly (object)
+ id: `1` (number)
+ number: `000001` (string)
+ createdDate: `2020-03-23T00:00:00+0000` (string) - Date string in the following format (see example).
+ dueDate: `2020-03-23T00:00:00+0000` (string) - Date string in the following format (see example).
+ emailSentDate: `2020-03-23T00:00:00+0000` - (string, nullable) - Date string in the following format (see example) or null. Does not actually send the invoice by email when changed.
+ maturityDays: `14` (number) - If not specified, will be taken from global settings.
+ taxableSupplyDate: `2020-03-23T00:00:00+0000` (string) - Date string in the following format (see example).
+ notes: `setup fee included` (string) - This text will be displayed on the invoice.
+ items (array[ClientZoneInvoiceItemReadOnly])
+ subtotal: `15.0` (number)
+ discount: `5.0` (number)
+ discountLabel: `Quantity discount` (string) - Label of discount.
+ taxes: (array[ClientZoneTotalTax])
+ total: `10.0` (number)
+ amountPaid: `10.0` (number)
+ totalUntaxed: `10.0` (number)
+ totalDiscount: `5.0` (number)
+ totalTaxAmount: `0.0` (number)
+ amountToPay: `0.0` (number)
+ currencyCode: `USD` (string)
+ status: `3` (InvoiceStatus)
+ paymentCovers: (array[ClientZonePaymentCoverReadOnly])
+ organizationName: `Your ISP inc.` (string)
+ organizationRegistrationNumber: `RN123456` (string)
+ organizationTaxId: `TI123456` (string)
+ organizationStreet1: `2580 Orchard Parkway` (string)
+ organizationStreet2: `Suite G` (string)
+ organizationCity: `San Jose` (string)
+ organizationCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ organizationStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ organizationZipCode: `95131` (string)
+ organizationBankAccountName: `Primary` (string)
+ organizationBankAccountField1: `12345` (string)
+ organizationBankAccountField2: `6789` (string)
+ clientFirstName: `John` (string)
+ clientLastName: `Doe` (string)
+ clientCompanyName: `John Doe Company inc.` (string)
+ clientCompanyRegistrationNumber: `CRN123` (string)
+ clientCompanyTaxId: `CTI123` (string)
+ clientStreet1: `2580 Orchard Parkway` (string)
+ clientStreet2: `Suite E` (string)
+ clientCity: `San Jose` (string)
+ clientCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ clientStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ clientZipCode: `95131` (string)
+ attributes (array[ClientZoneInvoiceAttributeReadOnly])
+ proforma: `false` (boolean) - Invoice is proforma.
+ generatedInvoiceId: `15` (number, nullable) - If filled, contains ID of regular invoice which was generated from this proforma invoice after receiving payment.
+ proformaInvoiceId: `7` (number, nullable) - If filled, contains ID of proforma invoice which triggered generation of this invoice after receiving payment.
+ isAppliedVatReverseCharge: `false` (boolean) - If true, items are subject to reverse charge – VAT to be accounted for by the recipient.

## ClientZoneInvoiceAttributeReadOnly (object)
+ id: `1` (number)
+ invoiceId: `2` (number)
+ customAttributeId: `1` (number)
+ name: `Invoice Code` (string)
+ key: `invoiceCode` (string)
+ value: `I20200323` (string)

## ClientZoneInvoiceItemReadOnly (object)
+ id: `1` (number)
+ type: `service` (InvoiceItemType)
+ label: `Internet` (required)
+ price: `10.0` (number, required)
+ quantity (number)
    + Default: `1`
+ total: `10.0` (number, required)
+ unit
+ tax1Id: `1` (number)
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ serviceId: `1` (number, nullable)
+ serviceSurchargeId: `1` (number, nullable)
+ productId: `1` (number, nullable)
+ feeId: `1` (number, nullable)
+ discountPrice: `10.0` (number, nullable)
+ discountQuantity (number, nullable)
+ discountTotal (number, nullable)

## ClientZonePaymentReadOnly (object)
+ id: `1` (number)
+ methodId: `6efe0fa8-36b2-4dd1-b049-427bffc7d369` (string, optional) - UUID
+ methodName: `Stripe credit card` - Payment method name.
+ checkNumber - Applicable in case of **Check** method only.
+ createdDate: `2020-03-20T15:30:27+0000` (string) - Date string in the following format (see example).
+ amount: `10.0` (number)
+ currencyCode: `USD` (string)
+ note: `Paid in coins.` (string)
+ receiptSentDate: `2020-03-20T00:00:00+0000` (string) - Date string in the following format (see example) or null if receipt was not sent.
+ providerName: `Worldpay` (string, nullable) - Required in case of **Custom** method.
+ providerPaymentId: `WP451837` (string, nullable) - Required in case of **Custom** method.
+ providerPaymentTime: `2016-09-12T00:00:00+0000` (string, nullable) - Date string in the following format (see example). If not specified, current date will be used. Required in case of **Custom** method.
+ paymentCovers: (array[ClientZonePaymentCoverReadOnly])
+ creditAmount: `10.0` (number)
+ attributes (array[ClientZonePaymentAttributeReadOnly])

## ClientZonePaymentAttributeReadOnly (object)
+ id: `2c6984d7-03ae-4e48-a212-7b99378837a2` (string)
+ paymentId: `1000` (number)
+ customAttributeId: `1` (number)
+ name: `Payment Code` (string)
+ key: `paymentCode` (string)
+ value: `P20200320` (string)

## ClientZonePaymentCoverReadOnly (object)
+ id: `1` (number)
+ paymentId: `1` (number, optional) - Source payment of the cover (can be `null`, but `creditNoteId` will be filled in that case).
+ creditNoteId: `1` (number, optional) - Source payment of the cover (can be `null`, but `paymentId` will be filled in that case).
+ invoiceId: `1` (number, optional) - Invoice assigned to this cover (can be `null`, but `refundId` will be filled in that case).
+ refundId: `75` (number, optional) - Refund assigned to this cover (can be `null`, but `invoiceId` will be filled in that case).
+ amount: `123.23` (number, required) - Amount this cover takes from source payment.

## ClientZoneQuoteReadOnly (object)
+ id: `1` (number)
+ number: `000001` (string)
+ createdDate: `2020-03-23T00:00:00+0000` (string) - Date string in the following format (see example).
+ notes: `setup fee included` (string) - This text will be displayed on the quote.
+ items (array[QuoteItemReadOnly])
+ subtotal: `15.0` (number)
+ discount: `5.0` (number)
+ discountLabel: `Quantity discount` (string) - Label of discount.
+ taxes: (array[ClientZoneTotalTax])
+ total: `10.0` (number)
+ totalUntaxed: `10.0` (number)
+ totalDiscount: `5.0` (number)
+ totalTaxAmount: `0.0` (number)
+ currencyCode: `USD` (string)
+ status: `3` (QuoteStatus)
+ organizationName: `Your ISP inc.` (string)
+ organizationRegistrationNumber: `RN123456` (string)
+ organizationTaxId: `TI123456` (string)
+ organizationStreet1: `2580 Orchard Parkway` (string)
+ organizationStreet2: `Suite G` (string)
+ organizationCity: `San Jose` (string)
+ organizationCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ organizationStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ organizationZipCode: `95131` (string)
+ organizationBankAccountName: `Primary` (string)
+ organizationBankAccountField1: `12345` (string)
+ organizationBankAccountField2: `6789` (string)
+ clientFirstName: `John` (string)
+ clientLastName: `Doe` (string)
+ clientCompanyName: `John Doe Company inc.` (string)
+ clientCompanyRegistrationNumber: `CRN123` (string)
+ clientCompanyTaxId: `CTI123` (string)
+ clientStreet1: `2580 Orchard Parkway` (string)
+ clientStreet2: `Suite E` (string)
+ clientCity: `San Jose` (string)
+ clientCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ clientStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ clientZipCode: `95131` (string)
+ isAppliedVatReverseCharge: `false` (boolean) - If true, items are subject to reverse charge – VAT to be accounted for by the recipient.

## ClientZoneServiceReadOnly (object)
+ id: `1` (number)
+ prepaid: `false` (boolean)
+ status: `3` (ServiceStatus)
+ name: `Internet` - If not specified, service plan name will be used
+ fullAddress: `2580 Orchard Pkwy, San Jose, CA, United States of America` (string) - If not specified, taken from client
+ street1: `2580 Orchard Pkwy` - If not specified, taken from client
+ street2 - If not specified, taken from client
+ city: `San Jose` - If not specified, taken from client
+ countryId: `249` (number) - If not specified, taken from client
    See [Retrieve Countries](#reference/general/countries/get)
+ stateId: `1` (number) - Applicable in case of **United States** and **Canada** only. If not specified, taken from client
    See [Retrieve States](#reference/general/countriesidstates/get)
+ zipCode: `95131` - If not specified, taken from client
+ addressGpsLat: `37.*************` (number) - Latitude of service location
+ addressGpsLon: `-121.*********113` (number) - Longitude of service location
+ servicePlanId: `3` (number)
    See [Retrieve Service Plans](#reference/client-zone/service-plans/get)
+ price: `58.5` (number) - Price from the chosen service plan period or the individual price if specified. Changing this property only sets the individual price.
+ hasIndividualPrice: `false` (boolean) - True if the service price is set individually, false if it's inherited from service plan.
+ totalPrice: `66.72` (number) - Total price of service, including surcharges, discounts and taxes. Price is calculated for current invoicing period and thus accounts for time-limited discounts.
+ currencyCode: `USD` (string)
+ invoiceLabel: `Internet` - Individual invoice label to override default set in chosen service plan
+ contractId: `000001`
+ contractLengthType (enum[number]) - *recurring only*
    + Default: `1`
    + Members
        + `1` - Open end contract
        + `2` - Close end contract
+ minimumContractLengthMonths:`12` (number) - *recurring only*
    Minimum contract length in months
+ activeFrom: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). If not specified, current date will be used.
+ activeTo: `2017-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example).
+ contractEndDate: `2017-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Required in case of **Close end contract**.
+ discountType (enum[number]) - *recurring only*
    + Default: `0`
    + Members
        + `0` - No discount
        + `1` - Percentage discount
        + `2` - Fixed discount
+ discountValue: `5.5` (number) - *recurring only*
+ discountInvoiceLabel: `Discount` - *recurring only*
+ discountFrom: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Must match one of the selected service plan period's start dates.
+ discountTo: `2016-12-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Must match one of the selected service plan period's end dates.
+ tax1Id: `1` (number) - Applicable if the service is taxable. If not specified, taken from client
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - Applicable if the service is taxable. If not specified, taken from client
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - Applicable if the service is taxable. If not specified, taken from client
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ invoicingStart: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). If not specified, current date will be used.
    Service will be invoiced from this date including
+ invoicingPeriodType: `1` (enum[number]) - *recurring only*
    + Members
        + `1` - Backwards - Recurring invoices are created at the end of the billing period
        + `2` - Forwards - Recurring invoices are created at the beginning of billing period (clients pay for the service in advance)
+ invoicingPeriodStartDay: `1` (number) - *recurring only*
    Day in month when the recurring period starts. If not specified, current day will be used. Valid values are 1-28 and 31, where 31 means last day of month.
+ nextInvoicingDayAdjustment: `5` (number) - *recurring only*
    Number of days to shift for before the default day, when the recurring invoices are automatically created
+ servicePlanName: `Maxi`
+ servicePlanPrice: `50.0` (number)
+ servicePlanPeriod: `1` (number) - *recurring only*
    Pricing period in months
+ servicePlanPeriodId: `11` (number) - *recurring only*
    See [Retrieve Service Plan By Period Id](#reference/client-zone-service-plans/client-zoneservice-plansperiodid/get)
+ downloadSpeed: `50` (number) - Download speed in Mbps
+ uploadSpeed: `50` (number) - Upload speed in Mbps

## ClientZoneTariffReadOnly (object)
+ id: `1` (number)
+ name: `Maxi` (required)
+ invoiceLabel: `Maxi` - If not specified, name will be used
+ downloadBurst: `200` (number) - Download burst in kB
+ uploadBurst: `100` (number) - Upload burst in kB
+ downloadSpeed: `50` (number) - Download speed in Mbps
+ uploadSpeed: `50` (number) - Upload speed in Mbps
+ dataUsageLimit: `100` (number) - Data usage limit in GB
+ organizationId: `1` (number) - If not specified, default organization will be used
+ taxable (boolean)
    + Default: `false`
+ periods (array[ClientZoneTariffPeriod])
+ downloadSpeedLimited: `2` (number) - Download speed after reaching data usage limit in Mbps
+ uploadSpeedLimited: `1` (number) - Upload speed after reaching data usage limit in Mbps

## ClientZoneTariffPeriod (object)
+ id: `1` (number)
+ period: `1` (enum[number]) - Service plan period in months
    + Members
        + `1`
        + `3`
        + `6`
        + `12`
        + `24`
+ price: `50.0` (number)
+ enabled (boolean)
    + Default: `false`

## ClientZoneTicket (object)
+ subject: `The Internet is not working` (string)

## ClientZoneTicketReadOnly (object)
+ id: `1` (number)
+ Include ClientZoneTicket
+ createdAt: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ status: (TicketStatus)
+ lastActivity: `2020-03-26T00:00:00+0000` (string) - Date string in the following format (see example).
+ lastCommentAt: `2020-03-26T00:00:00+0000` (string) - Date string in the following format (see example).
+ isLastActivityByClient: `true` (boolean) - if true, the last activity is on the client's side.
+ activity (array[ClientZoneTicketActivityReadOnly])

## ClientZoneTicketWritable (object)
+ Include ClientZoneTicket
+ activity (required, array[ClientZoneTicketActivityWritable]) - At least one comment required.

## ClientZoneTicketActivityReadOnly (object)
+ id: `1` (number)
+ ticketId: `12` (number)
    See [Retrieve Ticket](#reference/client-zone-tickets/client-zoneticketslimitoffset/get)
+ createdAt: `2020-03-27T00:00:00+0000` (string) - Date string in the following format (see example).
+ comment (ClientZoneTicketActivityCommentReadOnly, optional)
+ statusChange (ClientZoneTicketActivityStatusChange, optional)
+ type: `comment` (enum[string]) - Type of activity.
    + Members
        + `comment`
        + `status_change`
+ isAdmin: `true` (boolean) - True if activity is created by admin.

## ClientZoneTicketActivityWritable (object)
+ comment (required, ClientZoneTicketActivityCommentWritable)

## ClientZoneTicketActivityCommentReadOnly (object)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string)
+ attachments (array[ClientZoneTicketCommentAttachmentReadOnly])

## ClientZoneTicketActivityCommentWritable (object)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string) - Required when **attachments** is not specified.
+ attachments (array[ClientZoneTicketCommentAttachmentWritable]) - Required when **body** is not specified.

## ClientZoneTicketActivityStatusChange (object)
+ status: (TicketStatus)

## ClientZoneTicketCommentReadOnly (object)
+ id: `1` (number)
+ ticketId: `3` (number)
    See [Retrieve Ticket](#reference/client-zone-tickets/client-zoneticketslimitoffset/get)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string)
+ createdAt: `2020-03-26T00:00:00+0000` (string) - Date string in the following format (see example).
+ attachments (array[ClientZoneTicketCommentAttachmentReadOnly])

## ClientZoneTicketCommentWritable (object)
+ ticketId: `3` (required, number)
    See [Retrieve Ticket](#reference/client-zone-tickets/client-zoneticketslimitoffset/get)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string) - Required when **attachments** is not specified.
+ attachments (array[ClientZoneTicketCommentAttachmentWritable]) - Required when **body** is not specified.

## ClientZoneTicketCommentAttachment (object)
+ filename: `foto.jpg` (required, string) - File name.

## ClientZoneTicketCommentAttachmentReadOnly (object)
+ id: `1` (number)
+ Include ClientZoneTicketCommentAttachment
+ mimeType: `image/png` (string) - MIME Type of file.
+ size: `6983` (number) - Size of file in bytes.

## ClientZoneTicketCommentAttachmentWritable (object)
+ file: `` (required, string) - File encoded in base64.
+ Include ClientZoneTicketCommentAttachment

## ClientZoneTotalTax (object)
+ name: `VAT 19% - 19%` (string) - Name and rate of tax.
+ totalValue: `2.58` (number) - Sum of all taxes of this kind.

## ClientZoneUnifiOverview (object)
+ organization (object)
  + name: `UBNT ISP` (string, required)
  + logo: `SGVsbG8gV29ybGQhCg==` (string) - Image file encoded in base64.
+ client (object)
  + hasAutopayOrPaymentSubscription: `true` (boolean)
+ service (object)
  + id: `1` (number, required)
  + name: `Internet` (string, required)
  + downloadSpeed: `50` (number) - Download speed in Mbps.
  + uploadSpeed: `50` (number) - Upload speed in Mbps.
  + price: `58.5` (number, required) - Price for a single whole period or for single day in case of prepaid services.
  + period: `1` (number) - Pricing period in months.
  + prepaid: `false` (boolean)
  + nextInvoiceDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ invoices (array[ClientZoneUnifiOverviewInvoice])
+ creditCards (array[ClientZoneUnifiOverviewCreditCard])

## ClientZoneUnifiOverviewInvoice (object)
+ id: `1` (number, required)
+ number: `000001` (string, required)
+ createdDate: `2020-03-23T00:00:00+0000` (string, required) - Date string in the following format (see example).
+ dueDate: `2020-03-23T00:00:00+0000` (string, required) - Date string in the following format (see example).
+ total: `10.0` (number, required)
+ currencyCode: `USD` (string, required)
+ status: `3` (InvoiceStatus, required)
+ overdue: `false` (boolean)
+ proforma: `false` (boolean)

## ClientZoneUnifiOverviewCreditCard (object)
+ id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string, required) - UUID
+ brand: `mastercard` (string)
+ last4: `5821` (string)
+ expirationMonth: `8` (number)
+ expirationYear: `2025` (number)
+ useForAutopay: `true` (boolean)

## CreditNoteTemplateReadOnly (object)
+ id: `1` (number)
+ name: `Default` (string)
+ createdDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ isOfficial: `true` (boolean)
+ isValid: `true` (boolean)

## CreditNote (object)
+ createdDate: `2016-09-12T00:00:00+0000` - (string) - Date string in the following format (see example). If not specified, current date will be used.
+ emailSentDate: `2016-09-12T00:00:00+0000` - (string, nullable) - Date string in the following format (see example) or null. Does not actually send the credit note by email when changed.
+ notes - This text will be displayed on the credit note
+ adminNotes - This text will not be displayed on the credit note and is never shown to client
+ creditNoteTemplateId: `1` (number) - If not specified, credit note template set on Organization is used
    See [Retrieve Credit Note Templates](#reference/credit-note-template/credit-note-templates/get)
+ organizationName: `Your ISP inc.` (string)
+ organizationRegistrationNumber: `RN123456` (string)
+ organizationTaxId: `TI123456` (string)
+ organizationStreet1: `2580 Orchard Parkway` (string)
+ organizationStreet2: `Suite G` (string)
+ organizationCity: `San Jose` (string)
+ organizationCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ organizationStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ organizationZipCode: `95131` (string)
+ organizationBankAccountName: `Primary` (string)
+ organizationBankAccountField1: `12345` (string)
+ organizationBankAccountField2: `6789` (string)
+ clientFirstName: `John` (string)
+ clientLastName: `Doe` (string)
+ clientCompanyName: `John Doe Company inc.` (string)
+ clientCompanyRegistrationNumber: `CRN123` (string)
+ clientCompanyTaxId: `CTI123` (string)
+ clientStreet1: `2580 Orchard Parkway` (string)
+ clientStreet2: `Suite E` (string)
+ clientCity: `San Jose` (string)
+ clientCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ clientStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ clientZipCode: `95131` (string)

## CreditNoteReadOnly (object)
+ id: `1` (number)
+ Include CreditNoteReadOnlyData

## CreditNoteNew (object)
+ Include CreditNote
+ items (array[CreditNoteItem])

## CreditNoteReadOnlyData (object)
+ clientId: `1` (number)
+ number: `**********`
+ Include CreditNote
+ items (array[CreditNoteItemReadOnly])
+ subtotal: `15.0` (number)
+ discount: `5.0` (number)
+ discountLabel: `Quantity discount` (string) - Label of discount
+ taxes: (array[TotalTax])
+ total: `10.0` (number)
+ totalUntaxed: `10.0` (number)
+ totalDiscount: `5.0` (number)
+ totalTaxAmount: `0.0` (number)
+ currencyCode: `USD` (string)
+ paymentCovers: (array[PaymentCoverReadOnly])

## CreditNoteItem (object)
+ label: `Internet` (required)
+ price: `-10.0` (number, required)
+ quantity (number)
    + Default: `1`
+ unit
+ tax1Id: `1` (number)
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)

## CreditNoteItemReadOnly (object)
+ id: `1` (number)
+ total: `-10.0` (number, required)
+ type: `other` (string)
+ discountPrice: `10.0` (number, nullable)
+ discountQuantity (number, nullable)
+ discountTotal (number, nullable)
+ Include CreditNoteItem

## CreditNoteUpdate (object)
+ number: `**********`
+ items (array[CreditNoteItem])
+ Include CreditNote

## Document (object)
+ clientId: `1` (required, number)
    See [Retrieve Clients](#reference/clients/clientsid/get)
+ name: `contract.pdf` (required, string) - File name.

## DocumentReadOnly (object)
+ id: `1` (required, number)
+ userId: `1000` (number)
    See [Retrieve Users](#reference/users/usersadmins/get)
+ Include Document
+ type: `document` (required, enum[string])
    + Members
        + `document`
        + `image`
        + `other`
+ createdDate: `2020-08-11T00:00:00+0000` (required, string) - Date string in the following format (see example).
+ path: `/data/documents/1/e0b354c02f8d978291948bf4f515764f.pdf` (required, string)
+ size: `6983` (required, number) - Size of file in bytes.

## DocumentWritable (object)
+ file: `` (string) - File encoded in base64. Required when *templateId* is empty.
+ templateId: `1` (number) - Document template ID. Required when *file* is empty.
    See [Retrieve Document template](#reference/document-templates/document-templates/get)
+ Include Document

## DocumentTemplateReadOnly (object)
+ id: `1` (number)
+ name: `Default` (string)
+ createdDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ isValid: `true` (boolean)


## Service (object)
+ prepaid: `false` (boolean)
    + Default: `false`
+ servicePlanPeriodId: `11` (number, required) - *recurring only*
    See [Retrieve Service Plan](#reference/service-plans/service-plansid/get)
+ activeFrom: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). If not specified, current date will be used.
+ activeTo: `2017-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example).
+ name: `Internet` - If not specified, service plan name will be used
+ price: `58.5` (number) - Price from the chosen service plan period or the individual price if specified. Changing this property only sets the individual price.
+ note
+ invoicingStart: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). If not specified, current date will be used.
    Service will be invoiced from this date including
+ invoicingPeriodType: `1` (enum[number]) - *recurring only*
    If not specified, default type from billing settings will be used.
    + Members
        + `1` - Backwards - Recurring invoices are created at the end of the billing period
        + `2` - Forwards - Recurring invoices are created at the beginning of billing period (clients pay for the service in advance)
+ invoicingPeriodStartDay: `1` (number) - *recurring only*
    Day in month when the recurring period starts. If not specified, default day from billing settings will be used. Valid values are 1-28 and 31, where 31 means last day of month.
+ nextInvoicingDayAdjustment: `5` (number) - *recurring only*
    Number of days to shift for before the default day, when the recurring invoices are automatically created
+ invoicingProratedSeparately: `true` (boolean) - *recurring only*
    Whether to invoice the first prorated period alone or together with the next whole period
    + Default: `true`
+ invoicingSeparately (boolean) - *recurring only*
    Do not merge this service with other client's services into a single invoice
    + Default: `false`
+ sendEmailsAutomatically: `false` (boolean) - If `null`, system default is used
    Create and send the recurring invoice to client automatically. If not specified, taken from general settings
+ useCreditAutomatically (boolean) - *recurring only*
    + Default: `true`
+ invoiceLabel: `Internet` - Individual invoice label to override default set in chosen service plan
+ fullAddress: `2580 Orchard Pkwy, San Jose, CA, United States of America` (string) - If not specified, taken from client
+ street1: `2580 Orchard Pkwy` - If not specified, taken from client
+ street2 - If not specified, taken from client
+ city: `San Jose` - If not specified, taken from client
+ countryId: `249` (number) - If not specified, taken from client
    See [Retrieve Countries](#reference/general/countries/get)
+ stateId: `1` (number) - Applicable in case of **United States** and **Canada** only. If not specified, taken from client
    See [Retrieve States](#reference/general/countriesidstates/get)
+ zipCode: `95131` - If not specified, taken from client
+ addressGpsLat: `37.*************` (number) - Latitude of service location
+ addressGpsLon: `-121.*********113` (number) - Longitude of service location
+ contractId: `000001`
+ contractLengthType (enum[number]) - *recurring only*
    + Default: `1`
    + Members
        + `1` - Open end contract
        + `2` - Close end contract
+ minimumContractLengthMonths:`12` (number) - *recurring only*
    Minimum contract length in months
+ contractEndDate: `2017-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Required in case of **Close end contract**.
+ discountType (enum[number]) - *recurring only*
    + Default: `0`
    + Members
        + `0` - No discount
        + `1` - Percentage discount
        + `2` - Fixed discount
+ discountValue: `5.5` (number) - *recurring only*
+ discountInvoiceLabel: `Discount` - *recurring only*
+ discountFrom: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Must match one of the selected service plan period's start dates.
+ discountTo: `2016-12-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Must match one of the selected service plan period's end dates.
+ tax1Id: `1` (number) - Applicable if the service is taxable. If not specified, taken from client
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - Applicable if the service is taxable. If not specified, taken from client
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - Applicable if the service is taxable. If not specified, taken from client
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ fccBlockId: `***************` (optional) - Census block GEOID - used for FCC report generation
+ servicePlanGroupId: `4d9e206c-ba3b-4893-9cf7-fb08a778f00d` (string, optional) - UUID
    See [Retrieve Service Plan Groups](#reference/service-plan-groups/service-plan-groupslimitoffset/get)

## ServiceReadOnly (object)
+ id: `1` (number)
+ servicePlanId: `3` (number)
    See [Retrieve Service Plans](#reference/service-plans/service-planspublicserviceplantypeserviceplangroupid/get)
+ clientId: `1` (number)
+ status: `3` (ServiceStatus)
+ Include Service
+ hasIndividualPrice: `false` (boolean) - True if the service price is set individually, false if it's inherited from service plan.
+ totalPrice: `66.72` (number) - Total price of service, including surcharges, discounts and taxes. Price is calculated for current invoicing period and thus accounts for time-limited discounts.
+ servicePlanName: `Maxi`
+ servicePlanPrice: `50.0` (number)
+ servicePlanPeriod: `1` (number) - *recurring only*
    Pricing period in months
+ servicePlanType: `Internet` (enum[string])
    + Members
        + `Internet`
        + `General`
+ downloadSpeed: `50` (number) - Download speed in Mbps
+ uploadSpeed: `50` (number) - Upload speed in Mbps
+ currencyCode: `USD` (string)
+ hasOutage: `false` (boolean) - There's an internet connection outage of the devices in the Location related to this service.
+ unmsClientSiteStatus: `active` (enum[string])
    + Members
        + `active`
        + `inactive`
        + `disconnected`
+ fccBlockId: `***************` (optional) - Census block GEOID - used for FCC report generation
+ unmsClientSiteId: `7b1c38b5-a8b5-48c7-867c-405668be0324` (string) - Connected UISP Location ID.
+ lastInvoicedDate: `2016-09-12T00:00:00+0000` (string, nullable) - *recurring only*
    Date string in the following format (see example) or null if not yet invoiced.
+ attributes (array[ServiceAttributeReadOnly])
+ suspensionReasonId: `1000` (number) - Suspended service reason
    See [Service Suspension Reasons](#reference/service-suspension-reasons)
+ serviceChangeRequestId: `b899f30b-2417-4a79-9f52-76178840cb2a` (string, nullable)
    *recurring only*
    See [Retrieve Service Change Request](#reference/service-change-requests/service-change-requestsid/get)
+ setupFeePrice: `42.90` (number) - *recurring only*
+ earlyTerminationFeePrice: `129.90` (number) - *recurring only*
+ downloadSpeedOverride: `100` (number, nullable) - Download speed override in Mbps. If `null` and traffic shaping override is enabled, it means unlimited speed. 
+ uploadSpeedOverride: `10` (number, nullable) - Upload speed override in Mbps. If `null` and traffic shaping override is enabled, it means unlimited speed. 
+ trafficShapingOverrideEnd: `2016-09-12T00:00:00+0000` (string, nullable) - Date string in the following format (see example). If filled and traffic shaping override is enabled, it will be automatically disabled on the specified date.
+ trafficShapingOverrideEnabled: `false` (boolean)
+ suspensionPeriods (array[SuspensionPeriod])
+ surcharges (array[ServiceSurchargeReadOnly])

## ServiceUsageReadonly (object)
+ download `1051200155` (number) Number of bytes downloaded in period
+ upload `364602541` (number) Number of bytes uploaded in period
+ downloadLimit `100000000000` (number) Limit of downloadable bytes for period, if any
+ periodStart: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example).
+ periodEnd: `2016-09-18T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example).
+ lastInvoicedDate: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example) or null if not yet invoiced.

## ServiceWritable
+ servicePlanId: `11` (number) - *prepaid only*
    See [Retrieve Service Plan](#reference/service-plans/service-plansid/get)
+ Include Service
+ isQuoted: `false` (boolean) - *recurring only*
    Will create quoted service when `true`, otherwise regular service will be created. If regular service is created for client lead, he will be automatically converted to regular client.
    + Default: `false`
+ unmsClientSiteId: `7b1c38b5-a8b5-48c7-867c-405668be0324` (string) - Connect to given UISP Location.
+ attributes (array[ServiceAttribute])
+ addressData: (GeocoderResultData, optional) - if filled, this geocoder result will be parsed into the address fields. If you have just made a geocoding request, this avoids two identical calls to the geocoder service.

## ServiceUpdate (object)
+ servicePlanId: `11` (number) - *prepaid only*
    See [Retrieve Service Plan](#reference/service-plans/service-plansid/get)
+ servicePlanPeriodId: `11` (number) - *recurring only*
    See [Retrieve Service Plan](#reference/service-plans/service-plansid/get)
+ name: `Internet`
+ price: `58.5` (number) - Changing this property only sets the individual price.
+ invoicingPeriodType: `1` (enum) - *recurring only*
    + Members
        + `1` - Backwards - Recurring invoices are created at the end of the billing period
        + `2` - Forwards - Recurring invoices are created at the beginning of billing period (clients pay for the service in advance)
+ invoiceLabel: `Internet` - Individual invoice label to override default set in chosen service plan
+ activeTo: `2017-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example).
+ nextInvoicingDayAdjustment: `5` (number) - *recurring only*
    Number of days to shift for before the default day, when the recurring invoices are automatically created
+ invoicingSeparately: `false` (boolean) - *recurring only*
    Do not merge this service with other client's services into a single invoice
+ sendEmailsAutomatically: `false` (boolean) - *recurring only*
    If `null`, system default is used
    Create and send the recurring invoice to client automatically. If not specified, taken from general settings
+ useCreditAutomatically: `false` (boolean) - *recurring only*
+ contractId: `000001`
+ contractLengthType (enum[number]) - *recurring only*
    + Members
        + `1` - Open end contract
        + `2` - Close end contract
+ minimumContractLengthMonths:`12` (number) - *recurring only*
    Minimum contract length in months
+ contractEndDate: `2017-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Required in case of **Close end contract**.
+ fullAddress: `2580 Orchard Pkwy, San Jose, CA, United States of America` (string)
+ street1: `2580 Orchard Pkwy`
+ street2
+ city: `San Jose`
+ countryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ stateId: `1` (number) - Applicable in case of **United States** and **Canada** only.
    See [Retrieve States](#reference/general/countriesidstates/get)
+ zipCode: `95131`
+ addressGpsLat: `37.*************` (number) - Latitude of service location
+ addressGpsLon: `-121.*********113` (number) - Longitude of service location
+ note (string)
+ fccBlockId: `***************` (optional) - Census block GEOID - used for FCC report generation
+ attributes (array[ServiceAttribute]) - To remove an attribute, set value to `null`.
+ discountType (enum[number]) - *recurring only*
    + Default: `0`
    + Members
        + `0` - No discount
        + `1` - Percentage discount
        + `2` - Fixed discount
+ discountValue: `5.5` (number) - *recurring only*
+ discountInvoiceLabel: `Discount` - *recurring only*
+ discountFrom: `2016-09-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Must match one of the selected service plan period's start dates.
+ discountTo: `2016-12-12T00:00:00+0000` (string) - *recurring only*
    Date string in the following format (see example). Must match one of the selected service plan period's end dates.
+ setupFeePrice: `42.90` (number) - *recurring only*
+ earlyTerminationFeePrice: `129.90` (number) - *recurring only*
+ servicePlanGroupId: `4d9e206c-ba3b-4893-9cf7-fb08a778f00d` (string, optional) - UUID *recurring only*
    See [Retrieve Service Plan Groups](#reference/service-plan-groups/service-plan-groupslimitoffset/get)

## ServiceActivate (object)
+ activeFrom: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ invoicingStart: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
    Service will be invoiced from this date including

## ServiceStatus (enum[number])
+ `0` - Prepared
+ `1` - Active
+ `2` - Ended
+ `3` - Suspended
+ `4` - Prepared blocked
+ `5` - Obsolete
+ `6` - Deferred
+ `7` - Quoted
+ `8` - Inactive

## ServiceChangeRequest (object)
+ serviceId: `1` (number, required)
    See [Retrieve Service](#reference/services/clientsservicesid/get)
+ targetServicePlanPeriodId: `11` (number, required)
    See [Retrieve Service Plan](#reference/service-plans/service-plansid/get)

## ServiceChangeRequestReadOnly (object)
+ id: `cafeb00f-ecca-4331-9e2b-ea98a1c706f6` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ Include ServiceChangeRequest

## ServiceTrafficShapingOverride (object)
+ downloadSpeedOverride: `100` (number, nullable) - Download speed override in Mbps. If `null`, it means unlimited speed. 
+ uploadSpeedOverride: `10` (number, nullable) - Upload speed override in Mbps. If `null`, it means unlimited speed. 
+ trafficShapingOverrideEnd: `2016-09-12T00:00:00+0000` (string, nullable) - Date string in the following format (see example). If filled, traffic shaping override will be automatically disabled on the specified date.

## ServiceAttribute (object)
+ value: `lorem ipsum` (string) - integer custom attribute type is a string representation of the number; boolean custom attribute type is a string representation of the 0 (i.e. false) or 1 (i.e. true); date custom attribute type is a string representation of the date in the format 2020-12-16
+ customAttributeId: `1` (number)
    See [Retrieve Custom Attributes](#reference/custom-attributes/custom-attributes/get)

## ServiceAttributeReadOnly (object)
+ id: `2c6984d7-03ae-4e48-a212-7b99378837a2` (string)
+ serviceId: `1000` (number)
+ name: `Service Code` (string)
+ key: `serviceCode` (string)
+ clientZoneVisible: `true` (boolean) - if false, attribute is hidden in Client Zone
+ Include ServiceAttribute

## ServiceSurcharge (object)
+ surchargeId: `1` (number, required)
    See [Retrieve Surcharges](#reference/surcharges/surcharges/get)
+ invoiceLabel: `Public IP` - If not specified, surcharge name will be used
+ price: `10.0` (number) - Price per period. If not specified, surcharge price will be used
+ taxable (boolean)
    + Default: `false`

## ServiceSurchargeReadOnly (object)
+ id: `1` (number)
+ serviceId: `1` (number) 
+ Include ServiceSurcharge

## ServiceUnmsClientSite (object)
+ unmsClientSiteId: `7b1c38b5-a8b5-48c7-867c-405668be0324` (string) - Connected UISP Location ID.

## PrepaidServicePeriod (object)
+ serviceId: `1` (number, required)
    See [Retrieve Service](#reference/services/clientsservicesid/get)
+ startDate: `2020-03-23` (string, required) - Date in `YYYY-MM-DD` format.
+ endDate: `2020-03-25` (string, required) - Date in `YYYY-MM-DD` format.

## PrepaidServicePeriodCreate (object)
+ Include PrepaidServicePeriod
+ useCredit: `false` (optional, boolean) - When `true`, an invoice is automatically created for the prepaid period and paid right away using the client's credit. (Default: `false`)

## PrepaidServicePeriodReadOnly (object)
+ id: `04a80595-0109-4ca4-9230-afd8d584d761` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ amount: `10.0` (number)
+ Include PrepaidServicePeriod

## Invoice (object)
+ number: `**********`
+ createdDate: `2016-09-12T00:00:00+0000` - (string) - Date string in the following format (see example). If not specified, current date will be used.
+ emailSentDate: `2016-09-12T00:00:00+0000` - (string, nullable) - Date string in the following format (see example) or null. Does not actually send the invoice by email when changed.
+ maturityDays: `14` (number) - If not specified, will be taken from global settings
+ notes - This text will be displayed on the invoice
+ discount: `5.0` (number)
+ discountLabel: `Quantity discount` (string) - Label of discount
+ adminNotes - This text will not be displayed on the invoice and is never shown to client
+ invoiceTemplateId: `1` (number) - If not specified, invoice template set on Organization is used
    See [Retrieve Invoice Templates](#reference/invoice-templates/invoice-templates/get)
+ proformaInvoiceTemplateId: `1` (number) - If not specified, proforma invoice template set on Organization is used
    See [Retrieve Proforma Invoice Templates](#reference/proforma-invoice-templates/proforma-invoice-templates/get)
+ organizationName: `Your ISP inc.` (string)
+ organizationRegistrationNumber: `RN123456` (string)
+ organizationTaxId: `TI123456` (string)
+ organizationStreet1: `2580 Orchard Parkway` (string)
+ organizationStreet2: `Suite G` (string)
+ organizationCity: `San Jose` (string)
+ organizationCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ organizationStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ organizationZipCode: `95131` (string)
+ organizationBankAccountName: `Primary` (string)
+ organizationBankAccountField1: `12345` (string)
+ organizationBankAccountField2: `6789` (string)
+ clientFirstName: `John` (string)
+ clientLastName: `Doe` (string)
+ clientCompanyName: `John Doe Company inc.` (string)
+ clientCompanyRegistrationNumber: `CRN123` (string)
+ clientCompanyTaxId: `CTI123` (string)
+ clientStreet1: `2580 Orchard Parkway` (string)
+ clientStreet2: `Suite E` (string)
+ clientCity: `San Jose` (string)
+ clientCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ clientStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ clientZipCode: `95131` (string)
+ proforma: false (boolean) - Invoice is proforma
+ payAutomatically: false (boolean) - Automatically pay the invoice using an autopay-enabled credit card. You can only set this field to true if the client has an autopay-enabled credit card. False by default.

## InvoiceNew (object)
+ number: `**********` - If not specified, the next invoice number will be generated automatically
+ items (array[InvoiceItemCollectionWritable], required)
+ Include Invoice
+ attributes (array[InvoiceAttribute])
+ applyCredit: true (boolean) - Automatically pay the invoice from available credit. True by default.
+ draft: true (boolean) - Create as a draft.

## InvoiceUpdate (object)
+ number: `**********` - If the invoice is draft, you can approve it by filling in the invoice number.
+ items (array[InvoiceItemCollectionWritable])
+ Include Invoice
+ attributes (array[InvoiceAttribute]) - To remove an attribute, set value to `null`.

## InvoiceReadOnly (object)
+ id: `1` (number)
+ Include InvoiceReadOnlyData

## InvoiceReadOnlyPreview (object)
+ id: (number, nullable)
+ Include InvoiceReadOnlyData

## InvoiceReadOnlyData (object)
+ clientId: `1` (number)
+ number: `**********`
+ Include Invoice
+ dueDate: `2016-09-26T00:00:00+0000` (string) - Date string in the following format (see example).
+ taxableSupplyDate: `2016-09-26T00:00:00+0000` (string) - Date string in the following format (see example).
+ items (array[InvoiceItemReadOnly])
+ subtotal: `15.0` (number)
+ taxes: (array[TotalTax])
+ total: `10.0` (number)
+ amountPaid: `10.0` (number)
+ amountToPay: `0.0` (number)
+ totalUntaxed: `10.0` (number)
+ totalDiscount: `5.0` (number)
+ totalTaxAmount: `0.0` (number)
+ currencyCode: `USD` (string)
+ status: `3` (InvoiceStatus)
+ paymentCovers: (array[PaymentCoverReadOnly])
+ uncollectible: true (boolean) - Invoice is marked as uncollectible
+ proformaInvoiceId: `7` (number, nullable) - If filled, contains ID of proforma invoice which triggered generation of this invoice after receiving payment.
+ generatedInvoiceId: `15` (number, nullable) - If filled, contains ID of regular invoice which was generated from this proforma invoice after receiving payment.
+ attributes (array[InvoiceAttributeReadOnly])
+ appliedVatReverseCharge: false (boolean) - If true, items are subject to reverse charge – VAT to be accounted for by the recipient.

## InvoiceStatus (enum[number])
+ `0` - Draft
+ `1` - Unpaid
+ `2` - Partially paid
+ `3` - Paid
+ `4` - Void
+ `5` - Processed proforma

## TotalTax (object)
+ name: `VAT 19% - 19%` (string) - Name and rate of tax
+ totalValue: `2.58` (number) - Sum of all taxes of this kind.

## InvoiceItem (object)
+ label: `Internet` (string) - If productId/serviceId/feeId is defined, the label can be either empty (product/service/fee label is used) or not (product/service/fee label is overridden). If productId/serviceId/feeId is not defined, the label is required.
+ price: `10.0` (number) - If productId/serviceId/feeId is defined, the price can be either empty (product/service/fee price is used) or not (product/service/fee price is overridden). If productId/serviceId/feeId is not defined, the price is required.
+ quantity (number) - If serviceId/feeId is defined, quantity must be empty (for service next not invoiced period is calculated automatically, for fee the quantity is always 1).
    + Default: `1`
+ unit
+ tax1Id: `1` (number)
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)

## InvoiceItemReadOnly (object)
+ id: `1` (number)
+ serviceId: `1` (number, nullable)
+ serviceSurchargeId: `1` (number, nullable)
+ productId: `1` (number, nullable)
+ feeId: `1` (number, nullable)
+ total: `10.0` (number, required)
+ type: `service` (InvoiceItemType)
+ discountPrice: `10.0` (number, nullable)
+ discountQuantity (number, nullable)
+ discountTotal (number, nullable)
+ Include InvoiceItem

## InvoiceItemCollectionWritable
+ Include InvoiceItem
+ productId: `1` (number)
    See [Retrieve Products](#reference/products/products/get). If this is not null, serviceId and feeId must be empty.
+ serviceId: `1` (number)
    See [Retrieve Services](#reference/services/clientsservicesorganizationidclientidstatuses5b5dcustomattributeidcustomattributevaluelimitoffset/get). Period to be invoiced is calculated automatically (next not invoiced period). Service surcharges, if exist, are included as invoice items. If this is not null, quantity, productId and feeId must be empty.
+ feeId: `1` (number)
    See [Retrieve Fees](#reference/fees/feeslimitoffsetclientidtypeinvoiced/get). Only uninvoiced fees can be added to an invoice. The fee will be automatically marked as invoiced when added. If this is not null, quantity, serviceId and productId must be empty.

## InvoiceItemWritable
+ label: `Custom item` (string)
+ price: `10.0` (number) - Can be changed only for custom invoice items.
+ quantity: `3.0` (number)
+ unit: `pcs` (string)
+ tax1Id: `1` (number)
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)

## InvoiceItemType (enum[string])
+ `service` - Service
+ `fee` - Fee
+ `product` - Product
+ `surcharge` - Service surcharge
+ `other` - Other

## Quote (object)
+ number: `**********`
+ createdDate: `2016-09-12T00:00:00+0000` - (string) - Date string in the following format (see example). If not specified, current date will be used.
+ notes - This text will be displayed on the quote
+ adminNotes - This text will not be displayed on the quote and is never shown to client
+ quoteTemplateId: `1` (number) - If not specified, quote template set on Organization is used
    See [Retrieve Quote Templates](#reference/quote-templates/quote-templates/get)
+ organizationName: `Your ISP inc.` (string)
+ organizationRegistrationNumber: `RN123456` (string)
+ organizationTaxId: `TI123456` (string)
+ organizationStreet1: `2580 Orchard Parkway` (string)
+ organizationStreet2: `Suite G` (string)
+ organizationCity: `San Jose` (string)
+ organizationCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ organizationStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ organizationZipCode: `95131` (string)
+ organizationBankAccountName: `Primary` (string)
+ organizationBankAccountField1: `12345` (string)
+ organizationBankAccountField2: `6789` (string)
+ clientFirstName: `John` (string)
+ clientLastName: `Doe` (string)
+ clientCompanyName: `John Doe Company inc.` (string)
+ clientCompanyRegistrationNumber: `CRN123` (string)
+ clientCompanyTaxId: `CTI123` (string)
+ clientStreet1: `2580 Orchard Parkway` (string)
+ clientStreet2: `Suite E` (string)
+ clientCity: `San Jose` (string)
+ clientCountryId: `249` (number)
    See [Retrieve Countries](#reference/general/countries/get)
+ clientStateId: `1` (number)
    See [Retrieve States](#reference/general/countriesidstates/get)
+ clientZipCode: `95131` (string)

## QuoteNew (object)
+ number: `**********` - If not specified, the next quote number will be generated automatically
+ items (array[QuoteItemWritable], required)
+ Include Quote

## QuoteUpdate (object)
+ number: `**********`
+ items (array[QuoteItemWritable])
+ Include Quote

## QuoteReadOnly (object)
+ id: `1` (number)
+ clientId: `1` (number)
+ number: `**********`
+ invoiceId: `8` (number, nullable) - If filled, contains ID of invoice related to this quote.
+ Include Quote
+ items (array[QuoteItemReadOnly])
+ subtotal: `15.0` (number)
+ discount: `5.0` (number)
+ discountLabel: `Quantity discount` (string) - Label of discount
+ taxes: (array[TotalTax])
+ total: `10.0` (number)
+ totalUntaxed: `10.0` (number)
+ totalDiscount: `5.0` (number)
+ totalTaxAmount: `0.0` (number)
+ currencyCode: `USD` (string)
+ status: `1` (QuoteStatus)
+ appliedVatReverseCharge: false (boolean) - If true, items are subject to reverse charge – VAT to be accounted for by the recipient.

## QuoteItem (object)
+ label: `Internet` (required)
+ price: `10.0` (number, required)
+ quantity (number)
    + Default: `1`
+ unit
+ tax1Id: `1` (number)
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax2Id: `2` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)
+ tax3Id: `3` (number) - If your pricing mode is set to "Tax inclusive pricing", only single tax can be set.
    See [Retrieve Taxes](#reference/taxes/taxes/get)

## QuoteItemWritable (object)
+ Include QuoteItem
+ productId: `1` (number)
    See [Retrieve Products](#reference/products/products/get). If this is not null, serviceId and feeId must be empty.
+ serviceId: `1` (number)
    See [Retrieve Services](#reference/services/clientsservicesorganizationidclientidstatuses5b5dcustomattributeidcustomattributevaluelimitoffset/get). Period to be invoiced is calculated automatically (next not invoiced period). Service surcharges, if exist, are included as invoice items. If this is not null, quantity, productId and feeId must be empty.
+ feeId: `1` (number)
    See [Retrieve Fees](#reference/fees/feeslimitoffsetclientidtypeinvoiced/get). Only uninvoiced fees can be added to an invoice. The fee will be automatically marked as invoiced when added. If this is not null, quantity, serviceId and productId must be empty.

## QuoteItemReadOnly (object)
+ id: `1` (number)
+ serviceId: `1` (number, nullable)
+ serviceSurchargeId: `1` (number, nullable)
+ productId: `1` (number, nullable)
+ feeId: `1` (number, nullable)
+ total: `10.0` (number, required)
+ type: `service` (QuoteItemType)
+ discountPrice: `10.0` (number, nullable)
+ discountQuantity (number, nullable)
+ discountTotal (number, nullable)
+ Include QuoteItem

## QuoteItemType (enum[string])
+ `service` - Service
+ `fee` - Fee
+ `product` - Product
+ `surcharge` - Service surcharge
+ `other` - Other

## QuoteStatus (enum[number])
+ `0` - Open
+ `1` - Accepted
+ `2` - Rejected

## InvoiceTemplateReadOnly (object)
+ id: `1` (number)
+ name: `Default` (string)
+ createdDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ isOfficial: `true` (boolean)
+ isValid: `true` (boolean)

## ProformaInvoiceTemplateReadOnly (object)
+ id: `1` (number)
+ name: `Default` (string)
+ createdDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ isOfficial: `true` (boolean)
+ isValid: `true` (boolean)

## InvoiceAttribute (object)
+ value: `lorem ipsum` (string) - integer custom attribute type is a string representation of the number; boolean custom attribute type is a string representation of the 0 (i.e. false) or 1 (i.e. true); date custom attribute type is a string representation of the date in the format 2020-12-16
+ customAttributeId: `1` (number)
    See [Retrieve Custom Attributes](#reference/custom-attributes/custom-attributes/get)

## InvoiceAttributeReadOnly (object)
+ id: `2000` (number)
+ invoiceId: `1000` (number)
+ name: `Personal Code` (string)
+ key: `personalCode` (string)
+ clientZoneVisible: `true` (boolean) - if false, attribute is hidden in Client Zone
+ Include InvoiceAttribute

## QuoteTemplateReadOnly (object)
+ id: `1` (number)
+ name: `Default` (string)
+ createdDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ isOfficial: `true` (boolean)
+ isValid: `true` (boolean)

## Payment (object)
+ clientId: `1` (number)
+ methodId: `d8c1eae9-d41d-479f-aeaf-38497975d7b3` (string, optional) - UUID
    See [Retrieve Payment Methods](#reference/payment-methods/payment-methods/get)
+ checkNumber - Applicable in case of **Check** method only.
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.
+ amount: `10.0` (number, required)
+ note: `Paid in coins.` (string)
+ providerName: `Worldpay` - Required in case of **Custom** method.
+ providerPaymentId: `WP451837` - Required in case of **Custom** method.
+ providerPaymentTime: `2016-09-12T00:00:00+0000` - (string) - Date string in the following format (see example). If not specified, current date will be used. Required in case of **Custom** method.
+ userId: `1` (number) - Id of User who created payment.

## PaymentReadOnly (object)
+ id: `1` (number)
+ currencyCode: `USD` (string)
+ receiptSentDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example) or null if receipt was not sent.
+ paymentCovers: (array[PaymentCoverReadOnly])
+ creditAmount: `10.0` (number)
+ attributes (array[PaymentAttributeReadOnly])
+ Include Payment

## PaymentWritable (object)
+ currencyCode: `USD` (string, nullable)
+ attributes (array[PaymentAttribute])
+ applyToInvoicesAutomatically: `true` (boolean) - Automatically applies payment to client's unpaid invoices.
    + Default: `true`
+ invoiceIds: `[1, 2]` (array[number]) - Assign the payment to invoices. If the amount is greater than the invoices total, the overpayment will be added to the credit automatically. If `applyToInvoicesAutomatically` is set to `true`, overpayment will be applied to client's unpaid invoices.
    See [Retrieve Invoices](#reference/invoices/invoicesorganizationidclientidcreateddatefromcreateddatetostatuses5b5dnumberoverdueproformacustomattributekeycustomattributevaluequerylimitoffsetorderdirection/get)
+ Include Payment

## PaymentUpdate (object)
+ note: `payment note` (string, nullable)
+ attributes (array[PaymentAttribute]) - To remove an attribute, set value to `null`.

## PaymentMethod (object)
+ name: `Cash`
+ visible: `true` (boolean)

## PaymentMethodReadOnly (object)
+ id: `6efe0fa8-36b2-4dd1-b049-427bffc7d369` (string)
+ sort: `1` (number)
+ isSystem: `true` (boolean)
+ Include PaymentMethod

## PaymentAttribute (object)
+ value: `lorem ipsum` (string) - integer custom attribute type is a string representation of the number; boolean custom attribute type is a string representation of the 0 (i.e. false) or 1 (i.e. true); date custom attribute type is a string representation of the date in the format 2020-12-16
+ customAttributeId: `1` (number)
    See [Retrieve Custom Attributes](#reference/custom-attributes/custom-attributes/get)

## PaymentAttributeReadOnly (object)
+ id: `2c6984d7-03ae-4e48-a212-7b99378837a2` (string)
+ paymentId: `1000` (number)
+ name: `Payment Code` (string)
+ key: `paymentCode` (string)
+ clientZoneVisible: `true` (boolean) - if false, attribute is hidden in Client Zone
+ Include PaymentAttribute

## PaymentTokenReadOnly (object)
+ token: `01b0ae1bd2998ce7e5d65e4a33b7955d`
+ clientId: `1` (number)
    See [Retrieve Clients](#reference/clients/clients/get)
+ invoiceId: `1` (number, optional)
    See [Retrieve Invoices](#reference/invoices/invoicesorganizationidclientidcreateddatefromcreateddatetostatuses5b5dnumberoverdueproformacustomattributekeycustomattributevaluequerylimitoffsetorderdirection/get)
+ amount: `249.90` (number)

## Refund (object)
+ methodId: `6efe0fa8-36b2-4dd1-b049-427bffc7d369` (string, nullable) - UUID
    See [Retrieve Payment Methods](#reference/payment-methods/payment-methods/get)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ amount: `10.0` (number, required)
+ note: `Very kind person.` (string)
+ clientId: `1` (number)
+ currencyCode: `USD` (string, nullable)

## RefundReadOnly (object)
+ id: `1` (number)
+ Include Refund
+ paymentCovers: (array[PaymentCoverReadOnly])

## PaymentPlan (object)
+ provider: `ippay` (enum[string], required) - For now only subscriptions for IPPay can be added with API.
    + Members
        + `ippay`
+ providerPlanId (string) - PaymentPlan ID, specific for every provider.
+ providerSubscriptionId (string) - Subscription ID, specific for every provider. For IPPay it's the token used to authorize the payments.
+ clientId: `1` (number, required)
+ currencyId: `33` (number, required)
+ amount: `10.0` (number, required)
+ period: `1` (enum[number], required) - Period in months.
    + Members
        + `1`
        + `3`
        + `6`
        + `12`
        + `24`
+ startDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).

## PaymentPlanReadOnly (object)
+ id: `1` (number)
+ name: `$249.05 / 3 months - John Doe`
+ status: `active` (enum[string])
    + Members
        + `created`
        + `pending`
        + `active`
        + `cancelled`
        + `paused`
        + `error`
+ createdDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ canceledDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ nextPaymentDate: `2017-09-12T00:00:00+0000` (string) - Date string in the following format (see example). Only used for IPPay subscriptions.
+ active (boolean)
+ Include PaymentPlan

## User (object)
+ id: `1` (number)
+ unmsId: `006b87db-bd7f-417a-85cb-555ba4ae3de6` (string)
+ email: `<EMAIL>` (string, required)
+ username: `john.doe` (string, required)
+ firstName: `John` (string)
+ lastName: `Doe` (string)
+ avatarColor: `#FFC107` - Color in hexadecimal format. If not specified, will be assigned randomly.
+ isActive (boolean)

## PermissionGroup (object)
+ id: `0282f519-aaca-47f3-99a9-190b6ac901cf` (string)
+ name: `Admin Group` (string)
+ scope: `organization` (enum[string], optional)
    + Members
        + `organization`
        + `client`
+ organizationId: `1` (number, optional)

## Organization (object)
+ name: `UBNT ISP` (required)
+ registrationNumber
+ taxId
+ phone
+ email
+ website
+ street1: `2580 Orchard Pkwy` (required)
+ street2
+ city: `San Jose` (required)
+ countryId: `249` (number, required)
    See [Retrieve Countries](#reference/general/countries/get)
+ stateId: `1` (number) - Applicable in case of **United States** and **Canada** only. If not specified, taken from default organization
    See [Retrieve States](#reference/general/countriesidstates/get)
+ currencyId: `33` (number, required)
    See [Retrieve Currencies](#reference/general/currencies/get)
+ zipCode: `95131` (required)
+ selected (boolean) - Is preselected while adding new client
    + Default: `false`
+ locale: `en_US`
+ bankAccountName: `primary account`
+ bankAccountField1: `********`
+ bankAccountField2: `123`

## OrganizationReadOnly (object)
+ id: `1` (number)
+ logoUrl: `https://your-domain.example.com/crm/uploads/logo/image.png`
+ Include Organization

## Tariff (object)
+ name: `Maxi` (required)
+ organizationId: `1` (number) - If not specified, default organization will be used
+ invoiceLabel: `Maxi` - If not specified, name will be used
+ downloadBurst: `200` (number) - Download burst in kB
+ uploadBurst: `100` (number) - Upload burst in kB
+ downloadSpeed: `50` (number) - Download speed in Mbps
+ uploadSpeed: `50` (number) - Upload speed in Mbps
+ dataUsageLimit: `100` (number) - Data usage limit in GB
+ downloadSpeedLimited: `2` (number) - Download speed after reaching data usage limit in Mbps
+ uploadSpeedLimited: `1` (number) - Upload speed after reaching data usage limit in Mbps
+ aggregation: `10` (number)
+ taxable (boolean)
    + Default: `false`
+ amountExemptFromTaxation: `0.0` (number, nullable)
+ setupFee: `42.90` (number) - *recurring only*
+ earlyTerminationFee: `129.90` (number) - *recurring only*
+ minimumContractLengthMonths: `6` (number) - *recurring only*
+ periods (array[TariffPeriod]) - *recurring only*
    By default 1 month period is enabled
+ public (boolean) - *recurring only*
    + Default: `false`
+ servicePlanType: `Internet` (enum[string])
    + Members
        + `Internet`
        + `General`
+ prepaid (boolean)
    + Default: `false`
+ prepaidConfiguration (TariffPrepaidConfiguration)
+ defaultServicePlanGroupId: `4d9e206c-ba3b-4893-9cf7-fb08a778f00d` (string, optional) - UUID *recurring only*
    See [Retrieve Service Plan Groups](#reference/service-plan-groups/service-plan-groupslimitoffset/get)
+ archived (boolean) - When is service plans archived, is not possible to create service plan change request with this service plan
    + Default: `false`

## TariffReadOnly (object)
+ id: `1` (number)
+ Include Tariff
+ periods (array[TariffPeriodReadOnly]) - *recurring only*
+ servicePlanGroups (array[ServicePlanGroupReadOnly]) - *recurring only*

## TariffPeriod (object)
+ period: `1` (enum[number]) - Service plan period in months
    + Members
        + `1`
        + `3`
        + `6`
        + `12`
        + `24`
+ price: `50.0` (number)
+ enabled (boolean)
    + Default: `false`

## TariffPeriodReadOnly (object)
+ id: `1` (number)
+ Include TariffPeriod

## TariffStatistics (object)
+ id: `1` (number)
+ activeServices: `42` (number)

## TariffPrepaidConfiguration (object)
+ pricePerDay: `1.99` (number) - *prepaid only*

## ServiceSuspend (object)
+ suspensionReasonId: `1` (number)
    See [Retrieve Service Suspension Reason](#reference/service-suspension/service-suspension-reasons/get)

## ServiceSuspensionReason (object)
+ name: `Moving to Prague` (required)

## ServiceSuspensionReasonReadOnly (object)
+ id: `1000` (number)
+ Include ServiceSuspensionReason

## ServicePause (object)
+ pauseDate: `2021-01-18T00:00:00+0000` (string) - Date string in the following format (see example).
+ reactivateDate: `2021-03-01T00:00:00+0000` (string) - Date string in the following format (see example).

## Surcharge (object)
+ name: `Public IP` (required)
+ invoiceLabel: `Public IP` - If not specified, name will be used
+ priceMonth1: `10.0` (number) - Price for 1 month periods
+ priceMonth2: `10.0` (number) - Price for 2 months periods
+ priceMonth3: `10.0` (number) - Price for 3 months periods
+ priceMonth6: `10.0` (number) - Price for 6 months periods
+ priceMonth12: `10.0` (number) - Price for 1 year periods
+ priceMonth24: `10.0` (number) - Price for 2 years periods
+ pricePerDay: `10.0` (number) - Price per day (used for prepaid services)
+ taxable (boolean)
    + Default: `false`

## SurchargeReadOnly (object)
+ id: `1` (number)
+ Include Surcharge

## Tax (object)
+ name: `VAT 10%` (string, required)
+ agencyName (string)
+ rate: `10.0` (number, required) - Tax rate in percentage
+ compound (boolean)
    + Default: `false`

## TaxReadOnly (object)
+ id: `1` (number)
+ Include Tax
+ selected (boolean) - Is selected to be assigned to every new client as default

## Product (object)
+ name: `UTP cable` (required)
+ invoiceLabel: `UTP cable` - If not specified, name will be used
+ price: `10.0` (number, required)
+ unit: `m`
+ taxable (boolean)
    + Default: `false`

## ProductReadOnly (object)
+ id: `1` (number)
+ Include Product

## PaymentCoverReadOnly (object)
+ id: `1` (number)
+ paymentId: `1` (number, optional) - Source payment of the cover (can be `null`, but `creditNoteId` will be filled in that case).
+ creditNoteId: `1` (number, optional) - Source payment of the cover (can be `null`, but `paymentId` will be filled in that case).
+ invoiceId: `1` (number, optional) - Invoice assigned to this cover (can be `null`, but `refundId` will be filled in that case).
+ refundId: `75` (number, optional) - Refund assigned to this cover (can be `null`, but `invoiceId` will be filled in that case).
+ amount: `123.23` (number, required) - Amount this cover takes from source payment.

## PaymentMatch (object)
+ clientId: `1` (number, required)
    See [Retrieve Clients](#reference/clients/clients/get)
+ currencyId: `33` (number)
    See [Retrieve Currencies](#reference/general/currencies/get)
+ sendReceipt: `true` (boolean) - If true, a payment receipt will be sent for the payment.
+ invoiceIds: `[1, 2]` (array[number]) - Assign the payment to invoices. If the amount is greater than the invoices total, the overpayment will be added to the credit automatically. 
    See [Retrieve Invoices](#reference/invoices/invoicesorganizationidclientidcreateddatefromcreateddatetostatuses5b5dnumberoverdueproformacustomattributekeycustomattributevaluequerylimitoffsetorderdirection/get)

## Job (object)
+ title: `CPE installation` (required, string)
+ description: `CPE is to be installed on location.` (string)
+ assignedUserId: `1` (number)
    See [Retrieve Users](#reference/users/usersadmins/get)
+ clientId: `1` (number)
    See [Retrieve Clients](#reference/clients/clients/get)
+ date: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ duration: `60` (number) - Duration of job in minutes
+ status `0` (JobStatus)
+ address: `2580 Orchard Pkwy, San Jose, CA 95131, USA` (string)
+ gpsLat: `37.*************` (string) - Latitude of location
+ gpsLon: `-121.*********113` (string) - Longitude of location

## JobWritable (object)
+ Include Job

## JobReadOnly (object)
+ id: `1000` (number)
+ Include Job
+ attachments (array[JobAttachmentReadOnly])
+ tasks (array[JobTaskReadOnly])

## JobStatus (enum[number])
+ `0` - Open
+ `1` - In progress
+ `2` - Closed

## JobAttachment (object)
+ jobId: `1000` (required, number)
    See [Retrieve Jobs](#reference/jobs/schedulingjobs/get)
+ filename: `foto.jpg` (required, string) - File name

## JobAttachmentWritable (object)
+ file: `` (required, string) - File encoded in base64.
+ Include JobAttachment

## JobAttachmentReadOnly (object)
+ id: `1` (number)
+ size: `6983` (number) - Size of file in bytes
+ mimeType: `image/png` - MIME Type of file
+ Include JobAttachment

## JobComment (object)
+ jobId: `1000` (required, number)
    See [Retrieve Jobs](#reference/jobs/schedulingjobs/get)
+ userId: `1` (number)
    See [Retrieve Users](#reference/users/usersadmins/get)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.
+ message: `This is a comment for this job.` (required, string)

## JobCommentWritable (object)
+ Include JobComment

## JobCommentReadOnly (object)
+ id: `2000` (number)
+ Include JobComment

## JobTask (object)
+ jobId: `1000` (required, number)
    See [Retrieve Jobs](#reference/jobs/schedulingjobs/get)
+ label: `This is a task label.` (required, string)
+ closed (boolean)
    + Default: `false`

## JobTaskWritable (object)
+ Include JobTask

## JobTaskReadOnly (object)
+ id: `2000` (number)
+ Include JobTask

## CustomAttribute (object)
+ name: `Personal Code`
+ clientZoneVisible: `true` (boolean) - if false, attribute is hidden in Client Zone
+ attributeType: `client` (enum[string])
    + Members
        + `client`
        + `invoice`
        + `payment`
        + `service`
+ type: `string` (enum[string])
    + Members
        + `string`
        + `integer`
        + `boolean`
        + `date`
        + `enum`

## CustomAttributeReadOnly (object)
+ id: `2000` (number)
+ key: `personalCode` (string)
+ Include CustomAttribute

## Ticket (object)
+ subject: `The Internet is not working` (string)
+ clientId: `1` (number) - If not set, `emailFromAddress` or `phoneFrom` is required.
    See [Retrieve Clients](#reference/clients/clients/get)
+ emailFromAddress: `<EMAIL>` (string) - When set, the ticket behaves as if imported by IMAP integration.
+ emailFromName: `John Doe` (string)
+ phoneFrom: `+420 223 123 123` (string)
+ assignedGroupId: `1` (number)
    See [Retrieve TicketGroups](#reference/ticket-groups/ticketingticket-groups/get)
+ assignedUserId: `1` (number)
    See [Retrieve Users](#reference/users/usersadmins/get)
+ createdAt: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.
+ status: (TicketStatus)
+ public (boolean) - Is visible to client?
+ assignedJobIds: `[2]` (array[number])
    See [Retrieve Jobs](#reference/jobs/schedulingjobsclientidassigneduseriddatefromdateto/get)

## TicketWritable (object)
+ Include Ticket
+ activity (required, array[TicketActivityWritable]) - At least one comment required.

## TicketReadOnly (object)
+ id: `1000` (number)
+ lastActivity: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ lastCommentAt: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ isLastActivityByClient: `true` (boolean) - if true, the last activity is on the client's side.
+ tags (array[TicketTagReadOnly])
+ Include Ticket
+ activity (array[TicketActivityReadOnly])

## TicketPatch (object)
+ Include Ticket
+ activity (array[TicketActivityWritable])

## TicketStatus (enum[number])
+ `0` - New
+ `1` - Open
+ `2` - Pending
+ `3` - Solved

## TicketActivity (object)
+ userId: `35` (number, optional) - ID of User or null if activity is created by client. When sending new comment as a logged in user, this can only be ID of current user or `null` if user has client impersonation permission.
    See [Retrieve Users](#reference/users/usersadmins/get)
+ createdAt: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.

## TicketActivityReadOnly (object)
+ Include TicketActivity
+ public (boolean) - Is visible to client?
+ id: `1` (number)
+ ticketId: `12` (number)
    See [Retrieve Ticket](#reference/tickets/ticket/get)
+ comment (TicketActivityCommentReadOnly, optional)
+ assignment (TicketActivityUserAssignment, optional)
+ clientAssignment (TicketActivityClientAssignment, optional)
+ statusChange (TicketActivityStatusChange, optional)
+ subjectChange (TicketActivitySubjectChange, optional)
+ jobAssignment (TicketActivityJobAssignment, optional)
+ groupAssignment (TicketActivityGroupAssignment, optional)
+ type: `comment` (enum[string]) - Type of activity
    + Members
        + `comment`
        + `assignment`
        + `assignment_client`
        + `assignment_job`
        + `assignment_group`
        + `status_change`
        + `subject_change`

## TicketActivityWritable (object)
+ Include TicketActivity
+ public (boolean) - Is visible to client?
    + Default: true
+ comment (TicketActivityCommentWritable)

## TicketActivityClientAssignment (object)
+ assignedClientId: `35` (number)
    See [Retrieve Clients](#reference/clients/clients/get)
+ type: `add` (enum[string])
    + Members
        + `add`
        + `remove`

## TicketActivityJobAssignment (object)
+ assignedJobId: `35` (number)
    See [Retrieve Jobs](#reference/scheduling/jobs/get)
+ type: `add` (enum[string])
    + Members
        + `add`
        + `remove`

## TicketActivityGroupAssignment (object)
+ assignedGroupId: `35` (number)
    See [Retrieve Ticket Groups](#reference/ticket-groups/ticketingticket-groups/get)
+ type: `add` (enum[string])
    + Members
        + `add`
        + `remove`

## TicketActivityCommentReadOnly (object)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string)
+ attachments (array[TicketCommentAttachmentReadOnly])

## TicketActivityCommentWritable (object)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string) - Required when **attachments** is not specified.
+ attachments (array[TicketCommentAttachmentWritable]) - Required when **body** is not specified.
+ emailFromAddress: `<EMAIL>` (string)
+ emailFromName: `John Doe` (string)
+ phoneFrom: `+420 223 123 123` (string)

## TicketActivityStatusChange (object)
+ previousStatus: `0` (TicketStatus)
+ status: `1` (TicketStatus)

## TicketActivitySubjectChange (object)
+ previousSubject: `No internet connection` (string)
+ subject: `Slow speed` (string)

## TicketActivityUserAssignment (object)
+ assignedUserId: `35` (number)
    See [Retrieve Users](#reference/users/users/get)
+ type: `add` (enum[string])
    + Members
        + `add`
        + `remove`

## TicketComment
+ userId: `35` (number, optional) - ID of User or `null` if activity is created by client. When sending new comment as a logged in user, this can only be ID of current user or `null` if user has client impersonation permission. 
    See [Retrieve Users](#reference/users/usersadmins/get)
+ public (boolean) - Is visible to client? It is not possible to create public comment to private **Ticket** it will be created as private.
    + Default: `true`
+ createdAt: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example). If not specified, current date will be used.
+ emailFromAddress: `<EMAIL>` (string) - Can be only set, if `userId` is null (comment is created by client).
+ emailFromName: `John Doe` (string) - Can be only set, if `userId` is null (comment is created by client).
+ phoneFrom: `+420 223 123 123` (string) - Can be only set, if `userId` is null (comment is created by client).

## TicketCommentWritable (object)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string) - Required when **attachments** is not specified.
+ attachments (array[TicketCommentAttachmentWritable]) - Required when **body** is not specified.
+ Include TicketComment
+ ticketId: `35` (required, number)
    See [Retrieve Ticket](#reference/tickets/ticket/get)

## TicketCommentReadOnly (object)
+ id: `1` (number)
+ ticketId: `35` (number)
    See [Retrieve Ticket](#reference/tickets/ticket/get)
+ body: `When I tried to turn on my PC, I see blue screen only.` (string)
+ attachments (array[TicketCommentAttachmentReadOnly])
+ Include TicketComment

## TicketCommentAttachment
+ filename: `foto.jpg` (required, string) - File name

## TicketCommentAttachmentWritable (object)
+ file: `` (required, string) - File encoded in base64.
+ Include TicketCommentAttachment

## TicketCommentAttachmentReadOnly (object)
+ id: `1` (number)
+ size: `6983` (number) - Size of file in bytes
+ mimeType: `image/png` - MIME Type of file
+ Include TicketCommentAttachment

## TicketGroup (object)
+ name: `lorem ipsum` (string)

## TicketGroupReadOnly (object)
+ id: `5` (number)
+ Include TicketGroup

## TicketGroupWriteOnly (object)
+ Include TicketGroup

## ContactType (object)
+ name: `Home contact` (string)

## ContactTypeReadOnly (object)
+ id: `3000` (number)
+ Include ContactType

## WebhookEvent (object)
+ uuid: `2c6984d7-03ae-4e48-a212-7b99378837a2` (string)
+ changeType: `insert` (string)
+ entity: `payment` (string)
+ entityId: `1` (number)

## Options (object)
+ appLocale: `en_US` (string) - Language used for Invoicing, Client Zone and as a default language for Admin Zone.
+ appTimezone: `Europe/Amsterdam` (string) - Timezone.
+ automaticBillingEnabled: `true` (boolean) - When disabled, no recurring invoices will be automatically generated. After you enable this again, the recurring invoices generation will resume where it ended.
+ autopayAndCreditCardSavingEnabled: `true` (boolean) - If enabled, clients will be able to save credit cards and configure autopay in the client zone.
+ autopayType: `1` (enum[number]) - When to trigger autopay.
    + Members
        + `1` - Invoices will be paid automatically right after they're created (or approved).
        + `2` - Invoices will be paid automatically on (or before) their due date.
+ autopayTypeDays: `0` (number) - How many days before the due date should the autopay trigger. Has no effect when `autopayType` is set to trigger on invoice creation / approval.
+ backupIncludePdf: `false` (boolean) - Include generated PDF files (invoices, quotes, etc.).
+ backupIncludeDownloads: `true` (boolean) - Include downloads (e.g. generated billing report).
+ backupIncludeMedia: `true` (boolean) - Include media (e.g. organization logo and stamp).
+ backupIncludeWebroot: `true` (boolean) - Include custom webroot files.
+ backupIncludeDocuments: `true` (boolean) - Include client documents.
+ backupIncludePlugins: `true` (boolean) - Include plugins.
+ backupIncludeTicketAttachments: `true` (boolean) - Include ticket attachments.
+ backupIncludeJobAttachments: `true` (boolean) - Include job attachments.
+ backupIncludeMailingAttachments: `true` (boolean) - Include mailing attachments.
+ balanceStyle: `US` (enum[string]) - Positive or negative sign for client's balance.
    + Members
        + `EU` - "+" for client's available funds, "-" for client's debit
        + `US` - "-" for client's available funds, "+" for client's debit
+ billingCycleType: `1` (enum[number]) - Determines whether prorated periods should always use 30-day months or the real day count of the current month, to calculate quantity.
    + Members
        + `0` - Real days count
        + `1` - 30 days in month
+ clientIdType: `1` (enum[number]) - Specifies which client ID will be displayed throughout UCRM. Administrators can see both IDs, but only the selected ID will be displayed in Client Zone. When "Custom" ID is selected, but not filled for a client, there will be no ID displayed in Client Zone.
    + Members
        + `1` - Default
        + `2` - Custom
+ clientZone: `true` (boolean) - If enabled, clients will be able to access the client zone.
+ clientZoneReactivation: `true` (boolean) - Enable clients to reactivate their ended service.
+ clientZonePaymentDetails: `false` (boolean) - If enabled, client will be able to see more detailed payment information.
+ clientZoneQuoteAccept: `false` (boolean) - If enabled, client will be able to accept or reject quote. This is not possible when the service on quote is not fully configured, i.e. missing "Active from" or "Invoicing starts".
+ clientZonePaymentAmountChange: `true` (boolean) - If enabled, client will be able to change amount of online payment.
+ clientZoneServicePlanShapingInformation: `true` (boolean) - If enabled, client will be able to see service download and upload speed.
+ clientZoneServiceServiceChangeRequests: `true` (boolean) - If enabled, client will be able to request service change.
+ clientZoneServiceServiceChangeRequestsApprove: `true` (boolean) - If enabled, service change requests will be approved automatically (deferred change will be automatically created for the next uninvoiced period).
+ clientZoneMinimumPasswordLength: `12` (number) - Required minimum password length.
+ clientZoneRequireSecurePassword: `false` (boolean) - If true, passwords will have to contain at least 1 uppercase letter, 1 lowercase letter and 1 symbol.
+ discountInvoiceLabel: `Discount` (string) - Default discount label. Can be overridden in any invoice.
+ earlyTerminationFeeInvoiceLabel: `Early termination fee` (string) - Early termination fee invoice label.
+ earlyTerminationFeeTaxable: `false` (boolean) - Early termination fee taxable. If enabled, taxes related to client will be applied.
+ earlyTerminationFeeTaxId: `0` (number) - If this tax is set, it will be always used for invoicing regardless of the taxes associated with the client.
+ fccAlwaysUseGps: `false` (boolean) - Use GPS instead of address.
+ fccGeocoderVintage: `Census2020_Current` (string) - FCC geocoder vintage.
+ formatDateDefault: `1` (enum[number]) - Default date format.
    + Members
        + `1` - D MMM YYYY
        + `2` - Do MMM YYYY
        + `3` - DD MMM YYYY
        + `4` - MMM D, YYYY
        + `5` - MMM Do, YYYY
        + `6` - MMM DD, YYYY
        + `7` - YYYY-MM-DD
        + `8` - DD-MM-YYYY
        + `9` - D.M.YYYY
        + `10` - DD.MM.YYYY
        + `11` - D. M. YYYY
        + `12` - DD. MM. YYYY
        + `13` - D/M/YYYY
        + `14` - DD/MM/YYYY
        + `15` - M/D/YYYY
        + `16` - MM/DD/YYYY
+ formatDateAlternative: `9` (enum[number]) - Alternative date format is used in communication with client (e.g. Invoices in PDF, Email notifications, ...).
    + Members
        + `1` - D MMM YYYY
        + `2` - Do MMM YYYY
        + `3` - DD MMM YYYY
        + `4` - MMM D, YYYY
        + `5` - MMM Do, YYYY
        + `6` - MMM DD, YYYY
        + `7` - YYYY-MM-DD
        + `8` - DD-MM-YYYY
        + `9` - D.M.YYYY
        + `10` - DD.MM.YYYY
        + `11` - D. M. YYYY
        + `12` - DD. MM. YYYY
        + `13` - D/M/YYYY
        + `14` - DD/MM/YYYY
        + `15` - M/D/YYYY
        + `16` - MM/DD/YYYY
+ formatDecimalSeparator: `.` (string) - Decimal separator symbol.
+ formatThousandsSeparator: `,` (string) - Thousands separator symbol.
+ formatTime: `1` (enum[number]) - Time format.
    + Members
        + `1` - h:mm a
        + `2` - H:mm
+ generateProformaInvoices: `false` (boolean) - If enabled, proforma invoice will be generated instead of regular invoice.
+ googleOauthSecret (string) - Google OAuth secret (e.g. for Google Calendar sync). Only present in the response if user has permission to view it.
+ headerNotificationsLifetime: `90` (number) - Lifetime of notifications logs.
+ generateInvoicesForNewServicesImmediately: `false` (boolean) - If enabled, an invoice will be generated right after creating a new service, instead of waiting for the next invoice generation window.
+ includeServiceBillingPeriodOnQuotes: `false` (boolean) - If enabled, service billing period is visible on the quote.
+ invoicePeriodStartDay: `0` (number) - Day in month when the recurring invoicing period starts.
+ invoiceTimeHour: `10` (number) - At this hour (in the 24-hour notation) recurring invoices will be generated.
+ invoicingPeriodType: `1` (enum[number]) - Billing period type.
    + Members
        + `1` - Backward: recurring invoices are created at the end of the billing period
        + `2` - Forward: recurring invoices are created at the beginning of the billing period (clients pay for the service in advance)
+ lateFeeActive: `false` (boolean) - If enabled, late fee will be created when an invoice is overdue.
+ lateFeeDelayDays: `1` (number) - Number of days for which the late fee creation is deferred. For example, if 3 days are set and invoice due date is 17th March the late fee will be created on 20th March.
+ lateFeeInvoiceLabel: `Late fee` (string) - Late fee invoice label.
+ lateFeePrice: `0.0` (number) - Late fee price.
+ lateFeePriceType: `1` (enum[number]) - Late fee price type.
    + Members
        + `1` - Currency
        + `2` - Percentage
+ lateFeeTaxable: `false` (boolean) - If enabled, taxes related to client will be applied.
+ lateFeeTaxId: `0` (number) - If this tax is set, it will be always used for invoicing regardless of the taxes associated with the client.
+ logLifetimeEmail: `720` (number) - Lifetime of email logs.
+ logLifetimeEntity: `90` (number) - Lifetime of system logs.
+ logLifetimeWebhookEvent: `10` (number) - Lifetime of webhook request logs.
+ fileLifetimeInvoice: `5` (number) - Lifetime of invoice files (in years).
+ fileLifetimeProformaInvoice: `5` (number) - Lifetime of proforma invoice files (in years).
+ fileLifetimeQuote: `5` (number) - Lifetime of quote files (in years).
+ fileLifetimeCreditNote: `5` (number) - Lifetime of credit note files (in years).
+ fileLifetimePaymentReceipt: `5` (number) - Lifetime of payment receipt files (in years).
+ fileLifetimeSchedulingAttachment: `5` (number) - Lifetime of scheduling attachment files (in years).
+ fileLifetimeTicketingAttachment: `5` (number) - Lifetime of ticketing attachment files (in years).
+ fileLifetimeMailingAttachment: `5` (number) - Lifetime of mailing attachment files (in years).
+ mailerAntifloodLimitCount: `20` (number) - Mailer Antiflood count limit. Number of messages to Antiflood sleep time.
+ mailerAntifloodSleepTime: `60` (number) - Mailer Antiflood sleep time.
+ mailerThrottlerLimitCount: `500` (number) - Mailer Throttler count limit. Number of messages sent within the throttler time limit.
+ mailerThrottlerLimitTime: `24` (number) - Mailer Throttler time limit.
+ mailerEnabled: `true` (boolean) - Mailer is enabled.
+ onlinePaymentsEnabled: `true` (boolean) - Online payment processing. When disabled, processing of all online payments will be stopped, all online payment related pages will display a maintenance notice and all webhook events sent from payment processors will be rejected.
+ pdfPageSizeExport (PdfPageSize) - Export page size.
+ pdfPageSizeExportCustomWidth: `210` (number) - Export page size - custom width (mm). Only used when `pdfPageSizeExport` is `custom`.
+ pdfPageSizeExportCustomHeight: `297` (number) - Export page size - custom height (mm). Only used when `pdfPageSizeExport` is `custom`.
+ pdfPageSizeInvoice (PdfPageSize) - Invoice page size.
+ pdfPageSizeInvoiceCustomWidth: `210` (number) - Invoice page size - custom width (mm). Only used when `pdfPageSizeInvoice` is `custom`.
+ pdfPageSizeInvoiceCustomHeight: `297` (number) - Invoice page size - custom height (mm). Only used when `pdfPageSizeInvoice` is `custom`.
+ pdfPageSizePaymentReceipt (PdfPageSize)- Payment receipt page size.
+ pdfPageSizePaymentReceiptCustomWidth: `210` (number) - Payment receipt page size - custom width (mm). Only used when `pdfPageSizePaymentReceipt` is `custom`.
+ pdfPageSizePaymentReceiptCustomHeight: `297` (number) - Payment receipt page size - custom height (mm). Only used when `pdfPageSizePaymentReceipt` is `custom`.
+ pricingMode: `1` (enum[number]) - Pricing mode. Whether all item prices stored in UCRM are considered to be with or without tax. Has no effect on existing invoices.
    + Members
        + `1` - Tax exclusive pricing
        + `2` - Tax inclusive pricing
+ pricingTaxCoefficientPrecision: `2` (number) - Decimal places for tax coefficient. Used to calculate the taxed amount and consequently, also the net price from the given gross price.
+ subscriptionsEnabledCustom: `false` (boolean) - Custom subscriptions allow setting custom amount, payment frequency and start date when creating it.
+ subscriptionsEnabledLinked: `false` (boolean) - Linked subscriptions is linked to client's service. Start date can be changed when creating it, but amount and frequency are fixed to be the same as the service.
+ approveAndSendInvoiceAutomatically: `true` (boolean) - If enabled, invoice drafts are automatically approved and sent by email right after generating.
+ sendInvoiceByPost: `false` (boolean) - If enabled, the approved invoices are marked to be sent by post. You can then batch print only these.
+ serverHostname: `localhost` (string) - Server hostname or IP.
+ serverIp: `127.0.0.1` (string) - Server IP.
+ serviceInvoicingDayAdjustment: `0` (number) - Create invoice X days in advance. Create the recurring invoices earlier before the default create date.
+ setupFeeInvoiceLabel: `Setup fee` (string) - Fee invoice label.
+ setupFeeTaxable: `false` (boolean) - If enabled, taxes related to client will be applied.
+ setupFeeTaxId: `0` (number) - If this tax is set, it will be always used for invoicing regardless of the taxes associated with the client.
+ siteName: `My site` (string) - Site name.
+ stopInvoicing: `false` (boolean) - If enabled, backward invoicing will be suppressed for periods which have been suspended, i.e. the suspension lasted for the whole billing period.
+ stopServiceDue: `true` (boolean) - Suspend services if payment overdue.
+ stopServiceDueDays: `0` (number) - Suspension delay. "0" to suspend the service the day after the invoice due date.
+ supportEmailAddress: `<EMAIL>` (string) - Recipient for Client zone's contact form and the reply-to address for all emails sent to clients. Organization email is used as a fallback.
+ suspendTimeHour: `10` (number) - At this hour (in the 24-hour notation) services will be suspended.
+ suspensionEnablePostpone: `true` (boolean) - Enable clients to postpone their suspension by 24 hours. This also enables clients to pay online directly from the suspension page without entering the Client Zone.
+ suspensionMinimumUnpaidAmount: `0.0` (number) - Minimum unpaid amount of an overdue invoice to activate suspension. For example, if set to 5 the service would only be suspended, if unpaid amount on the invoice is greater than or equal to 5. This option is only applied per invoice, it does not consider client's total balance.
+ suspendQuoted: `false` (boolean) - Suspend quoted services.
+ ticketingEnabled: `true` (boolean) - Enables tickets in Client Zone. A new ticket will be created when client use the contact form.
+ ticketingImapAutomaticReplyEnabled: `false` (boolean) - Enable automatic reply. When enabled, new tickets created by IMAP integration will receive an automatic reply.
+ ticketingImapAttachmentFilesizeImportLimit: `10.0` (number) - Attachments exceeding the maximum allowed file size will not be imported into UCRM. You can view these attachments on demand.
+ ticketDefaultVisibility: `0` (enum[number]) - Default ticket visibility.
    + Members
        + `0` - Public
        + `1` - Private
+ notificationCreatedDraftsByEmail: `true` (boolean) - If enabled, system email notification is sent when invoice drafts are generated.
+ notificationCreatedDraftsInHeader: `true` (boolean) - If enabled, CRM notification is created when invoice drafts are generated.
+ notificationCreatedInvoicesByEmail: `true` (boolean) - If enabled, system email notification is sent when invoices are generated and automatically approved.
+ notificationCreatedInvoicesInHeader: `true` (boolean) - If enabled, CRM notification is created when invoices are generated and automatically approved.
+ notificationCreditCardPaymentAuthenticationRequired: `true` (boolean) - If turned on, email notification is sent to client when credit card payment requires authentication.
+ notificationCreditCardPaymentFailed: `true` (boolean) - If turned on, email notification is sent to client when credit card payment fails.
+ notificationDataUsageExceededFirstLimit: `true` (boolean) - If enabled, email is sent to client when configured percentage of data usage limit is exceeded.
+ notificationDataUsageFirstLimitPercent: '80' (number) - Percentage of data usage limit when first email is sent.
+ notificationDataUsageExhausted: `true` (boolean) - If enabled, email is sent to client when data usage limit is exhausted.
+ notificationEmailAddress: `<EMAIL>` (string) - System notification address. All UCRM system notifications are sent to this address. If system notification address is not configured, no system notification emails will be sent. Only present in the response if user has permission to view it.
+ sandboxRedirectEmailAddress: `<EMAIL>` (string) - Sandbox redirect address. All CRM outgoing emails will be sent to this address instead of their original address. Only present in the response if user has permission to view it.
+ notificationInvoiceNearDue: `false` (boolean) - Send notifications for invoices near due date. If enabled, email notification is sent to client when invoice is near due date.
+ notificationInvoiceNearDueDays: `7` (number) - How many days before the due date send the notification.
+ notificationInvoiceNew: `true` (boolean) - If enabled, email notification is sent to client when new invoice is generated.
+ notificationInvoiceOverdue: `true` (boolean) - If enabled, email notification is sent to client when invoice is overdue.
+ notificationInvoiceOverdueDays: `1` (number) - How many days after the due date send the notification.
+ notificationInvoiceOverdueRetry1: `3` (number) - If greater than 0, overdue invoice notification is sent again X days after the previous attempt.
+ notificationInvoiceOverdueRetry2: `5` (number) - If greater than 0, overdue invoice notification is sent again X days after the previous attempt.
+ notificationInvoiceOverdueRetry3: `0` (number) - If greater than 0, overdue invoice notification is sent again X days after the previous attempt.
+ notificationJobAssignedByEmail: `true` (boolean) - If enabled, email notification is sent to assigned user.
+ notificationJobAssignedInHeader: `true` (boolean) - If enabled, CRM notification is created for assigned user
+ notificationServiceSuspended: `true` (boolean) - If enabled, email notification is sent to client whose service was automatically suspended because of an overdue invoice.
+ notificationServiceSuspensionPostponed: `true` (boolean) - Service suspension postponed. If enabled, email notification is sent to client whose suspension has been manually postponed from the "walled garden" page.
+ notificationSubscriptionNew: `true` (boolean) - If enabled, email notification is sent to client when new subscription is created.
+ notificationSubscriptionCancelled: `true` (boolean) - If enabled, email notification is sent to client when subscription is cancelled.
+ notificationSubscriptionAmountChanged: `true` (boolean) - If enabled, email notification is sent to client when subscription amount is changed.
+ sendInvoiceWithZeroBalance: `true` (boolean) - If disabled, only invoices with balance higher than 0 will be sent.
+ notificationTicketClientCreatedByEmail: `true` (boolean) - If enabled, system email notification is sent when client submits ticket.
+ notificationTicketClientCreatedInHeader: `false` (boolean) - If enabled, CRM notification is created when client submits ticket.
+ notificationTicketCommentClientCreatedByEmail: `true` (boolean) - If enabled, system email notification is sent when client comments ticket.
+ notificationTicketCommentClientCreatedInHeader: `false` (boolean) - If enabled, CRM notification is created when client comments ticket.
+ notificationTicketCommentUserCreatedByEmail: `true` (boolean) - If enabled, email notification is sent to client when ticket is commented.
+ notificationTicketUserCreatedByEmail: `true` (boolean) - If enabled, email notification is sent to client when ticket is created by admin.
+ notificationTicketAssignedInHeader: `false` (boolean) - If enabled, CRM notification is created when client submits ticket.
+ notificationTicketAssignedByEmail: `true` (boolean) - If enabled, system email notification is sent when ticked is assigned to user.
+ notificationTicketUserChangedStatus: `false` (boolean) - If enabled, email notification is sent to assigned client when ticket changes status.
+ sendPaymentReceipts: `false` (boolean) - Send payment receipts automatically. If enabled, payment receipt emails will be sent automatically after receiving online payments. It is also used as the default while creating a payment manually.
+ sendPaymentReceiptsWithPdf: `false` (boolean) - If enabled, email notification will contain payment receipt in PDF as attachment.
+ notificationNewServiceChangeRequestByEmail: `true` (boolean) - If enabled, system email notification is sent when client requests a service change.
+ notificationNewServiceChangeRequestInHeader: `false` (boolean) - If enabled, CRM notification is created when client requests a service change.
+ notificationServiceSuspendedByEmail: `true` (boolean) - If enabled, system email notification is sent when service is suspended.
+ notificationServiceSuspendedInHeader: `false` (boolean) - If enabled, CRM notification is created when service is suspended.
+ serviceDataUsageVisibleInClientZone: `false` (boolean) - If enabled, service data usage is visible in the Client zone.
+ enableSsoLoginButton: `true` (boolean) - If enabled, clients will be able to sign into Client Zone with Ubiquiti account.
+ emailDeliveryWindowHourStart: `8` (number) - If enabled, automatically generated emails are sent only during the specified time window.
+ emailDeliveryWindowHourEnd: `16` (number) - If enabled, automatically generated emails are sent only during the specified time window.
  
## PdfPageSize (enum[string])
+ `letter` - US letter
+ `half-letter` - US half letter
+ `legal` - US legal
+ `A4` - A4
+ `A5` - A5
+ `custom` - Custom

## Email (object)
+ to: `<EMAIL>` (string, required) - recipient email
+ subject: `Hello World!` (string, required)
+ body: `Lorem ipsum dolor sit amet.` (string, required)
+ clientId: `35` (number, optional)
    When specified, the message will appear in client's email log.
    See [Retrieve Clients](#reference/clients/clients/get)
+ attachments (array[EmailAttachment])

## EmailAttachment
+ file: `SGVsbG8gV29ybGQhCg==` (required, string) - File encoded in base64.
+ filename: `hello_world.txt` (required, string) - File name
+ mimeType: `text/plain` - MIME Type of file

## LocationData (object)
+ lat: `40.7518983` (number) - latitude of the result
+ lon: `-73.9733989` (number) - longitude of the result
+ displayName: `685, 3rd Avenue, Murray Hill, Manhattan Community Board 6, Manhattan, New York County, New York, 10017, United States of America` (string) - name of the result
+ addressComponents: (enum)
    + members:
        + street1: `685 3rd Avenue` (string) - house number and street name, if available
        + street2 (string) - additional details, if available
        + city: `New York` (string) - city, if available
        + zipCode: `10017` (string) - ZIP code or local equivalent, if available
        + state: `New York` (string) - state, if available
        + stateCode: `NY` (string) - state code, if available
        + stateId: `32` (number) - stateId as saved by UCRM, if available
        + country: `United States` (string) - country, if available
        + countryCode: `US` (string) - country code, if available
        + countryId: `249` (number) - countryId as saved by UCRM, if available

## AddressSuggestionData (object)
+ address: `685, 3rd Avenue, Murray Hill, Manhattan Community Board 6, Manhattan, New York County, New York, 10017, United States of America` (string)
+ id: `placeId/ChIJOwg_06VPwokRYv534QaPC8g` (string)
    If `id` is provided (i.e. is not `null`), you should use it in the geocode request afterwards instead of address.
    In case of geocoding via Google, this ensures correct usage of session tokens (if you send them) and thus decreases price.
    You should then regenerate the session token, see the `sessionToken` request attribute for more information.

## GeocoderResultData (object)
+ type: `OpenStreetMap` (enum[string]) - geocoder type
    + Members
        + `OpenStreetMap`
        + `GoogleMaps`
+ formattedAddress: `Bytha Way, Silverado, California, USA` (string, optional) - full address, as returned by geocoder
+ latitude: `-51.2` (number, optional) - latitude as returned by geocoder
+ longitude: `103.1415926` (number, optional) - longitude as returned by geocoder
+ addressComponents (array) - address parts, as returned by geocoder. The inner structure of the address components will vary by geocoder type; passing in an invalid structure will result in an unresolved address.

## InventoryWarehouse (object)
+ name: `Lakeside Storage` (string)

## InventoryWarehouseReadOnly (object)
+ id: `beef000f-ecca-4331-9e2b-ea98a1c706f6` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ Include InventoryWarehouse
## InventoryProduct (object)
+ name: `Unifi Dream Machine` (string)

## InventoryProductReadOnly (object)
+ id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ type: `ubiquiti_devices` (enum[string], required)
    + Members
        + ubiquiti_devices
        + ubiquiti_accessories
        + other
+ Include InventoryProduct
## InventoryUnit (object)
+ productId: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string)
    See [Retrieve Inventory Product](#reference/inventory/inventory/products/get)
+ macAddress: `fc-ec-da-00-ca-fe` (string, optional) MAC address of the unit
+ customIdentification: `BBK1NGOOOO1` (string, optional) Custom identification of the unit
+ nmsUserId: (string, optional) User ID as returned by NMS
+ nmsSiteId: (string, optional) Site ID as returned by NMS
+ warehouseId: `cafecafe-ecca-4331-9e2b-ea98a1c706f6` (string)
    See [Retrieve Inventory Warehouse](#reference/inventory/inventory/warehouses/get)
    Note: at most one of {nmsUserId,nmsSiteId,warehouseId} can be set at any given time 

## InventoryMoveUnit (object)
+ nmsUserId: (string, optional) User ID as returned by NMS
+ nmsSiteId: (string, optional) Site ID as returned by NMS
+ warehouseId: `cafecafe-ecca-4331-9e2b-ea98a1c706f6` (string)
    See [Retrieve Inventory Warehouse](#reference/inventory/inventory/warehouses/get)
    Note: at most one of {nmsUserId,nmsSiteId,warehouseId} can be set at any given time 

## InventoryUnitReadOnly (object)
+ id: `cafeb00f-ecca-4331-9e2b-ea98a1c706f6` (string)
+ createdDate: `2016-09-12T00:00:00+0000` (string) - Date string in the following format (see example).
+ Include InventoryUnit

## ServicePlanGroup (object)
+ name: `Wireless` (string)

## ServicePlanGroupReadOnly (object)
+ id: `faafe1e3-0258-424d-a0ce-e85252bb9756` (string) - UUID
+ Include ServicePlanGroup

## CreditCardReadOnly (object)
+ id: `9e3e500f-ecca-4331-9e2b-ea98a1c706f6` (string) - UUID
+ clientId: `1` (number)
    See [Retrieve Clients](#reference/clients/clients/get)
+ providerId: `card_1GVv3l2eZvKYlo2CoZfEkF6B` (string)
+ brand: `mastercard` (string)
+ brandForView: `Mastercard®` (string)
+ expirationMonth: `8` (number)
+ expirationYear: `2025` (number)
+ last4: `5821` (string)
+ createdDate: `2020-04-08T00:00:00+0000` (string) - Date string in the following format (see example).
+ lastSuccessfulPaymentDate: `2020-04-08T00:00:00+0000` (string) - Date string in the following format (see example).
+ useForAutopay: `false` (boolean)
+ monthlyLimit: `1000.0` (number)

## Fee (object)
+ id: `1` (number)
+ clientId: `1` (number)
    See [Retrieve Client](#reference/clients/clientsid/get)
+ invoiced: `false` (boolean) - Already invoiced fees can't be deleted.
+ type: `1` (enum[number])
    + Members
        + `1` - late fee
        + `2` - setup fee
        + `3` - early termination fee
+ createdDate: `2020-04-08T00:00:00+0000` (string) - Date string in the following format (see example).
+ serviceId: `1` (number) - Is set when the fee was created due to a service (setup fee or early termination fee).
    See [Retrieve Service](#reference/services/clientsservicesid/get)
+ dueInvoiceId: `1` (number) - Is set when the fee was created due to an overdue invoice (late fee).
    See [Retrieve Invoice](#reference/invoices/invoicesid/get)
+ taxable: `false` (boolean) - If true, taxes related to client will be applied when invoiced.
+ price: `10.0` (number)
+ invoiceLabel: `Setup fee` (string)

## SuspensionPeriod (object)
+ id: `1` (number)
+ startDate: `2020-03-23T00:00:00+0000` - (string) - Date string in the following format (see example).
+ endDate: `2020-06-12T00:00:00+0000` - (string, nullable) - Date string in the following format (see example) or null.
