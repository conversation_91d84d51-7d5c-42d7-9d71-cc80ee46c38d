Krátkodobý majetok - Short-time property;;;;;;
<PERSON><PERSON><PERSON> pre Import a Export - Applicable for Import and Export;;;;;;
Č.sĺpca;Označenie;;Hodnota;Max počet znakov v reťazci;Stĺpec v databáze;Popis
Column #;Indication;;Value;Maximum characters in string;Column in database;Description
1;A;;R00;;;
2;B;;T15;;;T15 (Krátkodobý majetok) - T15 (Short-time property)
;;;;;;
1;A;;R01;;T224_DrMajetok;
2;B;;Inventurne cislo - inventory number;text(12);C030_InventurneCislo;
3;C;;Datum zaradenia - Date of inclusion;;"C060_Den; C061_Mes; C062_Rok";
4;D;;Datum vyradenia - Date of exclusion;;"C063_DenVyrad; C064_MesVyrad; C065_RokVyrad";
5;E;;Nazov - Name;text(50);C100_Nazov;
6;F;;Typ majetku - Type of property;number/integer;C101_TypMajetku;zadáva sa 0 ako hmotný majetok, 1 ako nehmotný
7;G;;Doklad nakupu - Purchasing document;text(20);C102_DokladNakup;
8;H;;Cena - price;currency;C103_Cena;
9;I;>>;Vyrobne cislo - Serial number;text(40);C099_VyrobneCislo;
10;J;;Kod evidencie - Code of evidence;text(5);C022_KodEvidencia;
11;K;;Kod ciselna rada  - Sequence code;text(5);C021_KodCiselnaRada;
12;L;;Sposob vyradenia - Method of exclusion;text(50);C100_Nazov(T227_SposobyVyradenia);pokiaľ sa zadáva vyradenie, je nutné zadať dátum a spôsob vyradenia
;;;;;;if exclusion is entered, you must specify the date and method of exclusion
13;M;;Poznamka - note;memo;C097_Poznamka;
;;;;;;
1;A;;R02;;;
2;B;;T16;;;kod T16 oznacuje sekciu so SZČP - T16 indicates the code section with SZČP
3;C;;kod stredisko - code of center;text(5);C021_Kod (T011_Outline);
4;D;;kod zakazka -code of order;text(5);C021_Kod (T011_Outline);
5;E;;kod cinnost - code of operation;text(5);C021_Kod (T011_Outline);
6;F;;kod pracovnik - code of worker;text(5);C021_Kod (T105_Pracovnici);
7;G;;Suma - Amount;number/double;C100_Ciastka;
8;H;;cislo stredisko - center number;text(20);C030_CisloInterne (T011_Outline);
9;I;;nazov stredisko - name of center;text(50);C101_Nazov (T011_Outline);
10;J;;cislo zakazka - order number;text(20);C030_CisloInterne (T011_Outline);
11;K;;nazov zakazka - name of order;text(50);C101_Nazov (T011_Outline);
12;L;;cislo cinnost - operation number;text(20);C030_CisloInterne (T011_Outline);
13;M;;nazov cinnost - name of operation;text(50);C101_Nazov (T011_Outline);
14;N;;cislo pracovnik - worker number ;text(20);C030_CisloInterne (T105_Pracovnici);
15;O;;meno pracovnik - name of worker;text(30);C100_Meno (T105_Pracovnici);
16;P;;priezvisko pracovnik - surname of worker;text(30);C101_Priezvisko (T105_Pracovnici);
