Skladove karty - Storage card;;;;;;
Platí pre Import a Export - Applicable for Import and Export;;;;;;
Č.sĺpca;Označenie;;Hodnota;Max počet znakov v reťazci;Stĺpec v databáze;Popis
Column #;Indication;;Value;Maximum characters in string;Column in database;Description
1;A;;R00;;;
2;B;;T03;;;T03 (Skladove karty) - T03 (Storage card)
;;;;;;
1;A;;R01;;;sklad - store
2;B;;nazov skladu -  store name;text(50);C100_Nazov(T200_Sklad);
3;C;>>;kod - code;text(5);C021_Kod(T200_Sklad);
4;D;;ulica - street;text(40);C047_Ulica(T200_Sklad);
5;E;;PSC - postal code;text(6);C048_PSC(T200_Sklad);
6;F;;mesto - city;text(40);C049_Mesto(T200_Sklad);
7;G;;typ ceny P/F - type of price P/F;;C101_SkladovaCena(T200_Sklad);zadáva sa P ako priemerovaný sklad/ F ako FIFO sklad - is entered P as averaged /F as FIFO
8;H;;MJ - Unit of measurement;text(5);C108_Jednotka(T200_Sklad);
9;I;;S ucet - S account;text(3);C225_SyntetickyUcet(T200_Sklad);synthetic account
10;J;;A ucet - A account;text(6);C226_AnalytickyUcet(T200_Sklad);analytical account
11;K;;S ucet fakturacia ;text(3);C227_SyntetickyUcetFakt(T200_Sklad);synthetic account of invoicing
12;L;;U ucet fakturacia;text(6);C228_AnalytickyUcetFakt(T200_Sklad);analytical account of invoicing
13;M;;poznamka - note;meno;C097_Poznamka(T200_Sklad);
14;N;;sadzba DPH 0/N/V - VAX rate;;C109_SadzbaDPH(T200_Sklad);"zadáva sa nulová ako 0, vyššia ako V, nižšia ako N, znížená 2 ako Y a neobsahuje ako X;  0 - null, N - lower, Y = lower 2, V - higher, X - not contain"
15;O;;dovozna prirazka - import surcharge;number/single;C110_DovoznaPrirazka(T200_Sklad);
16;P;;clo;number/single;C111_Clo(T200_Sklad);
;;;;;;
1;A;;R02;;;skladová karta - store card
2;B;;nazov karty - card name;text(200);C100_Nazov;
3;C;;cislo - number;text(20);C030_CisloKarty;
4;D;>>;MJ - Unit of Measurement ;text(5);C107_Jednotka;
5;E;;EAN;text(18);C102_EAN;
6;F;;PLU;number/long integer;C103_PLU;
7;G;;JPKOV;text(12);C104_JKPOV;
8;H;;min;number/double;C105_Min;
9;I;;max;number/double;C106_Max;
10;J;;cudzi nazov - foreign title ;text(50);C101_Nazov2;
11;K;;sadzba DPH 0/N/V - VAX rate;;C111_SadzbaDPH;"zadáva sa nulová ako 0, vyššia ako V, nižšia ako N, znížená 2 ako Y a neobsahuje ako X;  0 - null, N - lower, Y = lower 2, V - higher, X - not contain"
12;L;;S ucet - S account;text(3);C225_SyntetickyUcet;synthetic account
13;M;;A ucet - A account;text(6);C226_AnalytickyUcet;analytical account
14;N;;S ucet fakturacia;text(3);C227_SyntetickyUcetFakt;synthetic account of invoicing
15;O;;U ucet fakturacia;text(6);C228_AnalytickyUcetFakt;analytical account of invoicing
16;P;;volna polozka - free item;text(50);C115_Volne;
17;Q;;poznamka - note;memo;C097_Poznamka;
18;R;;Skrateny nazov - abbreviated name;memo;C121_SkratenyNazov;
19;S;;Hmotnost MJ - mass of UoM;number/double;C128_HmotnostMJ;UoM - unit of measurement
20;T;;Zarucna doba - warranty;number/double;C116_ZarucnaDoba;
21;U;;Zarucna doba typ - type of warranty;number/integer;C117_ZarucnaDobaTyp;
22;V;;CSN;text(12);C122_CSN;
23;W;;DIN;text(12);C123_DIN;
24;X;;BN;text(12);C124_BN;
25;Y;;dovozna prirazka - import surcharge;number/single;C113_DovoznaPrirazka;
26;Z;;Clo;number/single;C114_Clo;
27;AA;;Colny sadzobnik - customs tariff;text(10);C129_ColnySadzobnik;
28;AB;;Bezne mnozstvo - standard quantity;number/double;C125_BezneMnozstvo;
29;AC;;ID Linkovanej karty - ID linked card;number/long integer;C011_IDLinkKarta;
30;AD;;Typ karty - type of card;number/integer;C120_TypKarty;zadáva sa 0 ako jednoduchá, 1 ako karta s výrobnými číslami, 2 ako karta so šaržami -  is etered 0 as simple, 1 with serial numbers, 2 with batch
31;AE;;Tovarova skupina - Commodity group;number/integer;C132_TovarovaSkupina;
32;AF;;Predvolena PC -  default SP;currency;C136_PredvolenaPredajnaCena;SP -selling price
33;AG;;Predvolena PC s DPH - default SP with VAT;currency;C137_PredvolenaPredajnaCenaSDPH;default SP with VAT
34;AH;;Synteticky ucet obstarania;text(3);C229_SyntetickyUcetObst;synthetic account of acquisition
35;AI;;Analyticky ucet obstarania;text(6);C230_AnalytickyUcetObst;analytical account of acquisition
36;AJ;;Kod krajiny povodu - country of origin code;text(2);C026_KodKrajinyPovodu_IST;
37;AK;;Opis tovaru - description of goods;memo;C098_OpisTovaru_IST;
38;AL;;Kod tovaru IST - goods code IST;text(10);C027_KodTovaru_IST;
39;AM;;Koeficient pre KG IST - coefficient KG IST;number/double;C138_KoeficientPreKG_IST;
40;AN;;Koeficient pre DMJ IST - coefficient DMJ IST;number/double;C139_KoeficientPreDMJ_IST;coefficient for additional unit of measurement
41;AO;;Neaktivna - inactiv;number/integer;C090_Neaktivny;
42;AP;;MJ pre JC -  UoM for UP;text(5);C140_MJPreJC;merná jednotka pre jednotkovú cenu - unit of measurement for unit price
43;AQ;;MJ pre JC koeficient - UoM for UP coefficient;number/double;C141_MJPreJCKoef;
44;AR;;Koeficien recyklačného fondu;number/double;C142_KoefRecFond;coefficient Recycling Fund
45;AS;;Kod evidencie - code of evidence;text(5);C025_KodEvidencia;
47;AT;;Kod ciselneho radu - sequence code;text(5);C021_KodCiselnaRada;
48;AU;;S ucet nakladovy - S account - costs;text(3);C231_SyntetickyUcetNakl;synthetic account - costs
49;AV;;A ucet nakladovy - A account - costs;text(6);C232_AnalytickyUcetNakl;analytical account - costs
50;AW;;Objem MJ - volume of UoM;number/double;C143_ObjemMJ;UoM - unit of measurement
51;AX;;Priznak ci sa ma karta posielat do E-shopu;number/integer;C155_DoEshopu;flag whether the card is transferred to shop
;;;;;;
1;A;;R03;;;
2;B;;T11;;;kod T11 oznacuje sekciu s predajnou cenou - code T11 indicates section 
3;C;;ID;number/long integer;C000_ID;pri exporte sa nevyplňuje - not entered in export
4;D;;Poradove cislo -  serial number;number/long integer;C020_PoradoveCislo;
5;E;;Kod - code;text(5);C021_Kod;
6;F;;Nazov - title;text(50);C100_Nazov;
7;G;;Marza - margin;number/single;C101_Marza;
8;H;;Zaokruhlenie - rounding;number/integer;C102_Zaokruhlenie;
9;I;;Typ vypoctu - type of calculation;number/integer;C103_TypVypoctu;Zadáva sa ako 0 pri výpočte priemerná sklad.cena + marža, ako 1 pri výpočte fifo sklad.cena + marža, ako 2 pri fixnej cene, ako 5 pri fixnej cene v cudzej mene
10;J;;Predajna cena - selling price;number/double;C104_PredajnaCena;
12;K;;Mena - currency;text(5);C200_Mena;
12;L;;ID premenna marza - ID variable margin;number/long integer;C105_IDPremennaMarza;
13;M;;ID premenna kurz - ID variable exchange rate;number/long integer;C106_IDPremennaKurz;
14;N;;Kurz - exchange rate;number/single;C107_Kurz;
15;O;;Predajna cena s DPH - selling price with VAT;currency;C108_PredajnaCenaSDPH;
16;P;;ceny boli zadavane s DPH;number/integer;C143_CenyZadaneSDPH;Urcuje ci ceny boli zadavane s DPH (-1) alebo bez DPH (0) - indicates if prices was entered with VAT (-1) or without VAT (0)
;;;;;;
1;A;;R03;;;
2;B;;T12;;;kod T12 oznacuje sekciu s informaciou o dodavatelovi - code T12 indicates section with informations about supplier
3;C;;Partner ICO - partner REG;text(12);C051_PartnerICO;
4;D;;Partner IČO poradové číslo -  REG order number;number/integer;C052_PartnerICOPoradoveCislo;
5;E;;Poradove cislo - order number;number/long integer;C020_PoradoveCislo;
6;F;;Bezna cena - common price;currency;C100_BeznaCena;
7;G;;Dodacia lehota - Delivery time;number/integer;C101_DodaciaLehota;
8;H;;Mena - Currency;text(5);C200_Mena;
9;I;;Poznamka - note;memo;C097_Poznamka;
;;;;;;
1;A;;R03;;;
2;B;;T13;;;kod T13 oznacuje sekciu s odvodenymi mernymi jednotkami - code T13 indicates section with derived units of measurement
3;C;;ID;number/long integer;C000_ID;pri exporte sa nevyplňuje - not entered in export
4;D;;Nazov - title;text(5);C100_Nazov;
5;E;;Pocet - quantity;number/double;C101_Pocet;
6;F;;Predvolena - default;number/integer;C012_Predvolena;
