Kazdy riadok zacina jednou z hodnot R00, R0, R02 alebo R03, ktora definuje vetu v subore - Each row start one of these values R00, R01, R02 or R03, which defines record in the file.;;;;;;
Prvy riadok v subore musi zacinat R00 - First row in file have to start R00;;;;;;
Kazda veta je ulozena v jednom riadku a je ukoncena znakmi CRLF -  Each record is saved in one row and it is ended signs CRLF;;;;;;
Jednoduche evidencie (napr. partneri) nemusia obsahovat vety typu R02, obsahuju iba vety typu R00, R01 - Simple files (for example - partners) they don't have to include records such as R02. They include only records such as R00, R01.;;;;;;
V jednom subore moze byt aj viac typov udajov, dalsi typ sa zacina znovu riadkom R00 XX - In one file can be more then one type of data. Next type is starting again with record R00 XX.;;;;;;
Vsetky hodnoty su oddelene TAB (tabelator, kod ASCII = 9) - All values are separated with TAB (tabulator, code ASCII = 9);;;;;;
Položky so žltým podfarbením sú povinné - Items with a yellow shading are mandatory;;;;;;
Položky s oranžovým podfarbením sú povinné len za určitých okolností - Items with a orange shading are mandatory under certain conditions;;;;;;
Polozky od znacky >> su nepovinne - Items from the marks  >> are optional.;;;;;;
Polozky (vety typu R02) jedneho dokladu pokracuju az po dalsiu vetu R01 alebo R00 alebo do konca suboru - Items (records sort of R02) from one receipt continues right to next record R01 or R00 or to end of file.;;;;;;
Kodovanie slovenskej diakritiky - Windows ANSI - Coding Slovak diacritic - Windows ANSI;;;;;;
"dátumy sú v tvare DD.MM.RRRR, D.M.R, prípadne oddelovače / (lomítko) a , (čiarka) - Dates are in form DD.MM.YYYY, D.M.Y, possible dividers are ( "" / "" and "" , "" - comma)";;;;;;
Po ukonceni importu je vytvoreny LOG subor - subor s rovnakym nazvom, ale s priponou LOG - After the import ends there is created LOG file - file with same name but with type of document LOG;;;;;;
Sadzba DPH: 0 (nulova), N (nizsia), V (vyssia) - VAT rate: 0 (null), N(lower), V(higher);;;;;;
Ak je pri nazve pola T/F: T = True (ano), F = False (nie) - If there is T/F by the name of the file, it means that it is boolean value.;;;;;;
;;;;;;
I/E = Import a export, E = iba export - I/E = means import and export, E = means only export;;;;;;
Export aj import Omegy pouzivaju rovnaky format suboru. Ukazku importneho suboru je mozne ziskat vyexportovanim rovnakych dat z Omegy. - Export and import of Omega are using the same format of file.  Illustration of import file you can obtain, when you export the same data from ;;;;;;
Importny subor je mozne vytvorit pomocou programu Excel, v ktorom treba dokument ulozit vo formate Text oddeleny tabelatormi. - Import file you can make by Excel, in which you have to save the document in format TEXT (separated with tabulators) ;;;;;;
;;;;;;
;;;;;;
R00;typ udajov;T00 (doklady EUD), T01 (fakturacia), T02 (pohyby na sklade), T03 (skladove karty), T04 (partneri), ...;;;;
R00;type of data;T00 (receipts EUD), T01 (invoicing), T02 (receipts of inventory), T03 (records of inventory), T04 (partners), ...;;;;
R01;hlavicka dokladu;;;;;
R01;head of receipt;;;;;
R02;polozka dokladu;;;;;
R02;item of receipt;;;;;
R03;polozka dokladu;;T11 (Predajne ceny), T12 (Dodavatelia);;;
R03;item of receipt;;T11 (Selling prices), T12 (Suppliers);;;
;;;;;;
CM = cudzia mena - foreign currency;; ;;;;
TM = tuzemska mena - domestic currency;;;;;;
;;;;;;
na dalsich kartach su informacie o importe jednotlivych typov udajov - on then next  sheets are information about import another evidence;;;;;;
;;;;;;
;;;;;;
;;;;;;
Typy dokladov:;;;;;;
T00 (EUD);;;;;;
T01 (Fakturácia);;;;;;
T02 (Pohyby na sklade);;;;;;
T03 (Skladove karty);;;;;;
T04 (Partneri);;;;;;
T06 (Došlé objednávky);;;;;;
T07 (Obraty z učtovníctva);;;;;;
T08 (Úhrady);;;;;;
T09 (Kurzový lístok);;;;;;
T10 (Cenník služieb);;;;;;
T11 (Predajné ceny);;;;;;
T12 (Dodávatelia);;;;;;
T13 (Odvodené merné jednotky);;;;;;
;;;;;;
T15 (Krátkodobý majetok);;;;;;
T16 (SZČP krátkodobého majetku);;;;;;
T17 (Dlhodobý majetok);;;;;;
T18 (SZČP dlhodobého majetku);;;;;;
T19 (Pohyby dlhodobého majetku);; ;;;;
T20 (SZČP);;;;;;
T21 (Poštové adresy partnera);;;;;;
T22 (Kontaktné osoby partnera);;;;;;
T23 (Účtovné odpisy dlhodobého majetku);;;;;;