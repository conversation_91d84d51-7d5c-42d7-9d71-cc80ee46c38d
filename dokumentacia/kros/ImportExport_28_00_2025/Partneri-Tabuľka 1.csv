Partneri: - Partners;;;;;;
Platí pre Import a Export - Applicable for Import and Export;;;;;;
Č.sĺpca;Označenie;;Hodnota;Max počet znakov v reťazci;Stĺpec v databáze;Popis
Column #;Indication;;Value;Maximum characters in string;Column in database;Description
1;A;;R00;;;
2;B;;T04;;;T04 (Partneri) - T04 - Partners
;;;;;;
1;A;;R01;;(T020_Partner);
2;B;;meno firmy - name of company;text(75);C044_PartnerFirmaMeno;
3;C;;ICO - REG;text(12);C051_PartnerICO;
4;D;>>;ulica - street;text(40);C047_PartnerUlica;
5;E;;PSC - Postal code;text(6);C048_PartnerPSC;
6;F;;mesto - city;text(40);C049_PartnerMesto;
7;G;;DIC/DU - TAX partner;text(5)/text(12);C054_PartnerDICX/C053_PartnerDIC;"od vstupu do EÚ sa už nepoužíva
not used after joining EU"
8;H;;stat - country;text(30);C050_PartnerStat;
9;I;;kod - code;text(5);C021_Kod;
10;J;;stredisko - centre;text(50);C045_PartnerFirmaStredisko;
11;K;;prevadzka - plent;text(50);C046_PartnerFirmaPrevadzka;
12;L;;FO;;;nepoužíva sa - not used
13;M;;platitel DPH - payer of VAT;;C102_PlatitelDPH;zadáva sa T ako platca DPH, F ako neplatca DPH - is entered T as payer, F as non-payer
14;N;;e-mail;memo;C107_EMail;
15;O;;http;memo;C108_Http;
16;P;;bankovy ucet - bank account;number/integer;C100_CisloUctu(T023_Partner_BankoveUcty);
17;Q;;kod banky - code of bank;text(5);C101_KodBanky(T023_Partner_BankoveUcty);
18;R;;nazov uctu - name of account;text(30);C102_NazovUctu(T023_Partner_BankoveUcty);
19;S;;tel1;text(10)/text(20);"C104_TelSmerovka; C105_Cislo(T021_Partner_KontaktTelefonFax)";!!!nefunguje pre import
20;T;;tel2;text(10)/text(20);"C104_TelSmerovka; C105_Cislo(T021_Partner_KontaktTelefonFax)";závisí od počtu zadaných telefónov - depends on quantity of phone numbers  !!!nefunguje pre import
21;U;;tel3;text(10)/text(20);"C104_TelSmerovka; C105_Cislo(T021_Partner_KontaktTelefonFax)";závisí od počtu zadaných telefónov - depends on quantity of phone numbers !!!nefunguje pre import
22;V;;fax;text(10)/text(20);"C104_TelSmerovka; C105_Cislo(T021_Partner_KontaktTelefonFax)";závisí od počtu zadaných telefónov - depends on quantity of phone numbers !!!nefunguje pre import
23;W;;poznamka - note;memo;C098_Poznamka;
24;X;;interné číslo - internal number;text(20);C039_PartnerInterneCislo;
25;Y;;Kod IC DPH - code of VAT;text(20);C024_KodStatuDPH;
26;Z;;IČ DPH -  VAT;text(50);C025_PartnerIDpreDPH;
27;AA;;DIČ  - TAX partner;text(50);C026_PartnerIDpreDPH2;tu sa ukladá DIČ od vstupu do EÚ
28;AB;;IČO poradové číslo -  REG order number;number/integer;C052_PartnerICOPoradoveCislo;
29;AC;;Hlásiť neplatiča pre neuhradené doklady po lehote splatnosti;number/integer;C140_SumaNehlasNeplatica;hodnota určuje sumu, od koľkej hlásiť, -1 znamená nehlásiť
;;;Notify defaulters for unpaid overdue documents;;;value determines the amount by which the report, -1 means not to report
30;AD;;Hlásiť neplatiča pre všetky neuhradené doklady;number/integer;C147_SumaNeplaticBezOhladu;hodnota určuje sumu, od koľkej hlásiť, -1 znamená nehlásiť
;;;Notify defaulters for all outstanding documents;;;value determines the amount by which the report, -1 means not to report
31;AE;;Zakázať vystaviť doklad pre neplatiča;number/integer;C148_ZakazatAkNeplatic;0 - povoliť, -1 zakázať
;;;Disable document issuing to defaulters;;;0 - enable, disable -1
;;;;;;
32;AF;;Občan - Citizen;;C112_Obcan;zadáva sa T ako občan, alebo F firma - is entered T as citizen or F as firm
33;AG;;IBAN  - IBAN;text(50);C159_IBAN(T023_Partner_BankoveUcty);
34;AH;;SWIFT -  SWIFT;text(50);C158_SWIFT(T023_Partner_BankoveUcty);
35;AI;;Sadzba DPH -VAT rate;number/integer;C127_SadzbaDPH(T020_Partner);
;;;;;;
;;;R02;;;
;;;;;;
1;A;;R03;;(T024_Partner_Adresy);Poštové adresy
2;B;;T21;;;
3;C;;meno firmy - name of company;text(50);C044_PartnerFirmaMeno;
4;D;;stredisko - centre;text(50);C045_PartnerFirmaStredisko;
5;E;;prevadzka - plent;text(50);C046_PartnerFirmaPrevadzka;
6;F;;ulica - street;text(40);C047_PartnerUlica;
7;G;;PSC - Postal code;text(6);C048_PartnerPSC;
8;H;;mesto - city;text(40);C049_PartnerMesto;
9;I;;stat - country;text(30);C050_PartnerStat;
;;;;;;
1;A;;R03;;;Kontakty
2;B;;T22;;;
3;C;;meno osoby - name of person;text(30);T094_Partner_Osoby.C100_Meno;
4;D;;priezvisko osoby - surname of person;text(30);T094_Partner_Osoby.C101_Priezvisko;
5;E;;telefon predvolba krajiny - phone country code;text(10);T021_Partner_KontaktTelefonFax.C103_TelKrajina;
6;F;;telefon cislo - phone number;text(10);T021_Partner_KontaktTelefonFax.C105_Cislo;
7;G;;mobil predvolba krajiny - mobil country code;text(10);T021_Partner_KontaktTelefonFax.C103_TelKrajina;T021_Partner_KontaktTelefonFax.C006_JeMobil nastavi na -1
8;H;;mobil cislo - mobile phone number;text(10);T021_Partner_KontaktTelefonFax.C105_Cislo;
9;I;;email - email;memo;T021_Partner_KontaktTelefonFax.C100_Popis;
;;;;;;
Poznámky - notes:;;;;;;
Momentálne je možné importovať a exportovať len partnera bez špecifického zadania jednotlivých kontaktných osôb.;;;;;;
There can only import and export partner, with no specific assignment of contact persons;;;;;;
