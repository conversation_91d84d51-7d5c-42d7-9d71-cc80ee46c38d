// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Configuration table
model Config {
  id                    Int     @id @default(autoincrement())
  uispApiUrl           String?
  uispApiKey           String?
  omegaImportPath      String  @default("./omega-imports")
  autoSyncEnabled      Boolean @default(false)
  syncIntervalMinutes  Int     @default(60)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  @@map("config")
}

// Client mapping between UISP and Omega
model ClientMapping {
  id                Int      @id @default(autoincrement())
  uispClientId      Int      @unique
  omegaPartnerCode  String?
  omegaPartnerName  String?
  omegaPartnerIco   String?
  omegaPartnerDic   String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  invoices Invoice[]

  @@map("client_mapping")
}

// Invoices from UISP CRM
model Invoice {
  id                Int      @id @default(autoincrement())
  uispInvoiceId     Int      @unique
  invoiceNumber     String
  clientId          Int
  totalAmount       Float
  currency          String   @default("EUR")
  createdDate       DateTime
  dueDate           DateTime
  taxableSupplyDate DateTime?
  paidDate          DateTime?
  status            String   // draft, sent, paid, overdue, void
  omegaImported     Boolean  @default(false)
  omegaImportDate   DateTime?
  rawData           String   // JSON string of original UISP data
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  clientMapping ClientMapping @relation(fields: [clientId], references: [uispClientId])
  items         InvoiceItem[]
  payments      Payment[]

  @@map("invoices")
}

// Invoice items
model InvoiceItem {
  id          Int     @id @default(autoincrement())
  invoiceId   Int
  label       String
  quantity    Float
  unit        String?
  unitPrice   Float
  vatRate     String  // N, V, 0, X, Y
  totalAmount Float
  createdAt   DateTime @default(now())

  // Relations
  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

// Bank statements
model BankStatement {
  id             Int      @id @default(autoincrement())
  accountIban    String
  statementDate  DateTime
  openingBalance Float
  closingBalance Float
  fileName       String
  fileType       String   // XML, SWIFT, XLS
  processed      Boolean  @default(false)
  omegaImported  Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  transactions BankTransaction[]

  @@map("bank_statements")
}

// Bank transactions
model BankTransaction {
  id                   Int      @id @default(autoincrement())
  statementId          Int
  transactionDate      DateTime
  amount               Float
  currency             String   @default("EUR")
  debitCredit          String   // DBIT, CRDT
  variableSymbol       String?
  specificSymbol       String?
  constantSymbol       String?
  counterpartyName     String?
  counterpartyAccount  String?
  counterpartyBankCode String?
  reference            String?
  description          String?
  matchedInvoiceId     Int?
  omegaImported        Boolean  @default(false)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Relations
  statement       BankStatement @relation(fields: [statementId], references: [id], onDelete: Cascade)
  matchedInvoice  Invoice?      @relation(fields: [matchedInvoiceId], references: [id])
  payments        Payment[]

  @@map("bank_transactions")
}

// Payments (linking invoices with bank transactions)
model Payment {
  id                Int      @id @default(autoincrement())
  invoiceId         Int
  bankTransactionId Int?
  amount            Float
  paymentDate       DateTime
  paymentType       String   // bank_transfer, cash, card
  omegaImported     Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  invoice         Invoice          @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bankTransaction BankTransaction? @relation(fields: [bankTransactionId], references: [id])

  @@map("payments")
}

// Operation logs
model OperationLog {
  id            Int      @id @default(autoincrement())
  operationType String   // sync_invoices, import_bank, match_payments, export_omega
  status        String   // success, error, warning, info
  message       String
  details       String?  // JSON string with additional details
  duration      Int?     // Duration in milliseconds
  recordCount   Int?     // Number of records processed
  createdAt     DateTime @default(now())

  @@map("operation_logs")
}

// User sessions (for web interface)
model Session {
  id        String   @id @default(cuid())
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("sessions")
}

// File uploads tracking
model FileUpload {
  id           Int      @id @default(autoincrement())
  originalName String
  fileName     String
  filePath     String
  fileSize     Int
  mimeType     String
  uploadType   String   // bank_statement, manual_import
  processed    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("file_uploads")
}
