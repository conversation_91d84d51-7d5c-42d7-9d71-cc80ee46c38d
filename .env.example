# Environment
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL="file:./dev.db"

# UISP CRM API Configuration
UISP_API_URL=https://your-uisp-instance.com/api/v1.0
UISP_API_KEY=your-uisp-api-key

# Omega Configuration
OMEGA_IMPORT_PATH=./omega-imports

# Security
JWT_SECRET=your-super-secret-jwt-key-change-in-production
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Scheduler
SYNC_INTERVAL_MINUTES=30
AUTO_SYNC_ENABLED=false

# Email Notifications (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
NOTIFICATION_EMAIL=<EMAIL>
