<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>Bank API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            console.log('Testing API...');
            try {
                const response = await fetch('http://localhost:3001/api/bank/statements');
                const data = await response.json();
                console.log('API Response:', data);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('API Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        // Auto-test on load
        window.onload = function() {
            console.log('Page loaded, testing API...');
            testAPI();
        };
    </script>
</body>
</html>
