# UISP-Omega Bridge

A scalable bridge application for synchronizing invoices, payments, and bank statements between UISP CRM and KROS Omega accounting software.

## Features

- 🔄 **Invoice Synchronization**: Automatic import of invoices from UISP CRM to Omega
- 🏦 **Bank Statement Processing**: Support for XML, SWIFT MT940, and XLS formats from Tatra Banka
- 🔗 **Payment Matching**: Intelligent automatic matching of payments with invoices
- 📊 **CSV Export**: Generate properly formatted CSV files for Omega import
- 🕐 **Scheduled Operations**: Automated synchronization with configurable intervals
- 🔍 **Monitoring & Logging**: Comprehensive logging and operation tracking
- 🌐 **Web Interface**: React-based dashboard for configuration and monitoring

## Architecture

The application is built with scalability and upgradeability in mind:

- **Backend**: Node.js + TypeScript + Express.js
- **Database**: SQLite (development) / PostgreSQL (production) with Prisma ORM
- **Frontend**: React + TypeScript + Material-UI
- **File Processing**: Support for multiple bank file formats
- **Scheduling**: Cron-based automated operations
- **Logging**: <PERSON> with structured logging
- **Testing**: Jest with comprehensive test coverage

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm 8+

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd uisp-omega-bridge
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Initialize the database:
```bash
npm run db:generate
npm run db:migrate
npm run db:seed
```

5. Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# UISP CRM API
UISP_API_URL=https://your-uisp-instance.com/api/v1.0
UISP_API_KEY=your-uisp-api-key

# Omega
OMEGA_IMPORT_PATH=./omega-imports

# Scheduler
SYNC_INTERVAL_MINUTES=30
AUTO_SYNC_ENABLED=false
```

### Database Setup

The application uses Prisma ORM for database management:

```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed initial data
npm run db:seed

# Open Prisma Studio (database GUI)
npm run db:studio
```

## Usage

### Manual Operations

```bash
# Sync invoices from UISP CRM
curl -X POST http://localhost:3000/api/sync/invoices

# Import bank statement
curl -X POST http://localhost:3000/api/bank/import \
  -F "file=@statement.xml" \
  -F "type=XML"

# Match payments
curl -X POST http://localhost:3000/api/payments/match

# Export to Omega
curl -X POST http://localhost:3000/api/omega/export
```

### Automated Operations

Enable automatic synchronization by setting `AUTO_SYNC_ENABLED=true` in your `.env` file. The application will:

- Sync invoices every 30 minutes (configurable)
- Check for new bank files daily
- Automatically match payments
- Generate Omega export files

## Development

### Project Structure

```
src/
├── config/          # Configuration management
├── controllers/     # HTTP request handlers
├── services/        # Business logic
├── models/          # Database models (Prisma)
├── middleware/      # Express middleware
├── utils/           # Utility functions
├── types/           # TypeScript type definitions
├── database/        # Database migrations and seeds
├── parsers/         # Bank file parsers
├── generators/      # CSV generators for Omega
└── test/           # Test files
```

### Available Scripts

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server

# Database
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Run database migrations
npm run db:deploy    # Deploy migrations (production)
npm run db:seed      # Seed database with initial data
npm run db:studio    # Open Prisma Studio

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking

# Testing
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

### Adding New Features

1. **Create types** in `src/types/`
2. **Add database models** in `prisma/schema.prisma`
3. **Implement services** in `src/services/`
4. **Create controllers** in `src/controllers/`
5. **Add routes** in `src/routes/`
6. **Write tests** in `src/test/`

## Bank File Formats

### Supported Formats

1. **XML (SEPA)**: Standard European payment format
2. **SWIFT MT940**: International bank statement format
3. **XLS**: Tatra Banka Excel export format

### File Processing

Bank files are automatically parsed and transactions are extracted with:
- Transaction dates and amounts
- Variable, specific, and constant symbols
- Counterparty information
- Payment references

## Payment Matching

The application uses intelligent algorithms to match bank transactions with invoices:

1. **Variable Symbol Matching**: Direct match using invoice numbers
2. **Amount and Date Matching**: Match by amount within ±3 days of due date
3. **Partner Name Matching**: Fuzzy matching of counterparty names
4. **Manual Matching**: Web interface for manual review and matching

## Omega Integration

### CSV Export Format

The application generates CSV files compatible with KROS Omega:

- **Invoices**: T01 format with proper VAT handling
- **Payments**: T08 format for payment records
- **Encoding**: Windows ANSI (CP1250) with Slovak characters
- **Delimiter**: TAB-separated values

### Import Process

1. Generate CSV files via API or web interface
2. Copy files to Omega import directory
3. Import in Omega using standard import procedures

## Monitoring & Logging

### Logging Levels

- **Error**: Critical errors requiring attention
- **Warn**: Warning conditions
- **Info**: General information
- **Debug**: Detailed debugging information

### Log Files

- `logs/app.log`: All application logs
- `logs/error.log`: Error logs only
- `logs/debug.log`: Debug logs (development only)

### Monitoring Endpoints

```bash
# Health check
GET /api/health

# System status
GET /api/status

# Operation logs
GET /api/logs
```

## Production Deployment

### Environment Setup

1. Set `NODE_ENV=production`
2. Configure PostgreSQL database
3. Set up proper logging directory
4. Configure email notifications
5. Set up reverse proxy (nginx)

### Security Considerations

- Use strong JWT secrets
- Enable HTTPS
- Configure rate limiting
- Set up firewall rules
- Regular security updates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs for error details
