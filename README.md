# UISP-Omega Bridge

A scalable bridge application for synchronizing invoices, payments, and bank statements between UISP CRM and KROS Omega accounting software.

## Features

- 🔄 **Invoice Synchronization**: Automatic import of invoices from UISP CRM to Omega
- 🏦 **Bank Statement Processing**: Support for XML, SWIFT MT940, and XLS formats from Tatra Banka
- 🔗 **Payment Matching**: Intelligent automatic matching of payments with invoices
- 📊 **CSV Export**: Generate properly formatted CSV files for Omega import
- 🕐 **Scheduled Operations**: Automated synchronization with configurable intervals
- 🔍 **Monitoring & Logging**: Comprehensive logging and operation tracking
- 🌐 **Web Interface**: Modern React dashboard with real-time monitoring
- 📱 **Responsive Design**: Mobile-friendly interface with Material-UI components
- 📈 **Data Visualization**: Interactive charts and statistics
- 🎯 **Drag & Drop**: Intuitive file upload interface
- ⚡ **Real-time Updates**: Live data refresh and notifications

## Architecture

The application is built with scalability and upgradeability in mind:

### Backend
- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with comprehensive middleware
- **Database**: SQLite (development) / PostgreSQL (production) with Prisma ORM
- **File Processing**: Custom parsers for XML, SWIFT, Excel formats
- **API Integration**: UISP CRM REST API client
- **Export**: CSV generation for KROS Omega
- **Scheduling**: Cron-based automated operations
- **Logging**: Winston with structured logging
- **Testing**: Jest with comprehensive test coverage

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Library**: Material-UI v5 with custom theming
- **State Management**: TanStack Query for server state, Zustand for client state
- **Forms**: React Hook Form with validation
- **Charts**: Recharts for data visualization
- **Testing**: Vitest and React Testing Library
- **Routing**: React Router v6

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm 8+

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd uisp-omega-bridge
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Initialize the database:
```bash
npm run db:generate
npm run db:migrate
npm run db:seed
```

5. Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# UISP CRM API
UISP_API_URL=https://your-uisp-instance.com/api/v1.0
UISP_API_KEY=your-uisp-api-key

# Omega
OMEGA_IMPORT_PATH=./omega-imports

# Scheduler
SYNC_INTERVAL_MINUTES=30
AUTO_SYNC_ENABLED=false
```

### Database Setup

The application uses Prisma ORM for database management:

```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed initial data
npm run db:seed

# Open Prisma Studio (database GUI)
npm run db:studio
```

## Usage

### Web Interface

The application includes a modern React-based web interface accessible at `http://localhost:3001`:

1. **Start the backend:**
   ```bash
   npm run dev
   ```

2. **Start the frontend (in a new terminal):**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Access the dashboard:**
   Open `http://localhost:3001` in your browser

### Manual Operations (API)

```bash
# Sync invoices from UISP CRM
curl -X POST http://localhost:3000/api/uisp/sync/invoices

# Import bank statement
curl -X POST http://localhost:3000/api/bank/upload \
  -F "file=@statement.xml"

# Match payments
curl -X POST http://localhost:3000/api/payment/match

# Export to Omega
curl -X POST http://localhost:3000/api/omega/export/batch
```

### Automated Operations

Enable automatic synchronization by setting `AUTO_SYNC_ENABLED=true` in your `.env` file. The application will:

- Sync invoices every 30 minutes (configurable)
- Check for new bank files daily
- Automatically match payments
- Generate Omega export files

## Development

### Project Structure

```
uisp-omega-bridge/
├── src/                    # Backend source code
│   ├── config/            # Configuration files
│   ├── database/          # Database setup and migrations
│   ├── middleware/        # Express middleware
│   ├── routes/            # API routes
│   ├── services/          # Business logic services
│   ├── parsers/           # Bank file parsers
│   ├── generators/        # Omega CSV generators
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   └── app.ts             # Express app setup
├── frontend/              # React frontend application
│   ├── src/               # Frontend source code
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── lib/           # API client and utilities
│   │   ├── types/         # TypeScript type definitions
│   │   └── test/          # Frontend tests
│   ├── public/            # Static assets
│   ├── package.json       # Frontend dependencies
│   └── vite.config.ts     # Vite configuration
├── prisma/                # Database schema and migrations
├── test/                  # Backend test files
├── docs/                  # Documentation
├── scripts/               # Utility scripts
├── .env.example           # Environment variables template
├── package.json           # Backend dependencies and scripts
├── tsconfig.json          # TypeScript configuration
└── README.md              # This file
```

### Available Scripts

#### Backend Scripts
```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server

# Database
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Run database migrations
npm run db:deploy    # Deploy migrations (production)
npm run db:seed      # Seed database with initial data
npm run db:studio    # Open Prisma Studio

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking

# Testing
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

#### Frontend Scripts
```bash
cd frontend

# Development
npm run dev          # Start Vite dev server (http://localhost:3001)
npm run build        # Build for production
npm run preview      # Preview production build

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking

# Testing
npm run test         # Run Vitest tests
npm run test:ui      # Run tests with UI
npm run test:coverage # Run tests with coverage
```

### Adding New Features

#### Backend Development
1. **Create types** in `src/types/`
2. **Add database models** in `prisma/schema.prisma`
3. **Implement services** in `src/services/`
4. **Add routes** in `src/routes/`
5. **Write tests** in `src/test/`

#### Frontend Development
1. **Define types** in `frontend/src/types/`
2. **Create components** in `frontend/src/components/`
3. **Add pages** in `frontend/src/pages/`
4. **Update API client** in `frontend/src/lib/api.ts`
5. **Write tests** in `frontend/src/test/`

#### Full-Stack Feature Development
1. **Backend**: API endpoints and business logic
2. **Frontend**: UI components and state management
3. **Integration**: API client updates and type sharing
4. **Testing**: Both unit and integration tests
5. **Documentation**: Update README and API docs

## Bank File Formats

### Supported Formats

1. **XML (SEPA)**: Standard European payment format
2. **SWIFT MT940**: International bank statement format
3. **XLS**: Tatra Banka Excel export format

### File Processing

Bank files are automatically parsed and transactions are extracted with:
- Transaction dates and amounts
- Variable, specific, and constant symbols
- Counterparty information
- Payment references

## Payment Matching

The application uses intelligent algorithms to match bank transactions with invoices:

1. **Variable Symbol Matching**: Direct match using invoice numbers
2. **Amount and Date Matching**: Match by amount within ±3 days of due date
3. **Partner Name Matching**: Fuzzy matching of counterparty names
4. **Manual Matching**: Web interface for manual review and matching

## Omega Integration

### CSV Export Format

The application generates CSV files compatible with KROS Omega:

- **Invoices**: T01 format with proper VAT handling
- **Payments**: T08 format for payment records
- **Encoding**: Windows ANSI (CP1250) with Slovak characters
- **Delimiter**: TAB-separated values

### Import Process

1. Generate CSV files via API or web interface
2. Copy files to Omega import directory
3. Import in Omega using standard import procedures

## Monitoring & Logging

### Logging Levels

- **Error**: Critical errors requiring attention
- **Warn**: Warning conditions
- **Info**: General information
- **Debug**: Detailed debugging information

### Log Files

- `logs/app.log`: All application logs
- `logs/error.log`: Error logs only
- `logs/debug.log`: Debug logs (development only)

### Monitoring Endpoints

```bash
# Health check
GET /api/health

# System status
GET /api/status

# Operation logs
GET /api/logs
```

## Production Deployment

### Environment Setup

1. Set `NODE_ENV=production`
2. Configure PostgreSQL database
3. Set up proper logging directory
4. Configure email notifications
5. Set up reverse proxy (nginx)

### Security Considerations

- Use strong JWT secrets
- Enable HTTPS
- Configure rate limiting
- Set up firewall rules
- Regular security updates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs for error details
