import fs from 'fs';
import path from 'path';
import winston from 'winston';

import { config } from '@/config';

// Ensure logs directory exists
const logsDir = path.dirname(config.logging.file);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    // Add stack trace for errors
    if (stack) {
      log += `\n${stack}`;
    }
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create transports
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    level: config.logging.level,
    format: consoleFormat,
    silent: config.isTest, // Disable console logging in tests
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: config.logging.file,
    level: config.logging.level,
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  }),
  
  // Separate file for errors
  new winston.transports.File({
    filename: path.join(logsDir, 'error.log'),
    level: 'error',
    format: fileFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  }),
];

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true })
  ),
  transports,
  exitOnError: false,
});

// Add request ID to logs in development
if (config.isDevelopment) {
  logger.add(new winston.transports.File({
    filename: path.join(logsDir, 'debug.log'),
    level: 'debug',
    format: fileFormat,
    maxsize: 5 * 1024 * 1024, // 5MB
    maxFiles: 3,
  }));
}

// Helper functions for structured logging
export const logOperation = (
  operation: string,
  status: 'start' | 'success' | 'error',
  details?: Record<string, unknown>,
  error?: Error
): void => {
  const logData = {
    operation,
    status,
    ...details,
  };

  switch (status) {
    case 'start':
      logger.info(`Operation started: ${operation}`, logData);
      break;
    case 'success':
      logger.info(`Operation completed: ${operation}`, logData);
      break;
    case 'error':
      logger.error(`Operation failed: ${operation}`, { ...logData, error: error?.message, stack: error?.stack });
      break;
  }
};

export const logApiCall = (
  method: string,
  url: string,
  status: number,
  duration: number,
  details?: Record<string, unknown>
): void => {
  const logData = {
    method,
    url,
    status,
    duration,
    ...details,
  };

  if (status >= 400) {
    logger.warn('API call failed', logData);
  } else {
    logger.info('API call completed', logData);
  }
};

export const logDatabaseOperation = (
  operation: string,
  table: string,
  duration: number,
  recordCount?: number,
  error?: Error
): void => {
  const logData = {
    operation,
    table,
    duration,
    recordCount,
  };

  if (error) {
    logger.error('Database operation failed', { ...logData, error: error.message });
  } else {
    logger.debug('Database operation completed', logData);
  }
};

export const logFileOperation = (
  operation: string,
  filePath: string,
  fileSize?: number,
  error?: Error
): void => {
  const logData = {
    operation,
    filePath,
    fileSize,
  };

  if (error) {
    logger.error('File operation failed', { ...logData, error: error.message });
  } else {
    logger.info('File operation completed', logData);
  }
};

export const logSchedulerJob = (
  jobName: string,
  status: 'start' | 'success' | 'error',
  duration?: number,
  details?: Record<string, unknown>,
  error?: Error
): void => {
  const logData = {
    jobName,
    status,
    duration,
    ...details,
  };

  switch (status) {
    case 'start':
      logger.info(`Scheduler job started: ${jobName}`, logData);
      break;
    case 'success':
      logger.info(`Scheduler job completed: ${jobName}`, logData);
      break;
    case 'error':
      logger.error(`Scheduler job failed: ${jobName}`, { ...logData, error: error?.message });
      break;
  }
};

// Stream for Morgan HTTP request logging
export const httpLogStream = {
  write: (message: string): void => {
    logger.info(message.trim());
  },
};

export default logger;
