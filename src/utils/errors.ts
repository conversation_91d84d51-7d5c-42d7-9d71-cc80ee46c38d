import type { AppError } from '@/types/common';

// Custom error classes for better error handling
export class BaseError extends Error implements AppError {
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends BaseError {
  public readonly field?: string;
  public readonly value?: unknown;

  constructor(message: string, field?: string, value?: unknown) {
    super(message, 400);
    this.field = field;
    this.value = value;
  }
}

export class NotFoundError extends BaseError {
  constructor(resource: string, identifier?: string | number) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    super(message, 404);
  }
}

export class ConflictError extends BaseError {
  constructor(message: string) {
    super(message, 409);
  }
}

export class UnauthorizedError extends BaseError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends BaseError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}

export class BadRequestError extends BaseError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class InternalServerError extends BaseError {
  constructor(message: string = 'Internal server error') {
    super(message, 500);
  }
}

export class ServiceUnavailableError extends BaseError {
  constructor(message: string = 'Service temporarily unavailable') {
    super(message, 503);
  }
}

export class TooManyRequestsError extends BaseError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

// API specific errors
export class UispApiError extends BaseError {
  public readonly apiResponse?: unknown;

  constructor(message: string, statusCode: number = 500, apiResponse?: unknown) {
    super(`UISP API Error: ${message}`, statusCode);
    this.apiResponse = apiResponse;
  }
}

export class BankParsingError extends BaseError {
  public readonly fileName?: string;
  public readonly lineNumber?: number;

  constructor(message: string, fileName?: string, lineNumber?: number) {
    super(`Bank file parsing error: ${message}`, 400);
    this.fileName = fileName;
    this.lineNumber = lineNumber;
  }
}

export class OmegaExportError extends BaseError {
  public readonly recordCount?: number;

  constructor(message: string, recordCount?: number) {
    super(`Omega export error: ${message}`, 500);
    this.recordCount = recordCount;
  }
}

export class PaymentMatchingError extends BaseError {
  public readonly transactionId?: number;
  public readonly invoiceId?: number;

  constructor(message: string, transactionId?: number, invoiceId?: number) {
    super(`Payment matching error: ${message}`, 400);
    this.transactionId = transactionId;
    this.invoiceId = invoiceId;
  }
}

export class DatabaseError extends BaseError {
  public readonly operation?: string;
  public readonly table?: string;

  constructor(message: string, operation?: string, table?: string) {
    super(`Database error: ${message}`, 500);
    this.operation = operation;
    this.table = table;
  }
}

export class FileUploadError extends BaseError {
  public readonly fileName?: string;
  public readonly fileSize?: number;

  constructor(message: string, fileName?: string, fileSize?: number) {
    super(`File upload error: ${message}`, 400);
    this.fileName = fileName;
    this.fileSize = fileSize;
  }
}

export class ConfigurationError extends BaseError {
  public readonly configKey?: string;

  constructor(message: string, configKey?: string) {
    super(`Configuration error: ${message}`, 500, false);
    this.configKey = configKey;
  }
}

// Error factory functions
export const createValidationError = (
  field: string,
  value: unknown,
  expectedType?: string
): ValidationError => {
  const message = expectedType
    ? `Invalid value for field '${field}'. Expected ${expectedType}, got ${typeof value}`
    : `Invalid value for field '${field}'`;
  return new ValidationError(message, field, value);
};

export const createNotFoundError = (
  resource: string,
  identifier?: string | number
): NotFoundError => {
  return new NotFoundError(resource, identifier);
};

export const createConflictError = (
  resource: string,
  field: string,
  value: unknown
): ConflictError => {
  return new ConflictError(
    `${resource} with ${field} '${value}' already exists`
  );
};

// Error type guards
export const isAppError = (error: unknown): error is AppError => {
  return error instanceof BaseError;
};

export const isValidationError = (error: unknown): error is ValidationError => {
  return error instanceof ValidationError;
};

export const isNotFoundError = (error: unknown): error is NotFoundError => {
  return error instanceof NotFoundError;
};

export const isUispApiError = (error: unknown): error is UispApiError => {
  return error instanceof UispApiError;
};

export const isBankParsingError = (error: unknown): error is BankParsingError => {
  return error instanceof BankParsingError;
};

// Error handling utilities
export const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
};

export const getErrorStatusCode = (error: unknown): number => {
  if (isAppError(error)) {
    return error.statusCode;
  }
  return 500;
};

export const isOperationalError = (error: unknown): boolean => {
  if (isAppError(error)) {
    return error.isOperational;
  }
  return false;
};
