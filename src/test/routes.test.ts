import request from 'supertest';
import express from 'express';

import { createApp } from '@/app';
import { testDb, createTestInvoice, createTestBankTransaction, createTestBankStatement, createTestClientMapping } from './setup';

describe('API Routes', () => {
  let app: express.Application;

  beforeAll(async () => {
    app = createApp();
  });

  describe('Health and Status', () => {
    it('GET /api/health should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('healthy');
      expect(response.body.data.database).toBe(true);
    });

    it('GET /api/status should return system status', async () => {
      const response = await request(app)
        .get('/api/status')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.system).toBeDefined();
      expect(response.body.data.database).toBeDefined();
      expect(response.body.data.recentActivity).toBeDefined();
    });

    it('GET /api/info should return API information', async () => {
      const response = await request(app)
        .get('/api/info')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('UISP-Omega Bridge API');
      expect(response.body.data.endpoints).toBeDefined();
    });
  });

  describe('Statistics', () => {
    beforeEach(async () => {
      // Create test data
      const clientMapping = await testDb.clientMapping.create({
        data: createTestClientMapping({ uispClientId: 1 }),
      });

      await testDb.invoice.create({
        data: createTestInvoice({ clientId: clientMapping.uispClientId }),
      });

      const bankStatement = await testDb.bankStatement.create({
        data: createTestBankStatement(),
      });

      await testDb.bankTransaction.create({
        data: createTestBankTransaction({ statementId: bankStatement.id }),
      });
    });

    it('GET /api/stats should return global statistics', async () => {
      const response = await request(app)
        .get('/api/stats')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.sync).toBeDefined();
      expect(response.body.data.unmatched).toBeDefined();
      expect(response.body.data.recent).toBeDefined();
      expect(typeof response.body.data.sync.totalInvoices).toBe('number');
      expect(typeof response.body.data.sync.totalTransactions).toBe('number');
    });
  });

  describe('Logs', () => {
    it('GET /api/logs should return operation logs', async () => {
      const response = await request(app)
        .get('/api/logs')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('GET /api/logs should filter by operation type', async () => {
      const response = await request(app)
        .get('/api/logs?operationType=sync_invoices&limit=10')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Database Test', () => {
    it('GET /api/test-db should test database connection', async () => {
      const response = await request(app)
        .get('/api/test-db')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toContain('successful');
    });
  });
});

describe('UISP Routes', () => {
  let app: express.Application;

  beforeAll(async () => {
    app = createApp();
  });

  describe('Connection', () => {
    it('GET /api/uisp/test-connection should test UISP connection', async () => {
      const response = await request(app)
        .get('/api/uisp/test-connection')
        .set('Authorization', 'Bearer test-token');

      // May fail if UISP is not configured, but should not crash
      expect([200, 400, 500]).toContain(response.status);
      expect(response.body.success).toBeDefined();
    });

    it('GET /api/uisp/status should return UISP status', async () => {
      const response = await request(app)
        .get('/api/uisp/status')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.apiStatus).toBeDefined();
    });
  });

  describe('Sync', () => {
    it('GET /api/uisp/sync/stats should return sync statistics', async () => {
      const response = await request(app)
        .get('/api/uisp/sync/stats')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.totalInvoices).toBeDefined();
    });

    it('POST /api/uisp/sync/invoices should sync invoices (dry run)', async () => {
      const response = await request(app)
        .post('/api/uisp/sync/invoices')
        .set('Authorization', 'Bearer test-token')
        .send({ dryRun: true });

      // May fail if UISP is not configured
      expect([200, 400, 500]).toContain(response.status);
    });
  });
});

describe('Bank Routes', () => {
  let app: express.Application;

  beforeAll(async () => {
    app = createApp();
  });

  describe('File Types', () => {
    it('GET /api/bank/supported-types should return supported file types', async () => {
      const response = await request(app)
        .get('/api/bank/supported-types')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });
  });

  describe('Statements', () => {
    beforeEach(async () => {
      await testDb.bankStatement.create({
        data: createTestBankStatement(),
      });
    });

    it('GET /api/bank/statements should return bank statements', async () => {
      const response = await request(app)
        .get('/api/bank/statements')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('GET /api/bank/statements should filter by date range', async () => {
      const response = await request(app)
        .get('/api/bank/statements?fromDate=2024-01-01&toDate=2024-12-31')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Transactions', () => {
    beforeEach(async () => {
      const bankStatement = await testDb.bankStatement.create({
        data: createTestBankStatement(),
      });

      await testDb.bankTransaction.create({
        data: createTestBankTransaction({ statementId: bankStatement.id }),
      });
    });

    it('GET /api/bank/transactions should return bank transactions', async () => {
      const response = await request(app)
        .get('/api/bank/transactions')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('GET /api/bank/transactions should filter by debit/credit', async () => {
      const response = await request(app)
        .get('/api/bank/transactions?debitCredit=CRDT')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Statistics', () => {
    it('GET /api/bank/stats should return import statistics', async () => {
      const response = await request(app)
        .get('/api/bank/stats')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.totalStatements).toBeDefined();
    });
  });
});

describe('Payment Routes', () => {
  let app: express.Application;

  beforeAll(async () => {
    app = createApp();
  });

  describe('Matching', () => {
    it('GET /api/payment/stats should return matching statistics', async () => {
      const response = await request(app)
        .get('/api/payment/stats')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.totalTransactions).toBeDefined();
      expect(response.body.data.matchingRate).toBeDefined();
    });

    it('GET /api/payment/unmatched should return unmatched transactions', async () => {
      const response = await request(app)
        .get('/api/payment/unmatched')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('POST /api/payment/match should match payments (dry run)', async () => {
      const response = await request(app)
        .post('/api/payment/match')
        .set('Authorization', 'Bearer test-token')
        .send({ dryRun: true });

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Payments', () => {
    it('GET /api/payment should return payments', async () => {
      const response = await request(app)
        .get('/api/payment')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });
});

describe('Omega Routes', () => {
  let app: express.Application;

  beforeAll(async () => {
    app = createApp();
  });

  describe('Export', () => {
    it('GET /api/omega/stats should return export statistics', async () => {
      const response = await request(app)
        .get('/api/omega/stats')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.totalExports).toBeDefined();
    });

    it('GET /api/omega/files should return export files', async () => {
      const response = await request(app)
        .get('/api/omega/files')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('GET /api/omega/validate should validate export configuration', async () => {
      const response = await request(app)
        .get('/api/omega/validate')
        .set('Authorization', 'Bearer test-token');

      expect([200, 400]).toContain(response.status);
      expect(response.body.data.isValid).toBeDefined();
    });

    it('POST /api/omega/preview/invoices should return invoice export preview', async () => {
      const response = await request(app)
        .post('/api/omega/preview/invoices')
        .set('Authorization', 'Bearer test-token')
        .send({});

      expect(response.body.success).toBe(true);
      expect(response.body.data.invoiceCount).toBeDefined();
    });
  });
});

describe('Config Routes', () => {
  let app: express.Application;

  beforeAll(async () => {
    app = createApp();
  });

  describe('Configuration', () => {
    it('GET /api/config should return configuration', async () => {
      const response = await request(app)
        .get('/api/config')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.uisp).toBeDefined();
      expect(response.body.data.omega).toBeDefined();
      expect(response.body.data.sync).toBeDefined();
    });

    it('GET /api/config/system should return system information', async () => {
      const response = await request(app)
        .get('/api/config/system')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.node).toBeDefined();
      expect(response.body.data.memory).toBeDefined();
    });
  });
});
