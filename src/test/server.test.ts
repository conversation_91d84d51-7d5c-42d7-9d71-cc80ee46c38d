import request from 'supertest';

import app from '@/server';
import { testDb } from './setup';

describe('Server', () => {
  describe('GET /', () => {
    it('should return welcome message in development', async () => {
      const response = await request(app).get('/');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        message: 'UISP-Omega Bridge API',
        version: '1.0.0',
        environment: 'test',
      });
    });
  });

  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const response = await request(app).get('/api/health');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          status: 'healthy',
          environment: 'test',
          database: expect.any(Boolean),
        },
      });
    });
  });

  describe('GET /api/status', () => {
    it('should return system status', async () => {
      const response = await request(app).get('/api/status');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          system: {
            uptime: expect.any(Number),
            memory: expect.any(Object),
            environment: 'test',
            nodeVersion: expect.any(String),
          },
          database: {
            connected: expect.any(Boolean),
            stats: expect.any(Object),
          },
          recentActivity: expect.any(Array),
        },
      });
    });
  });

  describe('GET /api/info', () => {
    it('should return API information', async () => {
      const response = await request(app).get('/api/info');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          name: 'UISP-Omega Bridge API',
          version: '1.0.0',
          description: expect.any(String),
          environment: 'test',
          features: expect.any(Array),
          endpoints: expect.any(Object),
        },
      });
    });
  });

  describe('GET /api/config', () => {
    it('should return configuration (without sensitive data)', async () => {
      // Create test config
      await testDb.config.create({
        data: {
          uispApiUrl: 'https://test.com',
          uispApiKey: 'test-key',
          omegaImportPath: './test-imports',
          autoSyncEnabled: true,
          syncIntervalMinutes: 30,
        },
      });

      const response = await request(app).get('/api/config');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          autoSyncEnabled: true,
          syncIntervalMinutes: 30,
          omegaImportPath: './test-imports',
          hasUispConfig: true,
        },
      });
      
      // Should not expose sensitive data
      expect(response.body.data).not.toHaveProperty('uispApiKey');
      expect(response.body.data).not.toHaveProperty('uispApiUrl');
    });
  });

  describe('GET /api/stats', () => {
    it('should return statistics', async () => {
      const response = await request(app).get('/api/stats');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          sync: {
            totalInvoices: expect.any(Number),
            syncedInvoices: expect.any(Number),
            totalTransactions: expect.any(Number),
            matchedTransactions: expect.any(Number),
            totalPayments: expect.any(Number),
            exportedPayments: expect.any(Number),
          },
          unmatched: {
            transactions: expect.any(Number),
            invoices: expect.any(Number),
          },
          recent: {
            unmatchedTransactions: expect.any(Array),
            unpaidInvoices: expect.any(Array),
          },
        },
      });
    });
  });

  describe('GET /api/logs', () => {
    it('should return operation logs', async () => {
      // Create test log
      await testDb.operationLog.create({
        data: {
          operationType: 'test_operation',
          status: 'success',
          message: 'Test operation completed',
          duration: 1000,
          recordCount: 5,
        },
      });

      const response = await request(app).get('/api/logs');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.any(Array),
      });
      
      if (response.body.data.length > 0) {
        expect(response.body.data[0]).toMatchObject({
          id: expect.any(Number),
          operationType: expect.any(String),
          status: expect.any(String),
          message: expect.any(String),
          createdAt: expect.any(String),
        });
      }
    });

    it('should filter logs by operation type', async () => {
      await testDb.operationLog.createMany({
        data: [
          {
            operationType: 'sync_invoices',
            status: 'success',
            message: 'Sync completed',
          },
          {
            operationType: 'import_bank',
            status: 'success',
            message: 'Import completed',
          },
        ],
      });

      const response = await request(app)
        .get('/api/logs')
        .query({ operationType: 'sync_invoices' });
      
      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].operationType).toBe('sync_invoices');
    });

    it('should filter logs by status', async () => {
      await testDb.operationLog.createMany({
        data: [
          {
            operationType: 'test_op',
            status: 'success',
            message: 'Success message',
          },
          {
            operationType: 'test_op',
            status: 'error',
            message: 'Error message',
          },
        ],
      });

      const response = await request(app)
        .get('/api/logs')
        .query({ status: 'error' });
      
      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('error');
    });

    it('should limit number of logs returned', async () => {
      // Create multiple logs
      const logs = Array.from({ length: 10 }, (_, i) => ({
        operationType: 'test_operation',
        status: 'success',
        message: `Test message ${i}`,
      }));
      
      await testDb.operationLog.createMany({ data: logs });

      const response = await request(app)
        .get('/api/logs')
        .query({ limit: '5' });
      
      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(5);
    });
  });

  describe('GET /api/test-db', () => {
    it('should test database connection', async () => {
      const response = await request(app).get('/api/test-db');
      
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          message: 'Database connection successful',
        },
      });
    });
  });

  describe('Error handling', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app).get('/api/non-existent');
      
      expect(response.status).toBe(404);
      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('not found'),
      });
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/test')
        .set('Content-Type', 'application/json')
        .send('{ invalid json }');
      
      expect(response.status).toBe(400);
    });
  });

  describe('Security headers', () => {
    it('should include security headers', async () => {
      const response = await request(app).get('/api/health');
      
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-xss-protection');
    });
  });

  describe('CORS', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/health')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET');
      
      expect(response.status).toBe(204);
      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });
  });
});
