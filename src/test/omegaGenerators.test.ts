import fs from 'fs/promises';
import path from 'path';

import { InvoiceOmegaGenerator } from '@/generators/invoiceOmegaGenerator';
import { PaymentOmegaGenerator } from '@/generators/paymentOmegaGenerator';
import { OmegaExportService } from '@/services/omegaExport';
import { VATRate } from '@/types/common';
import { testDb, createTestInvoice, createTestInvoiceItem, createTestClientMapping, createTestPayment, createTestBankTransaction, createTestBankStatement } from './setup';

describe('InvoiceOmegaGenerator', () => {
  let generator: InvoiceOmegaGenerator;
  let testData: any;

  beforeEach(async () => {
    generator = new InvoiceOmegaGenerator();
    
    // Create test data
    const clientMapping = await testDb.clientMapping.create({
      data: createTestClientMapping({
        uispClientId: 1,
        omegaPartnerName: 'Test Company s.r.o.',
        omegaPartnerIco: '********',
        omegaPartnerDic: '**********',
        omegaPartnerAddress: 'Test Street 123',
        omegaPartnerCity: 'Bratislava',
        omegaPartnerZip: '12345',
      }),
    });

    const invoice = await testDb.invoice.create({
      data: createTestInvoice({
        clientId: clientMapping.uispClientId,
        invoiceNumber: 'INV-001',
        totalAmount: 120.0,
        status: 'sent',
        omegaImported: false,
      }),
    });

    const invoiceItem = await testDb.invoiceItem.create({
      data: createTestInvoiceItem({
        invoiceId: invoice.id,
        label: 'Test Service',
        quantity: 1,
        unitPrice: 100.0,
        vatRate: VATRate.HIGHER,
        totalAmount: 120.0,
      }),
    });

    testData = {
      clientMapping,
      invoice,
      invoiceItem,
    };
  });

  describe('generateRecords', () => {
    it('should generate Omega records for invoices', async () => {
      const records = await generator.generateRecords();

      expect(records).toHaveLength(1);
      
      const record = records[0];
      expect(record.DOKLAD).toBe('INV-001');
      expect(record.PARTNER).toBe('TEST COMPANY S.R.O.');
      expect(record.NAZOV).toBe('Test Company s.r.o.');
      expect(record.ICO).toBe('********');
      expect(record.DIC).toBe('**********');
      expect(record.SUMA).toBe('120,00');
      expect(record.MENA).toBe('EUR');
      expect(record.NAZOV_POL).toBe('Test Service');
      expect(record.MNOZSTVO).toBe('1,00');
      expect(record.CENA).toBe('100,00');
      expect(record.DPH).toBe('V');
    });

    it('should handle invoices with multiple items', async () => {
      // Add another item
      await testDb.invoiceItem.create({
        data: createTestInvoiceItem({
          invoiceId: testData.invoice.id,
          label: 'Second Service',
          quantity: 2,
          unitPrice: 50.0,
          vatRate: VATRate.LOWER,
          totalAmount: 105.0,
        }),
      });

      const records = await generator.generateRecords();

      expect(records).toHaveLength(2);
      expect(records[0].POLOZKA).toBe('1');
      expect(records[1].POLOZKA).toBe('2');
      expect(records[1].NAZOV_POL).toBe('Second Service');
      expect(records[1].DPH).toBe('X');
    });

    it('should filter by date range', async () => {
      const generator = new InvoiceOmegaGenerator({
        fromDate: new Date('2024-02-01'),
        toDate: new Date('2024-02-28'),
      });

      const records = await generator.generateRecords();
      expect(records).toHaveLength(0); // Invoice is from 2024-01-01
    });

    it('should filter by invoice IDs', async () => {
      const generator = new InvoiceOmegaGenerator({
        invoiceIds: [testData.invoice.id],
      });

      const records = await generator.generateRecords();
      expect(records).toHaveLength(1);
    });

    it('should filter only unexported invoices', async () => {
      // Mark invoice as exported
      await testDb.invoice.update({
        where: { id: testData.invoice.id },
        data: { omegaImported: true },
      });

      const generator = new InvoiceOmegaGenerator({
        onlyUnexported: true,
      });

      const records = await generator.generateRecords();
      expect(records).toHaveLength(0);
    });
  });

  describe('exportInvoices', () => {
    it('should export invoices to CSV file', async () => {
      const result = await generator.exportInvoices();

      expect(result.filePath).toBeDefined();
      expect(result.invoiceCount).toBe(1);
      expect(result.recordCount).toBe(1);

      // Check if file exists
      const fileExists = await fs.access(result.filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);

      // Check file content
      const content = await fs.readFile(result.filePath, 'utf-8');
      expect(content).toContain('INV-001');
      expect(content).toContain('Test Company s.r.o.');
    });
  });

  describe('getExportPreview', () => {
    it('should return export preview', async () => {
      const preview = await generator.getExportPreview();

      expect(preview.invoiceCount).toBe(1);
      expect(preview.recordCount).toBe(1);
      expect(preview.totalAmount).toBe(120.0);
      expect(preview.dateRange.from).toBeInstanceOf(Date);
      expect(preview.dateRange.to).toBeInstanceOf(Date);
    });
  });
});

describe('PaymentOmegaGenerator', () => {
  let generator: PaymentOmegaGenerator;
  let testData: any;

  beforeEach(async () => {
    generator = new PaymentOmegaGenerator();
    
    // Create test data
    const clientMapping = await testDb.clientMapping.create({
      data: createTestClientMapping({
        uispClientId: 1,
        omegaPartnerName: 'Test Company s.r.o.',
      }),
    });

    const invoice = await testDb.invoice.create({
      data: createTestInvoice({
        clientId: clientMapping.uispClientId,
        invoiceNumber: 'INV-001',
        totalAmount: 120.0,
        status: 'paid',
      }),
    });

    const bankStatement = await testDb.bankStatement.create({
      data: {
        accountIban: '************************',
        statementDate: new Date(),
        openingBalance: 1000,
        closingBalance: 1120,
        fileName: 'test.xml',
        fileType: 'XML',
        processed: true,
      },
    });

    const bankTransaction = await testDb.bankTransaction.create({
      data: createTestBankTransaction({
        statementId: bankStatement.id,
        amount: 120.0,
        debitCredit: 'CRDT',
        variableSymbol: 'INV-001',
        counterpartyName: 'Test Company s.r.o.',
        matchedInvoiceId: invoice.id,
      }),
    });

    const payment = await testDb.payment.create({
      data: createTestPayment({
        invoiceId: invoice.id,
        bankTransactionId: bankTransaction.id,
        amount: 120.0,
        omegaExported: false,
      }),
    });

    testData = {
      clientMapping,
      invoice,
      bankStatement,
      bankTransaction,
      payment,
    };
  });

  describe('generateRecords', () => {
    it('should generate Omega records for payments', async () => {
      const records = await generator.generateRecords();

      expect(records).toHaveLength(1);
      
      const record = records[0];
      expect(record.DOKLAD).toMatch(/^P\d{8}/); // Payment document number pattern
      expect(record.PARTNER).toBe('TEST COMPANY S.R.O.');
      expect(record.FAKTURA).toBe('INV-001');
      expect(record.SUMA).toBe('120,00');
      expect(record.VS).toBe('INV-001');
      expect(record.TYP).toBe('BANK');
    });

    it('should filter by date range', async () => {
      const generator = new PaymentOmegaGenerator({
        fromDate: new Date('2024-02-01'),
        toDate: new Date('2024-02-28'),
      });

      const records = await generator.generateRecords();
      expect(records).toHaveLength(0); // Payment is from 2024-01-01
    });

    it('should filter only unexported payments', async () => {
      // Mark payment as exported
      await testDb.payment.update({
        where: { id: testData.payment.id },
        data: { omegaExported: true },
      });

      const generator = new PaymentOmegaGenerator({
        onlyUnexported: true,
      });

      const records = await generator.generateRecords();
      expect(records).toHaveLength(0);
    });
  });

  describe('exportPayments', () => {
    it('should export payments to CSV file', async () => {
      const result = await generator.exportPayments();

      expect(result.filePath).toBeDefined();
      expect(result.paymentCount).toBe(1);
      expect(result.totalAmount).toBe(120.0);

      // Check if file exists
      const fileExists = await fs.access(result.filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);

      // Check file content
      const content = await fs.readFile(result.filePath, 'utf-8');
      expect(content).toContain('INV-001');
      expect(content).toContain('Test Company s.r.o.');
    });
  });

  describe('getExportPreview', () => {
    it('should return export preview', async () => {
      const preview = await generator.getExportPreview();

      expect(preview.paymentCount).toBe(1);
      expect(preview.totalAmount).toBe(120.0);
      expect(preview.invoiceCount).toBe(1);
      expect(preview.dateRange.from).toBeInstanceOf(Date);
      expect(preview.dateRange.to).toBeInstanceOf(Date);
    });
  });
});

describe('OmegaExportService', () => {
  let service: OmegaExportService;
  let testData: any;

  beforeEach(async () => {
    service = new OmegaExportService();
    
    // Create minimal test data
    const clientMapping = await testDb.clientMapping.create({
      data: createTestClientMapping({
        uispClientId: 1,
        omegaPartnerName: 'Test Company s.r.o.',
      }),
    });

    const invoice = await testDb.invoice.create({
      data: createTestInvoice({
        clientId: clientMapping.uispClientId,
        invoiceNumber: 'INV-001',
        totalAmount: 120.0,
        status: 'sent',
        omegaImported: false,
      }),
    });

    await testDb.invoiceItem.create({
      data: createTestInvoiceItem({
        invoiceId: invoice.id,
        label: 'Test Service',
        quantity: 1,
        unitPrice: 100.0,
        vatRate: VATRate.HIGHER,
        totalAmount: 120.0,
      }),
    });

    testData = { clientMapping, invoice };
  });

  describe('exportInvoices', () => {
    it('should export invoices using service', async () => {
      const result = await service.exportInvoices();

      expect(result.filePath).toBeDefined();
      expect(result.fileName).toMatch(/T01_Faktury_\d+\.csv/);
      expect(result.recordCount).toBe(1);
      expect(result.fileSize).toBeGreaterThan(0);
      expect(result.exportDate).toBeInstanceOf(Date);
    });
  });

  describe('batchExport', () => {
    it('should perform batch export', async () => {
      const result = await service.batchExport({
        exportInvoices: true,
        exportPayments: false, // No payments in test data
        cleanupOldFiles: false,
      });

      expect(result.invoices).toBeDefined();
      expect(result.invoices?.recordCount).toBe(1);
      expect(result.payments).toBeUndefined();
    });
  });

  describe('getInvoiceExportPreview', () => {
    it('should return invoice export preview', async () => {
      const preview = await service.getInvoiceExportPreview();

      expect(preview.invoiceCount).toBe(1);
      expect(preview.recordCount).toBe(1);
      expect(preview.totalAmount).toBe(120.0);
    });
  });

  describe('getExportStats', () => {
    it('should return export statistics', async () => {
      // First export something to have stats
      await service.exportInvoices();

      const stats = await service.getExportStats();

      expect(stats.totalExports).toBeGreaterThan(0);
      expect(stats.exportsByType.invoices).toBeGreaterThan(0);
      expect(stats.totalFileSize).toBeGreaterThan(0);
      expect(stats.exportPath).toBeDefined();
    });
  });

  describe('validateConfiguration', () => {
    it('should validate export configuration', async () => {
      const validation = await service.validateConfiguration();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      // May have warnings about no payments to export
    });
  });
});
