import { PaymentMatcherService } from '@/services/paymentMatcher';
import { testDb, createTestInvoice, createTestBankTransaction, createTestClientMapping, createTestBankStatement } from './setup';

describe('PaymentMatcherService', () => {
  let paymentMatcher: PaymentMatcherService;
  let testData: any;

  beforeEach(async () => {
    paymentMatcher = new PaymentMatcherService();
    
    // Create test data
    const clientMapping = await testDb.clientMapping.create({
      data: createTestClientMapping({ 
        uispClientId: 1,
        omegaPartnerName: 'Test Company s.r.o.',
      }),
    });

    const bankStatement = await testDb.bankStatement.create({
      data: createTestBankStatement(),
    });

    const invoice = await testDb.invoice.create({
      data: createTestInvoice({ 
        clientId: clientMapping.uispClientId,
        invoiceNumber: 'INV-001',
        totalAmount: 120.0,
        status: 'sent',
      }),
    });

    testData = {
      clientMapping,
      bankStatement,
      invoice,
    };
  });

  describe('matchByVariableSymbol', () => {
    it('should match transaction with exact variable symbol', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          variableSymbol: 'INV-001',
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice?.id).toBe(testData.invoice.id);
      expect(results[0].matchType).toBe('exact');
      expect(results[0].confidence).toBe(100);
    });

    it('should match with fuzzy variable symbol when amount differs', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 115.0, // Slightly different amount
          debitCredit: 'CRDT',
          variableSymbol: 'INV-001',
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice?.id).toBe(testData.invoice.id);
      expect(results[0].matchType).toBe('fuzzy');
      expect(results[0].confidence).toBe(85);
    });

    it('should not match transaction without variable symbol', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          variableSymbol: null,
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice).toBeUndefined();
      expect(results[0].matchType).toBe('none');
    });
  });

  describe('matchByAmountAndDate', () => {
    it('should match by exact amount and close date', async () => {
      const dueDate = new Date('2024-01-15');
      const paymentDate = new Date('2024-01-17'); // 2 days after due date

      await testDb.invoice.update({
        where: { id: testData.invoice.id },
        data: { dueDate },
      });

      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          transactionDate: paymentDate,
          variableSymbol: null, // No VS to force amount/date matching
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
        maxDaysDifference: 7,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice?.id).toBe(testData.invoice.id);
      expect(results[0].matchType).toBe('fuzzy');
      expect(results[0].confidence).toBeGreaterThan(70);
    });

    it('should not match if date difference is too large', async () => {
      const dueDate = new Date('2024-01-01');
      const paymentDate = new Date('2024-01-20'); // 19 days after due date

      await testDb.invoice.update({
        where: { id: testData.invoice.id },
        data: { dueDate },
      });

      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          transactionDate: paymentDate,
          variableSymbol: null,
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
        maxDaysDifference: 7,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice).toBeUndefined();
      expect(results[0].matchType).toBe('none');
    });
  });

  describe('matchByCounterpartyName', () => {
    it('should match by similar counterparty name', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          counterpartyName: 'Test Company',
          variableSymbol: null,
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
        fuzzyNameMatching: true,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice?.id).toBe(testData.invoice.id);
      expect(results[0].matchType).toBe('fuzzy');
      expect(results[0].confidence).toBeGreaterThan(70);
    });

    it('should not match with very different counterparty name', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          counterpartyName: 'Completely Different Company',
          variableSymbol: null,
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
        fuzzyNameMatching: true,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice).toBeUndefined();
      expect(results[0].matchType).toBe('none');
    });
  });

  describe('matchByReference', () => {
    it('should match by invoice number in description', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          description: 'Payment for invoice INV-001',
          variableSymbol: null,
        }),
      });

      const results = await paymentMatcher.matchPayments({
        transactionId: transaction.id,
        dryRun: true,
      });

      expect(results).toHaveLength(1);
      expect(results[0].invoice?.id).toBe(testData.invoice.id);
      expect(results[0].matchType).toBe('fuzzy');
      expect(results[0].confidence).toBe(80);
    });
  });

  describe('manualMatch', () => {
    it('should create manual match', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
        }),
      });

      const payment = await paymentMatcher.manualMatch(
        transaction.id,
        testData.invoice.id
      );

      expect(payment.invoiceId).toBe(testData.invoice.id);
      expect(payment.bankTransactionId).toBe(transaction.id);
      expect(payment.amount).toBe(120.0);

      // Check that transaction is now matched
      const updatedTransaction = await testDb.bankTransaction.findUnique({
        where: { id: transaction.id },
      });
      expect(updatedTransaction?.matchedInvoiceId).toBe(testData.invoice.id);

      // Check that invoice status is updated
      const updatedInvoice = await testDb.invoice.findUnique({
        where: { id: testData.invoice.id },
      });
      expect(updatedInvoice?.status).toBe('paid');
    });

    it('should throw error for non-existent transaction', async () => {
      await expect(
        paymentMatcher.manualMatch(999, testData.invoice.id)
      ).rejects.toThrow('Bank transaction');
    });

    it('should throw error for non-existent invoice', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
        }),
      });

      await expect(
        paymentMatcher.manualMatch(transaction.id, 999)
      ).rejects.toThrow('Invoice');
    });
  });

  describe('unmatchPayment', () => {
    it('should unmatch payment', async () => {
      const transaction = await testDb.bankTransaction.create({
        data: createTestBankTransaction({
          statementId: testData.bankStatement.id,
          amount: 120.0,
          debitCredit: 'CRDT',
          matchedInvoiceId: testData.invoice.id,
        }),
      });

      const payment = await testDb.payment.create({
        data: {
          invoiceId: testData.invoice.id,
          bankTransactionId: transaction.id,
          amount: 120.0,
          paymentDate: new Date(),
          paymentType: 'bank_transfer',
        },
      });

      await paymentMatcher.unmatchPayment(transaction.id);

      // Check that payment is deleted
      const deletedPayment = await testDb.payment.findUnique({
        where: { id: payment.id },
      });
      expect(deletedPayment).toBeNull();

      // Check that transaction is unmatched
      const updatedTransaction = await testDb.bankTransaction.findUnique({
        where: { id: transaction.id },
      });
      expect(updatedTransaction?.matchedInvoiceId).toBeNull();

      // Check that invoice status is reverted
      const updatedInvoice = await testDb.invoice.findUnique({
        where: { id: testData.invoice.id },
      });
      expect(updatedInvoice?.status).toBe('sent');
    });
  });

  describe('getMatchingStats', () => {
    it('should return matching statistics', async () => {
      // Create some test transactions
      await testDb.bankTransaction.createMany({
        data: [
          createTestBankTransaction({
            statementId: testData.bankStatement.id,
            debitCredit: 'CRDT',
            matchedInvoiceId: testData.invoice.id,
          }),
          createTestBankTransaction({
            statementId: testData.bankStatement.id,
            debitCredit: 'CRDT',
            matchedInvoiceId: null,
          }),
          createTestBankTransaction({
            statementId: testData.bankStatement.id,
            debitCredit: 'DBIT', // Outgoing payment - should not be counted
          }),
        ],
      });

      const stats = await paymentMatcher.getMatchingStats();

      expect(stats.totalTransactions).toBe(2); // Only credit transactions
      expect(stats.matchedTransactions).toBe(1);
      expect(stats.unmatchedTransactions).toBe(1);
      expect(stats.matchingRate).toBe(50);
    });
  });

  describe('getUnmatchedTransactions', () => {
    it('should return unmatched credit transactions', async () => {
      await testDb.bankTransaction.createMany({
        data: [
          createTestBankTransaction({
            statementId: testData.bankStatement.id,
            debitCredit: 'CRDT',
            matchedInvoiceId: null,
          }),
          createTestBankTransaction({
            statementId: testData.bankStatement.id,
            debitCredit: 'CRDT',
            matchedInvoiceId: testData.invoice.id,
          }),
          createTestBankTransaction({
            statementId: testData.bankStatement.id,
            debitCredit: 'DBIT',
            matchedInvoiceId: null,
          }),
        ],
      });

      const unmatched = await paymentMatcher.getUnmatchedTransactions();

      expect(unmatched).toHaveLength(1);
      expect(unmatched[0].debitCredit).toBe('CRDT');
      expect(unmatched[0].matchedInvoiceId).toBeNull();
    });
  });
});
