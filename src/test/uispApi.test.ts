import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

import { UispApiClient } from '@/services/uispApi';
import { UispApiError } from '@/utils/errors';
import type { UispInvoiceReadOnly, UispClient, UispOrganization } from '@/types/uisp';

// Mock axios
const mockAxios = new MockAdapter(axios);

describe('UispApiClient', () => {
  let uispApi: UispApiClient;
  const testApiUrl = 'https://test-uisp.com/api/v1.0';
  const testApiKey = 'test-api-key';

  beforeEach(() => {
    mockAxios.reset();
    uispApi = new UispApiClient(testApiUrl, testApiKey);
  });

  afterEach(() => {
    mockAxios.restore();
  });

  describe('constructor', () => {
    it('should create instance with provided credentials', () => {
      const client = new UispApiClient(testApiUrl, testApiKey);
      const status = client.getApiStatus();
      
      expect(status.baseUrl).toBe(testApiUrl);
      expect(status.hasApiKey).toBe(true);
      expect(status.isConfigured).toBe(true);
    });

    it('should throw error if credentials are missing', () => {
      expect(() => new UispApiClient('', '')).toThrow(UispApiError);
    });
  });

  describe('testConnection', () => {
    it('should return true for successful connection', async () => {
      const mockOrganizations: UispOrganization[] = [
        {
          id: 1,
          name: 'Test Organization',
          currencyCode: 'EUR',
          locale: 'en',
          selected: true,
        },
      ];

      mockAxios.onGet('/organizations').reply(200, mockOrganizations);

      const result = await uispApi.testConnection();
      expect(result).toBe(true);
    });

    it('should return false for failed connection', async () => {
      mockAxios.onGet('/organizations').reply(401, { message: 'Unauthorized' });

      const result = await uispApi.testConnection();
      expect(result).toBe(false);
    });
  });

  describe('getOrganizations', () => {
    it('should fetch organizations successfully', async () => {
      const mockOrganizations: UispOrganization[] = [
        {
          id: 1,
          name: 'Test Organization',
          currencyCode: 'EUR',
          locale: 'en',
          selected: true,
        },
      ];

      mockAxios.onGet('/organizations').reply(200, mockOrganizations);

      const result = await uispApi.getOrganizations();
      expect(result).toEqual(mockOrganizations);
    });

    it('should handle API errors', async () => {
      mockAxios.onGet('/organizations').reply(500, { message: 'Internal Server Error' });

      await expect(uispApi.getOrganizations()).rejects.toThrow(UispApiError);
    });
  });

  describe('getClients', () => {
    it('should fetch clients successfully', async () => {
      const mockClients: UispClient[] = [
        {
          id: 1,
          clientType: 1,
          isLead: false,
          firstName: 'John',
          lastName: 'Doe',
          companyName: 'Test Company',
          companyRegistrationNumber: '********',
          sendInvoiceByPost: false,
          invoiceMaturityDays: 30,
          stopServiceDue: false,
          stopServiceDueDelayed: false,
          accountStandingsCredit: 0,
          accountStandingsRefundableCredit: 0,
          currencyCode: 'EUR',
          organizationId: 1,
          isActive: true,
          createdDate: '2024-01-01T00:00:00Z',
        },
      ];

      mockAxios.onGet('/clients').reply(200, mockClients);

      const result = await uispApi.getClients();
      expect(result).toEqual(mockClients);
    });

    it('should handle query parameters', async () => {
      mockAxios.onGet('/clients').reply(200, []);

      await uispApi.getClients({
        organizationId: 1,
        isActive: true,
        limit: 50,
      });

      expect(mockAxios.history.get[0].params).toEqual({
        organizationId: 1,
        isActive: true,
        limit: 50,
      });
    });
  });

  describe('getInvoices', () => {
    it('should fetch invoices successfully', async () => {
      const mockInvoices: UispInvoiceReadOnly[] = [
        {
          id: 1,
          number: 'INV-001',
          clientId: 1,
          status: 1,
          createdDate: '2024-01-01T00:00:00Z',
          dueDate: '2024-01-31T00:00:00Z',
          currencyCode: 'EUR',
          exchangeRate: 1,
          maturityDays: 30,
          discount: 0,
          subtotal: 100,
          taxes: 20,
          total: 120,
          amountPaid: 0,
          amountDue: 120,
          items: [],
          emailSentCount: 0,
          isProforma: false,
          organizationId: 1,
          uncollectible: false,
          void: false,
        },
      ];

      mockAxios.onGet('/invoices').reply(200, mockInvoices);

      const result = await uispApi.getInvoices();
      expect(result).toEqual(mockInvoices);
    });

    it('should handle date range parameters', async () => {
      mockAxios.onGet('/invoices').reply(200, []);

      await uispApi.getInvoicesByDateRange(
        new Date('2024-01-01'),
        new Date('2024-01-31')
      );

      expect(mockAxios.history.get[0].params).toMatchObject({
        createdDateFrom: '2024-01-01T00:00:00.000Z',
        createdDateTo: '2024-01-31T00:00:00.000Z',
      });
    });
  });

  describe('getInvoice', () => {
    it('should fetch single invoice successfully', async () => {
      const mockInvoice: UispInvoiceReadOnly = {
        id: 1,
        number: 'INV-001',
        clientId: 1,
        status: 1,
        createdDate: '2024-01-01T00:00:00Z',
        dueDate: '2024-01-31T00:00:00Z',
        currencyCode: 'EUR',
        exchangeRate: 1,
        maturityDays: 30,
        discount: 0,
        subtotal: 100,
        taxes: 20,
        total: 120,
        amountPaid: 0,
        amountDue: 120,
        items: [
          {
            id: 1,
            label: 'Test Item',
            price: 100,
            quantity: 1,
            tax1: 20,
            total: 120,
          },
        ],
        emailSentCount: 0,
        isProforma: false,
        organizationId: 1,
        uncollectible: false,
        void: false,
      };

      mockAxios.onGet('/invoices/1').reply(200, mockInvoice);

      const result = await uispApi.getInvoice(1);
      expect(result).toEqual(mockInvoice);
    });

    it('should handle 404 errors', async () => {
      mockAxios.onGet('/invoices/999').reply(404, { message: 'Invoice not found' });

      await expect(uispApi.getInvoice(999)).rejects.toThrow(UispApiError);
    });
  });

  describe('createInvoice', () => {
    it('should create invoice successfully', async () => {
      const newInvoice = {
        clientId: 1,
        items: [
          {
            label: 'Test Item',
            price: 100,
            quantity: 1,
            tax1: 20,
          },
        ],
      };

      const createdInvoice: UispInvoiceReadOnly = {
        id: 1,
        number: 'INV-001',
        clientId: 1,
        status: 0,
        createdDate: '2024-01-01T00:00:00Z',
        dueDate: '2024-01-31T00:00:00Z',
        currencyCode: 'EUR',
        exchangeRate: 1,
        maturityDays: 30,
        discount: 0,
        subtotal: 100,
        taxes: 20,
        total: 120,
        amountPaid: 0,
        amountDue: 120,
        items: [
          {
            id: 1,
            label: 'Test Item',
            price: 100,
            quantity: 1,
            tax1: 20,
            total: 120,
          },
        ],
        emailSentCount: 0,
        isProforma: false,
        organizationId: 1,
        uncollectible: false,
        void: false,
      };

      mockAxios.onPost('/clients/1/invoices').reply(201, createdInvoice);

      const result = await uispApi.createInvoice(1, newInvoice);
      expect(result).toEqual(createdInvoice);
    });
  });

  describe('getAllInvoices', () => {
    it('should fetch all invoices with pagination', async () => {
      const firstBatch: UispInvoiceReadOnly[] = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        number: `INV-${String(i + 1).padStart(3, '0')}`,
        clientId: 1,
        status: 1,
        createdDate: '2024-01-01T00:00:00Z',
        dueDate: '2024-01-31T00:00:00Z',
        currencyCode: 'EUR',
        exchangeRate: 1,
        maturityDays: 30,
        discount: 0,
        subtotal: 100,
        taxes: 20,
        total: 120,
        amountPaid: 0,
        amountDue: 120,
        items: [],
        emailSentCount: 0,
        isProforma: false,
        organizationId: 1,
        uncollectible: false,
        void: false,
      }));

      const secondBatch: UispInvoiceReadOnly[] = Array.from({ length: 50 }, (_, i) => ({
        ...firstBatch[0],
        id: i + 101,
        number: `INV-${String(i + 101).padStart(3, '0')}`,
      }));

      mockAxios
        .onGet('/invoices', { params: { limit: 100, offset: 0 } })
        .reply(200, firstBatch)
        .onGet('/invoices', { params: { limit: 100, offset: 100 } })
        .reply(200, secondBatch);

      const result = await uispApi.getAllInvoices();
      expect(result).toHaveLength(150);
      expect(result[0].id).toBe(1);
      expect(result[149].id).toBe(150);
    });
  });

  describe('getRecentInvoices', () => {
    it('should fetch recent invoices', async () => {
      mockAxios.onGet('/invoices').reply(200, []);

      await uispApi.getRecentInvoices(7);

      const expectedFromDate = new Date();
      expectedFromDate.setDate(expectedFromDate.getDate() - 7);

      expect(mockAxios.history.get[0].params.createdDateFrom).toBeDefined();
    });
  });

  describe('getUnpaidInvoices', () => {
    it('should fetch unpaid invoices', async () => {
      mockAxios.onGet('/invoices').reply(200, []);

      await uispApi.getUnpaidInvoices();

      expect(mockAxios.history.get[0].params.status).toBe(1); // UNPAID status
    });
  });

  describe('updateCredentials', () => {
    it('should update API credentials', () => {
      const newUrl = 'https://new-uisp.com/api/v1.0';
      const newKey = 'new-api-key';

      uispApi.updateCredentials(newUrl, newKey);

      const status = uispApi.getApiStatus();
      expect(status.baseUrl).toBe(newUrl);
      expect(status.hasApiKey).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle network errors', async () => {
      mockAxios.onGet('/invoices').networkError();

      await expect(uispApi.getInvoices()).rejects.toThrow(UispApiError);
    });

    it('should handle timeout errors', async () => {
      mockAxios.onGet('/invoices').timeout();

      await expect(uispApi.getInvoices()).rejects.toThrow(UispApiError);
    });

    it('should handle API rate limiting', async () => {
      mockAxios.onGet('/invoices').reply(429, { message: 'Rate limit exceeded' });

      await expect(uispApi.getInvoices()).rejects.toThrow(UispApiError);
    });
  });
});
