import { BankParserFactory } from '@/parsers/bankParserFactory';
import { XmlBankParser } from '@/parsers/xmlBankParser';
import { SwiftBankParser } from '@/parsers/swiftBankParser';
import { XlsBankParser } from '@/parsers/xlsBankParser';
import { BankParsingError } from '@/utils/errors';

describe('BankParserFactory', () => {
  describe('detectFileType', () => {
    it('should detect XML files', () => {
      expect(BankParserFactory.detectFileType('statement.xml')).toBe('XML');
      expect(BankParserFactory.detectFileType('sepa_statement.xml')).toBe('XML');
    });

    it('should detect SWIFT files', () => {
      expect(BankParserFactory.detectFileType('statement.sta')).toBe('SWIFT');
      expect(BankParserFactory.detectFileType('mt940.swift')).toBe('SWIFT');
      expect(BankParserFactory.detectFileType('bank_mt940.txt')).toBe('SWIFT');
    });

    it('should detect XLS files', () => {
      expect(BankParserFactory.detectFileType('statement.xls')).toBe('XLS');
      expect(BankParserFactory.detectFileType('export.xlsx')).toBe('XLS');
    });

    it('should throw error for unsupported files', () => {
      expect(() => BankParserFactory.detectFileType('document.pdf')).toThrow(BankParsingError);
      expect(() => BankParserFactory.detectFileType('text.txt')).toThrow(BankParsingError);
    });
  });

  describe('createParser', () => {
    it('should create XML parser', () => {
      const parser = BankParserFactory.createParser('test.xml');
      expect(parser).toBeInstanceOf(XmlBankParser);
    });

    it('should create SWIFT parser', () => {
      const parser = BankParserFactory.createParser('test.sta');
      expect(parser).toBeInstanceOf(SwiftBankParser);
    });

    it('should create XLS parser', () => {
      const parser = BankParserFactory.createParser('test.xls');
      expect(parser).toBeInstanceOf(XlsBankParser);
    });

    it('should respect explicit file type', () => {
      const parser = BankParserFactory.createParser('test.txt', 'XML');
      expect(parser).toBeInstanceOf(XmlBankParser);
    });
  });

  describe('validateFile', () => {
    it('should validate empty file', () => {
      const result = BankParserFactory.validateFile('test.xml', '');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File is empty');
    });

    it('should validate file size', () => {
      const largeContent = 'x'.repeat(51 * 1024 * 1024); // 51MB
      const result = BankParserFactory.validateFile('test.xml', largeContent);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File is too large (max 50MB)');
    });

    it('should validate XML content', () => {
      const result = BankParserFactory.validateFile('test.xml', 'not xml content');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File does not appear to be valid XML');
    });

    it('should validate SWIFT content', () => {
      const result = BankParserFactory.validateFile('test.sta', 'not swift content');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File does not appear to be valid SWIFT MT940 format');
    });

    it('should pass valid XML content', () => {
      const xmlContent = '<?xml version="1.0"?><Document></Document>';
      const result = BankParserFactory.validateFile('test.xml', xmlContent);
      expect(result.isValid).toBe(true);
      expect(result.detectedType).toBe('XML');
    });

    it('should pass valid SWIFT content', () => {
      const swiftContent = ':20:REFERENCE\n:25:SK1234567890\n:28C:1/1';
      const result = BankParserFactory.validateFile('test.sta', swiftContent);
      expect(result.isValid).toBe(true);
      expect(result.detectedType).toBe('SWIFT');
    });
  });

  describe('getSupportedFileTypes', () => {
    it('should return all supported file types', () => {
      const types = BankParserFactory.getSupportedFileTypes();
      expect(types).toHaveLength(3);
      expect(types.map(t => t.type)).toEqual(['XML', 'SWIFT', 'XLS']);
    });
  });

  describe('isFileTypeSupported', () => {
    it('should return true for supported files', () => {
      expect(BankParserFactory.isFileTypeSupported('test.xml')).toBe(true);
      expect(BankParserFactory.isFileTypeSupported('test.sta')).toBe(true);
      expect(BankParserFactory.isFileTypeSupported('test.xls')).toBe(true);
    });

    it('should return false for unsupported files', () => {
      expect(BankParserFactory.isFileTypeSupported('test.pdf')).toBe(false);
      expect(BankParserFactory.isFileTypeSupported('test.txt')).toBe(false);
    });
  });
});

describe('XmlBankParser', () => {
  let parser: XmlBankParser;

  beforeEach(() => {
    parser = new XmlBankParser('test.xml');
  });

  describe('parse', () => {
    it('should parse valid SEPA XML', async () => {
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:camt.053.001.02">
  <BkToCstmrStmt>
    <Stmt>
      <Id>STMT001</Id>
      <CreDtTm>2024-01-31T23:59:59</CreDtTm>
      <Acct>
        <Id>
          <IBAN>************************</IBAN>
        </Id>
      </Acct>
      <Bal>
        <Tp>
          <CdOrPrtry>
            <Cd>OPBD</Cd>
          </CdOrPrtry>
        </Tp>
        <Amt Ccy="EUR">1000.00</Amt>
        <CdtDbtInd>CRDT</CdtDbtInd>
        <Dt>
          <Dt>2024-01-31</Dt>
        </Dt>
      </Bal>
      <Bal>
        <Tp>
          <CdOrPrtry>
            <Cd>CLBD</Cd>
          </CdOrPrtry>
        </Tp>
        <Amt Ccy="EUR">1200.00</Amt>
        <CdtDbtInd>CRDT</CdtDbtInd>
        <Dt>
          <Dt>2024-01-31</Dt>
        </Dt>
      </Bal>
      <Ntry>
        <NtryRef>REF123</NtryRef>
        <Amt Ccy="EUR">200.00</Amt>
        <CdtDbtInd>CRDT</CdtDbtInd>
        <BookgDt>
          <Dt>2024-01-15</Dt>
        </BookgDt>
        <NtryDtls>
          <TxDtls>
            <RmtInf>
              <Ustrd>Payment for invoice INV-001 VS123456</Ustrd>
            </RmtInf>
            <RltdPties>
              <Dbtr>
                <Nm>Test Company s.r.o.</Nm>
              </Dbtr>
              <DbtrAcct>
                <Id>
                  <IBAN>************************</IBAN>
                </Id>
              </DbtrAcct>
            </RltdPties>
          </TxDtls>
        </NtryDtls>
      </Ntry>
    </Stmt>
  </BkToCstmrStmt>
</Document>`;

      const result = await parser.parse(xmlContent);

      expect(result.statement.accountIban).toBe('************************');
      expect(result.statement.openingBalance).toBe(1000);
      expect(result.statement.closingBalance).toBe(1200);
      expect(result.transactions).toHaveLength(1);
      
      const transaction = result.transactions[0];
      expect(transaction.amount).toBe(200);
      expect(transaction.debitCredit).toBe('CRDT');
      expect(transaction.variableSymbol).toBe('123456');
      expect(transaction.counterpartyName).toBe('Test Company s.r.o.');
    });

    it('should handle invalid XML', async () => {
      const invalidXml = 'not xml content';
      await expect(parser.parse(invalidXml)).rejects.toThrow(BankParsingError);
    });

    it('should handle missing required fields', async () => {
      const incompleteXml = '<?xml version="1.0"?><Document></Document>';
      await expect(parser.parse(incompleteXml)).rejects.toThrow(BankParsingError);
    });
  });
});

describe('SwiftBankParser', () => {
  let parser: SwiftBankParser;

  beforeEach(() => {
    parser = new SwiftBankParser('test.sta');
  });

  describe('parse', () => {
    it('should parse valid SWIFT MT940', async () => {
      const swiftContent = `:20:REFERENCE123
:25:************************
:28C:1/1
:60F:C240131EUR1000,00
:61:2401150115CR200,00NTRFREF123//VS123456
:86:Payment for invoice INV-001
:62F:C240131EUR1200,00`;

      const result = await parser.parse(swiftContent);

      expect(result.statement.accountIban).toBe('************************');
      expect(result.statement.openingBalance).toBe(1000);
      expect(result.statement.closingBalance).toBe(1200);
      expect(result.transactions).toHaveLength(1);
      
      const transaction = result.transactions[0];
      expect(transaction.amount).toBe(200);
      expect(transaction.debitCredit).toBe('CRDT');
      expect(transaction.variableSymbol).toBe('123456');
    });

    it('should handle missing required fields', async () => {
      const incompleteSwift = ':20:REF';
      await expect(parser.parse(incompleteSwift)).rejects.toThrow(BankParsingError);
    });
  });
});

describe('XlsBankParser', () => {
  let parser: XlsBankParser;

  beforeEach(() => {
    parser = new XlsBankParser('************************_20240131.xls');
  });

  describe('parse', () => {
    it('should extract account info from filename', async () => {
      // This would require actual Excel file content for full testing
      // For now, we test the filename parsing logic
      expect(parser['extractAccountInfo']).toBeDefined();
    });

    it('should handle empty Excel file', async () => {
      const emptyBuffer = Buffer.alloc(0);
      await expect(parser.parse(emptyBuffer)).rejects.toThrow(BankParsingError);
    });
  });
});

describe('parseMultipleFiles', () => {
  it('should parse multiple files', async () => {
    const files = [
      {
        fileName: 'test1.xml',
        content: '<?xml version="1.0"?><Document><BkToCstmrStmt><Stmt><Acct><Id><IBAN>SK123</IBAN></Id></Acct><Bal><Tp><CdOrPrtry><Cd>OPBD</Cd></CdOrPrtry></Tp><Amt>100</Amt><CdtDbtInd>CRDT</CdtDbtInd><Dt><Dt>2024-01-01</Dt></Dt></Bal><Bal><Tp><CdOrPrtry><Cd>CLBD</Cd></CdOrPrtry></Tp><Amt>100</Amt><CdtDbtInd>CRDT</CdtDbtInd><Dt><Dt>2024-01-01</Dt></Dt></Bal></Stmt></BkToCstmrStmt></Document>',
      },
      {
        fileName: 'invalid.xml',
        content: 'invalid content',
      },
    ];

    const results = await BankParserFactory.parseMultipleFiles(files);

    expect(results).toHaveLength(2);
    expect(results[0].result).toBeDefined();
    expect(results[1].error).toBeDefined();
  });
});
