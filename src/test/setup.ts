import { PrismaClient } from '@prisma/client';

import { config } from '@/config';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'file:./test.db';

// Create test database client
const testDb = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./test.db',
    },
  },
});

// Global test setup
beforeAll(async () => {
  // Ensure test database is clean
  await cleanupTestDatabase();
});

// Cleanup after each test
afterEach(async () => {
  await cleanupTestDatabase();
});

// Global test teardown
afterAll(async () => {
  await testDb.$disconnect();
});

// Helper function to clean up test database
async function cleanupTestDatabase(): Promise<void> {
  const tablenames = await testDb.$queryRaw<
    Array<{ name: string }>
  >`SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_migrations';`;

  const tables = tablenames
    .map(({ name }) => name)
    .filter(name => name !== '_prisma_migrations');

  try {
    for (const table of tables) {
      await testDb.$executeRawUnsafe(`DELETE FROM "${table}";`);
    }
  } catch (error) {
    console.log('Error cleaning up test database:', error);
  }
}

// Export test utilities
export { testDb };

// Mock logger for tests
jest.mock('@/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
  logOperation: jest.fn(),
  logApiCall: jest.fn(),
  logDatabaseOperation: jest.fn(),
  logFileOperation: jest.fn(),
  logSchedulerJob: jest.fn(),
}));

// Mock config for tests
jest.mock('@/config', () => ({
  config: {
    nodeEnv: 'test',
    port: 3001,
    isDevelopment: false,
    isProduction: false,
    isTest: true,
    database: {
      url: 'file:./test.db',
    },
    uisp: {
      apiUrl: 'https://test-uisp.com/api/v1.0',
      apiKey: 'test-api-key',
    },
    omega: {
      importPath: './test-omega-imports',
    },
    security: {
      jwtSecret: 'test-jwt-secret',
      bcryptRounds: 4, // Lower for faster tests
    },
    rateLimit: {
      windowMs: 900000,
      maxRequests: 100,
    },
    upload: {
      maxFileSize: 10485760,
      uploadPath: './test-uploads',
    },
    logging: {
      level: 'error', // Reduce noise in tests
      file: './test-logs/app.log',
    },
    scheduler: {
      syncIntervalMinutes: 30,
      autoSyncEnabled: false,
    },
    email: {
      smtp: {
        host: undefined,
        port: undefined,
        user: undefined,
        pass: undefined,
      },
      notificationEmail: undefined,
    },
  },
}));

// Test data factories
export const createTestInvoice = (overrides: Partial<any> = {}) => ({
  uispInvoiceId: Math.floor(Math.random() * 10000),
  invoiceNumber: `TEST-${Date.now()}`,
  clientId: 1,
  totalAmount: 100.0,
  currency: 'EUR',
  createdDate: new Date(),
  dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  status: 'sent',
  rawData: JSON.stringify({ test: true }),
  ...overrides,
});

export const createTestBankTransaction = (overrides: Partial<any> = {}) => ({
  statementId: 1,
  transactionDate: new Date(),
  amount: 100.0,
  currency: 'EUR',
  debitCredit: 'CRDT',
  variableSymbol: `VS${Math.floor(Math.random() * 10000)}`,
  counterpartyName: 'Test Company',
  description: 'Test payment',
  ...overrides,
});

export const createTestClientMapping = (overrides: Partial<any> = {}) => ({
  uispClientId: Math.floor(Math.random() * 10000),
  omegaPartnerCode: `PART${Math.floor(Math.random() * 1000)}`,
  omegaPartnerName: 'Test Partner',
  omegaPartnerIco: '********',
  omegaPartnerDic: '**********',
  ...overrides,
});

export const createTestBankStatement = (overrides: Partial<any> = {}) => ({
  accountIban: '************************',
  statementDate: new Date(),
  openingBalance: 1000.0,
  closingBalance: 1100.0,
  fileName: `test-statement-${Date.now()}.xml`,
  fileType: 'XML',
  processed: true,
  ...overrides,
});

// Helper to create test data in database
export const seedTestData = async () => {
  // Create test client mapping
  const clientMapping = await testDb.clientMapping.create({
    data: createTestClientMapping({ uispClientId: 1 }),
  });

  // Create test bank statement
  const bankStatement = await testDb.bankStatement.create({
    data: createTestBankStatement(),
  });

  // Create test invoice
  const invoice = await testDb.invoice.create({
    data: createTestInvoice({ clientId: clientMapping.uispClientId }),
  });

  // Create test bank transaction
  const bankTransaction = await testDb.bankTransaction.create({
    data: createTestBankTransaction({ statementId: bankStatement.id }),
  });

  return {
    clientMapping,
    bankStatement,
    invoice,
    bankTransaction,
  };
};
