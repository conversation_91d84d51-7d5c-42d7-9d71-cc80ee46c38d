import type { Invoice, InvoiceItem, ClientMapping } from '@prisma/client';

import db from '@/database/client';
import { BaseOmegaGenerator, type OmegaRecord, type OmegaExportOptions } from './baseOmegaGenerator';
import { OmegaDocumentType } from '@/types/common';
import { OmegaExportError, NotFoundError } from '@/utils/errors';
import { logger } from '@/utils/logger';

export interface InvoiceExportOptions extends OmegaExportOptions {
  fromDate?: Date;
  toDate?: Date;
  invoiceIds?: number[];
  clientIds?: number[];
  onlyPaid?: boolean;
  onlyUnexported?: boolean;
}

export interface InvoiceOmegaRecord extends OmegaRecord {
  // Header fields (T01)
  DOKLAD: string;           // Document number
  DATUM: string;            // Invoice date
  SPLATNOST: string;        // Due date
  PARTNER: string;          // Partner code
  NAZOV: string;            // Partner name
  ICO: string;              // Company registration number
  DIC: string;              // Tax ID
  ULICA: string;            // Street
  MESTO: string;            // City
  PSC: string;              // Postal code
  STAT: string;             // Country
  MENA: string;             // Currency
  KURZ: string;             // Exchange rate
  POZNAMKA: string;         // Note
  
  // Item fields
  POLOZKA: string;          // Item number
  NAZOV_POL: string;        // Item name
  MNOZSTVO: string;         // Quantity
  JEDNOTKA: string;         // Unit
  CENA: string;             // Unit price
  DPH: string;              // VAT rate
  SUMA: string;             // Total amount
}

export class InvoiceOmegaGenerator extends BaseOmegaGenerator {
  private options: InvoiceExportOptions;

  constructor(options: InvoiceExportOptions = {}) {
    super(OmegaDocumentType.CUSTOMER_INVOICE, options);
    this.options = options;
  }

  async generateRecords(): Promise<InvoiceOmegaRecord[]> {
    logger.info('Starting invoice export to Omega format', {
      options: this.options,
    });

    try {
      // Get invoices to export
      const invoices = await this.getInvoicesToExport();
      
      if (invoices.length === 0) {
        logger.warn('No invoices found for export');
        return [];
      }

      logger.info(`Found ${invoices.length} invoices to export`);

      const records: InvoiceOmegaRecord[] = [];

      // Process each invoice
      for (const invoice of invoices) {
        try {
          const invoiceRecords = await this.generateInvoiceRecords(invoice);
          records.push(...invoiceRecords);
        } catch (error) {
          logger.error(`Error processing invoice ${invoice.id}:`, error);
          throw new OmegaExportError(
            `Failed to process invoice ${invoice.invoiceNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }

      logger.info(`Generated ${records.length} Omega records from ${invoices.length} invoices`);
      return records;

    } catch (error) {
      logger.error('Invoice export failed:', error);
      throw error instanceof OmegaExportError 
        ? error 
        : new OmegaExportError(`Invoice export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getInvoicesToExport(): Promise<Array<Invoice & {
    items: InvoiceItem[];
    clientMapping: ClientMapping | null;
  }>> {
    const where: any = {};

    // Date range filter
    if (this.options.fromDate || this.options.toDate) {
      where.createdDate = {};
      if (this.options.fromDate) {
        where.createdDate.gte = this.options.fromDate;
      }
      if (this.options.toDate) {
        where.createdDate.lte = this.options.toDate;
      }
    }

    // Specific invoice IDs
    if (this.options.invoiceIds && this.options.invoiceIds.length > 0) {
      where.id = { in: this.options.invoiceIds };
    }

    // Specific client IDs
    if (this.options.clientIds && this.options.clientIds.length > 0) {
      where.clientId = { in: this.options.clientIds };
    }

    // Only paid invoices
    if (this.options.onlyPaid) {
      where.status = 'paid';
    }

    // Only unexported invoices
    if (this.options.onlyUnexported) {
      where.omegaImported = false;
    }

    return db.invoice.findMany({
      where,
      include: {
        items: true,
        clientMapping: true,
      },
      orderBy: {
        createdDate: 'asc',
      },
    });
  }

  private async generateInvoiceRecords(
    invoice: Invoice & {
      items: InvoiceItem[];
      clientMapping: ClientMapping | null;
    }
  ): Promise<InvoiceOmegaRecord[]> {
    
    // Validate invoice
    this.validateInvoice(invoice);

    const records: InvoiceOmegaRecord[] = [];
    const clientMapping = invoice.clientMapping;

    if (!clientMapping) {
      throw new OmegaExportError(`No client mapping found for invoice ${invoice.invoiceNumber}`);
    }

    // Generate base record data (common for all items)
    const baseRecord = this.generateBaseRecord(invoice, clientMapping);

    // Generate record for each invoice item
    for (let i = 0; i < invoice.items.length; i++) {
      const item = invoice.items[i];
      const itemRecord = this.generateItemRecord(baseRecord, item, i + 1);
      records.push(itemRecord);
    }

    return records;
  }

  private generateBaseRecord(
    invoice: Invoice,
    clientMapping: ClientMapping
  ): Partial<InvoiceOmegaRecord> {
    
    return {
      DOKLAD: this.formatOmegaText(invoice.invoiceNumber, 20),
      DATUM: this.formatOmegaDate(invoice.createdDate),
      SPLATNOST: this.formatOmegaDate(invoice.dueDate),
      PARTNER: this.formatOmegaPartnerCode(clientMapping.omegaPartnerName || `CLIENT_${invoice.clientId}`),
      NAZOV: this.formatOmegaText(clientMapping.omegaPartnerName || 'Unknown Client', 75),
      ICO: clientMapping.omegaPartnerIco ? this.formatOmegaICO(clientMapping.omegaPartnerIco) : '',
      DIC: clientMapping.omegaPartnerDic ? this.formatOmegaDIC(clientMapping.omegaPartnerDic) : '',
      ULICA: this.formatOmegaText(clientMapping.omegaPartnerAddress || '', 50),
      MESTO: this.formatOmegaText(clientMapping.omegaPartnerCity || '', 30),
      PSC: this.formatOmegaText(clientMapping.omegaPartnerZip || '', 10),
      STAT: 'SK', // Default to Slovakia
      MENA: invoice.currency,
      KURZ: '1,00', // Default exchange rate
      POZNAMKA: this.formatOmegaText(`UISP Invoice ${invoice.invoiceNumber}`, 75),
    };
  }

  private generateItemRecord(
    baseRecord: Partial<InvoiceOmegaRecord>,
    item: InvoiceItem,
    itemNumber: number
  ): InvoiceOmegaRecord {
    
    this.validateInvoiceItem(item);

    return {
      ...baseRecord,
      POLOZKA: itemNumber.toString(),
      NAZOV_POL: this.formatOmegaText(item.label, 75),
      MNOZSTVO: this.formatOmegaAmount(item.quantity),
      JEDNOTKA: 'ks', // Default unit
      CENA: this.formatOmegaAmount(item.unitPrice),
      DPH: this.formatOmegaVatRate(item.vatRate),
      SUMA: this.formatOmegaAmount(item.totalAmount),
    } as InvoiceOmegaRecord;
  }

  private validateInvoice(invoice: Invoice): void {
    if (!invoice.invoiceNumber) {
      throw new OmegaExportError(`Invoice ${invoice.id} missing invoice number`);
    }

    if (!invoice.createdDate) {
      throw new OmegaExportError(`Invoice ${invoice.invoiceNumber} missing created date`);
    }

    if (!invoice.dueDate) {
      throw new OmegaExportError(`Invoice ${invoice.invoiceNumber} missing due date`);
    }

    if (!invoice.totalAmount || invoice.totalAmount <= 0) {
      throw new OmegaExportError(`Invoice ${invoice.invoiceNumber} has invalid total amount`);
    }

    if (!invoice.items || invoice.items.length === 0) {
      throw new OmegaExportError(`Invoice ${invoice.invoiceNumber} has no items`);
    }

    this.validateDate(invoice.createdDate);
    this.validateDate(invoice.dueDate);
    this.validateAmount(invoice.totalAmount);
  }

  private validateInvoiceItem(item: InvoiceItem): void {
    if (!item.label) {
      throw new OmegaExportError(`Invoice item ${item.id} missing label`);
    }

    if (!item.quantity || item.quantity <= 0) {
      throw new OmegaExportError(`Invoice item ${item.id} has invalid quantity`);
    }

    if (!item.unitPrice || item.unitPrice < 0) {
      throw new OmegaExportError(`Invoice item ${item.id} has invalid unit price`);
    }

    if (!item.vatRate) {
      throw new OmegaExportError(`Invoice item ${item.id} missing VAT rate`);
    }

    this.validateAmount(item.quantity);
    this.validateAmount(item.unitPrice);
    this.validateAmount(item.totalAmount);
    this.validateVatRate(item.vatRate);
  }

  // Export invoices and mark as exported
  async exportInvoices(options: InvoiceExportOptions = {}): Promise<{
    filePath: string;
    invoiceCount: number;
    recordCount: number;
  }> {
    
    // Merge options
    this.options = { ...this.options, ...options };

    // Generate CSV
    const filePath = await this.generateCsv();
    
    // Get the records that were exported
    const records = await this.generateRecords();
    const invoiceIds = this.extractInvoiceIds(records);

    // Mark invoices as exported to Omega
    if (!options.onlyUnexported) { // Don't mark if this was just a preview
      await this.markInvoicesAsExported(invoiceIds);
    }

    logger.info('Invoice export completed', {
      filePath,
      invoiceCount: invoiceIds.length,
      recordCount: records.length,
    });

    return {
      filePath,
      invoiceCount: invoiceIds.length,
      recordCount: records.length,
    };
  }

  private extractInvoiceIds(records: InvoiceOmegaRecord[]): number[] {
    // Extract unique invoice numbers and find their IDs
    const invoiceNumbers = [...new Set(records.map(r => r.DOKLAD))];
    
    // This is a simplified approach - in practice, you'd want to track IDs more directly
    return invoiceNumbers.map((_, index) => index + 1); // Placeholder
  }

  private async markInvoicesAsExported(invoiceIds: number[]): Promise<void> {
    if (invoiceIds.length === 0) return;

    await db.invoice.updateMany({
      where: {
        id: { in: invoiceIds },
      },
      data: {
        omegaImported: true,
        omegaImportedAt: new Date(),
      },
    });

    logger.info(`Marked ${invoiceIds.length} invoices as exported to Omega`);
  }

  // Get export preview without marking as exported
  async getExportPreview(options: InvoiceExportOptions = {}): Promise<{
    invoiceCount: number;
    recordCount: number;
    totalAmount: number;
    dateRange: { from: Date | null; to: Date | null };
  }> {
    
    const tempOptions = { ...this.options, ...options };
    const originalOptions = this.options;
    this.options = tempOptions;

    try {
      const invoices = await this.getInvoicesToExport();
      
      let totalAmount = 0;
      let recordCount = 0;
      let minDate: Date | null = null;
      let maxDate: Date | null = null;

      for (const invoice of invoices) {
        totalAmount += invoice.totalAmount;
        recordCount += invoice.items.length;
        
        if (!minDate || invoice.createdDate < minDate) {
          minDate = invoice.createdDate;
        }
        if (!maxDate || invoice.createdDate > maxDate) {
          maxDate = invoice.createdDate;
        }
      }

      return {
        invoiceCount: invoices.length,
        recordCount,
        totalAmount,
        dateRange: {
          from: minDate,
          to: maxDate,
        },
      };
    } finally {
      this.options = originalOptions;
    }
  }
}

// Create singleton instance
export const invoiceOmegaGenerator = new InvoiceOmegaGenerator();

export default invoiceOmegaGenerator;
