import fs from 'fs/promises';
import path from 'path';
import iconv from 'iconv-lite';

import { config } from '@/config';
import type { OmegaDocumentType, VATRate } from '@/types/common';
import { OmegaExportError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import { ensureDirectoryExists, formatDate, sanitizeFileName } from '@/utils/helpers';

export interface OmegaRecord {
  [key: string]: string | number | Date | null | undefined;
}

export interface OmegaExportOptions {
  outputPath?: string;
  encoding?: string;
  delimiter?: string;
  includeHeader?: boolean;
  dateFormat?: 'DD.MM.YYYY' | 'YYYY-MM-DD';
}

export abstract class BaseOmegaGenerator {
  protected documentType: OmegaDocumentType;
  protected outputPath: string;
  protected encoding: string;
  protected delimiter: string;
  protected dateFormat: 'DD.MM.YYYY' | 'YYYY-MM-DD';

  constructor(
    documentType: OmegaDocumentType,
    options: OmegaExportOptions = {}
  ) {
    this.documentType = documentType;
    this.outputPath = options.outputPath || config.omega.importPath;
    this.encoding = options.encoding || 'win1250'; // Windows ANSI for Slovak characters
    this.delimiter = options.delimiter || '\t'; // TAB delimiter for Omega
    this.dateFormat = options.dateFormat || 'DD.MM.YYYY';
  }

  // Abstract method that each generator must implement
  abstract generateRecords(): Promise<OmegaRecord[]>;

  // Generate CSV file
  async generateCsv(fileName?: string): Promise<string> {
    try {
      // Ensure output directory exists
      await ensureDirectoryExists(this.outputPath);

      // Generate records
      const records = await this.generateRecords();
      
      if (records.length === 0) {
        throw new OmegaExportError('No records to export');
      }

      // Generate CSV content
      const csvContent = this.recordsToCsv(records);

      // Generate filename if not provided
      const outputFileName = fileName || this.generateFileName();
      const fullPath = path.join(this.outputPath, outputFileName);

      // Convert to target encoding and write file
      const encodedContent = iconv.encode(csvContent, this.encoding);
      await fs.writeFile(fullPath, encodedContent);

      logger.info('Omega CSV file generated', {
        fileName: outputFileName,
        recordCount: records.length,
        fileSize: encodedContent.length,
        encoding: this.encoding,
      });

      return fullPath;

    } catch (error) {
      logger.error('Omega CSV generation failed:', error);
      throw error instanceof OmegaExportError 
        ? error 
        : new OmegaExportError(`CSV generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Convert records to CSV format
  protected recordsToCsv(records: OmegaRecord[]): string {
    if (records.length === 0) {
      return '';
    }

    const lines: string[] = [];

    // Get all unique column names from all records
    const allColumns = new Set<string>();
    records.forEach(record => {
      Object.keys(record).forEach(key => allColumns.add(key));
    });
    const columns = Array.from(allColumns);

    // Add header if needed (usually not for Omega imports)
    // lines.push(columns.join(this.delimiter));

    // Add data rows
    for (const record of records) {
      const values = columns.map(column => this.formatValue(record[column]));
      lines.push(values.join(this.delimiter));
    }

    return lines.join('\r\n'); // Windows line endings
  }

  // Format value for CSV output
  protected formatValue(value: unknown): string {
    if (value === null || value === undefined) {
      return '';
    }

    if (value instanceof Date) {
      return formatDate(value, this.dateFormat);
    }

    if (typeof value === 'number') {
      // Format numbers with Slovak decimal separator (comma)
      return value.toString().replace('.', ',');
    }

    if (typeof value === 'string') {
      // Escape quotes and handle special characters
      let escaped = value.replace(/"/g, '""');
      
      // Quote if contains delimiter, quotes, or line breaks
      if (escaped.includes(this.delimiter) || escaped.includes('"') || escaped.includes('\n') || escaped.includes('\r')) {
        escaped = `"${escaped}"`;
      }
      
      return escaped;
    }

    return String(value);
  }

  // Generate filename based on document type and current date
  protected generateFileName(): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const docTypePrefix = this.getDocumentTypePrefix();
    return sanitizeFileName(`${docTypePrefix}_${timestamp}.csv`);
  }

  // Get document type prefix for filename
  protected getDocumentTypePrefix(): string {
    switch (this.documentType) {
      case OmegaDocumentType.CUSTOMER_INVOICE:
        return 'T01_Faktury';
      case OmegaDocumentType.PURCHASE_INVOICE:
        return 'T14_Dosle_Faktury';
      case OmegaDocumentType.DELIVERY_NOTE:
        return 'T02_Dodacie_Listy';
      case OmegaDocumentType.CREDIT_NOTE:
        return 'T04_Dobropisy';
      case OmegaDocumentType.SALES_RECEIPT:
        return 'T13_Predajky';
      default:
        return `T${this.documentType}_Export`;
    }
  }

  // Helper methods for common Omega formatting
  protected formatOmegaDate(date: Date): string {
    return formatDate(date, this.dateFormat);
  }

  protected formatOmegaAmount(amount: number): string {
    // Format with 2 decimal places and comma as decimal separator
    return amount.toFixed(2).replace('.', ',');
  }

  protected formatOmegaVatRate(vatRate: VATRate): string {
    // Return the VAT rate code as expected by Omega
    return vatRate;
  }

  protected formatOmegaPartnerCode(code: string): string {
    // Ensure partner code is max 20 characters and uppercase
    return code.substring(0, 20).toUpperCase();
  }

  protected formatOmegaText(text: string, maxLength: number = 75): string {
    // Clean and limit text length for Omega fields
    return text
      .replace(/[\r\n\t]/g, ' ') // Replace line breaks with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim()
      .substring(0, maxLength);
  }

  protected formatOmegaICO(ico: string): string {
    // Format Slovak IČO (8 digits)
    return ico.replace(/\D/g, '').padStart(8, '0').substring(0, 8);
  }

  protected formatOmegaDIC(dic: string): string {
    // Format Slovak DIČ (10 digits)
    return dic.replace(/\D/g, '').padStart(10, '0').substring(0, 10);
  }

  // Validation methods
  protected validateRecord(record: OmegaRecord, requiredFields: string[]): void {
    for (const field of requiredFields) {
      if (!record[field] && record[field] !== 0) {
        throw new OmegaExportError(`Missing required field: ${field}`);
      }
    }
  }

  protected validateAmount(amount: number): void {
    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new OmegaExportError(`Invalid amount: ${amount}`);
    }
  }

  protected validateDate(date: Date): void {
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      throw new OmegaExportError(`Invalid date: ${date}`);
    }
  }

  protected validateVatRate(vatRate: string): void {
    const validRates = ['0', 'N', 'V', 'X', 'Y'];
    if (!validRates.includes(vatRate)) {
      throw new OmegaExportError(`Invalid VAT rate: ${vatRate}. Must be one of: ${validRates.join(', ')}`);
    }
  }

  // Get export statistics
  async getExportStats(): Promise<{
    totalFiles: number;
    lastExportDate: Date | null;
    exportPath: string;
  }> {
    try {
      await ensureDirectoryExists(this.outputPath);
      const files = await fs.readdir(this.outputPath);
      const csvFiles = files.filter(file => file.endsWith('.csv'));
      
      let lastExportDate: Date | null = null;
      
      if (csvFiles.length > 0) {
        // Get the most recent file modification time
        const stats = await Promise.all(
          csvFiles.map(async file => {
            const filePath = path.join(this.outputPath, file);
            const stat = await fs.stat(filePath);
            return stat.mtime;
          })
        );
        
        lastExportDate = new Date(Math.max(...stats.map(date => date.getTime())));
      }

      return {
        totalFiles: csvFiles.length,
        lastExportDate,
        exportPath: this.outputPath,
      };
    } catch (error) {
      logger.error('Error getting export stats:', error);
      return {
        totalFiles: 0,
        lastExportDate: null,
        exportPath: this.outputPath,
      };
    }
  }

  // Clean up old export files
  async cleanupOldFiles(daysToKeep: number = 30): Promise<number> {
    try {
      await ensureDirectoryExists(this.outputPath);
      const files = await fs.readdir(this.outputPath);
      const csvFiles = files.filter(file => file.endsWith('.csv'));
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      let deletedCount = 0;
      
      for (const file of csvFiles) {
        const filePath = path.join(this.outputPath, file);
        const stat = await fs.stat(filePath);
        
        if (stat.mtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
          logger.info(`Deleted old export file: ${file}`);
        }
      }
      
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old files:', error);
      return 0;
    }
  }
}
