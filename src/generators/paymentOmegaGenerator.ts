import type { Payment, Invoice, BankTransaction, ClientMapping } from '@prisma/client';

import db from '@/database/client';
import { BaseOmegaGenerator, type OmegaRecord, type OmegaExportOptions } from './baseOmegaGenerator';
import { OmegaDocumentType } from '@/types/common';
import { OmegaExportError } from '@/utils/errors';
import { logger } from '@/utils/logger';

export interface PaymentExportOptions extends OmegaExportOptions {
  fromDate?: Date;
  toDate?: Date;
  paymentIds?: number[];
  invoiceIds?: number[];
  onlyUnexported?: boolean;
  includePartialPayments?: boolean;
}

export interface PaymentOmegaRecord extends OmegaRecord {
  // Payment document fields for Omega
  DOKLAD: string;           // Payment document number
  DATUM: string;            // Payment date
  PARTNER: string;          // Partner code
  NAZOV: string;            // Partner name
  FAKTURA: string;          // Related invoice number
  SUMA: string;             // Payment amount
  MENA: string;             // Currency
  KURZ: string;             // Exchange rate
  UCET: string;             // Bank account
  VS: string;               // Variable symbol
  SS: string;               // Specific symbol
  KS: string;               // Constant symbol
  POZNAMKA: string;         // Note/description
  TYP: string;              // Payment type
}

export class PaymentOmegaGenerator extends BaseOmegaGenerator {
  private options: PaymentExportOptions;

  constructor(options: PaymentExportOptions = {}) {
    super(OmegaDocumentType.SALES_RECEIPT, options); // Using sales receipt type for payments
    this.options = options;
  }

  async generateRecords(): Promise<PaymentOmegaRecord[]> {
    logger.info('Starting payment export to Omega format', {
      options: this.options,
    });

    try {
      // Get payments to export
      const payments = await this.getPaymentsToExport();
      
      if (payments.length === 0) {
        logger.warn('No payments found for export');
        return [];
      }

      logger.info(`Found ${payments.length} payments to export`);

      const records: PaymentOmegaRecord[] = [];

      // Process each payment
      for (const payment of payments) {
        try {
          const paymentRecord = await this.generatePaymentRecord(payment);
          records.push(paymentRecord);
        } catch (error) {
          logger.error(`Error processing payment ${payment.id}:`, error);
          throw new OmegaExportError(
            `Failed to process payment ${payment.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }

      logger.info(`Generated ${records.length} Omega payment records`);
      return records;

    } catch (error) {
      logger.error('Payment export failed:', error);
      throw error instanceof OmegaExportError 
        ? error 
        : new OmegaExportError(`Payment export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getPaymentsToExport(): Promise<Array<Payment & {
    invoice: Invoice & {
      clientMapping: ClientMapping | null;
    };
    bankTransaction: BankTransaction | null;
  }>> {
    const where: any = {};

    // Date range filter
    if (this.options.fromDate || this.options.toDate) {
      where.paymentDate = {};
      if (this.options.fromDate) {
        where.paymentDate.gte = this.options.fromDate;
      }
      if (this.options.toDate) {
        where.paymentDate.lte = this.options.toDate;
      }
    }

    // Specific payment IDs
    if (this.options.paymentIds && this.options.paymentIds.length > 0) {
      where.id = { in: this.options.paymentIds };
    }

    // Specific invoice IDs
    if (this.options.invoiceIds && this.options.invoiceIds.length > 0) {
      where.invoiceId = { in: this.options.invoiceIds };
    }

    // Only unexported payments
    if (this.options.onlyUnexported) {
      where.omegaExported = false;
    }

    // Include/exclude partial payments
    if (!this.options.includePartialPayments) {
      // Only include payments where the invoice is fully paid
      where.invoice = {
        status: 'paid',
      };
    }

    return db.payment.findMany({
      where,
      include: {
        invoice: {
          include: {
            clientMapping: true,
          },
        },
        bankTransaction: true,
      },
      orderBy: {
        paymentDate: 'asc',
      },
    });
  }

  private async generatePaymentRecord(
    payment: Payment & {
      invoice: Invoice & {
        clientMapping: ClientMapping | null;
      };
      bankTransaction: BankTransaction | null;
    }
  ): Promise<PaymentOmegaRecord> {
    
    // Validate payment
    this.validatePayment(payment);

    const invoice = payment.invoice;
    const bankTransaction = payment.bankTransaction;
    const clientMapping = invoice.clientMapping;

    if (!clientMapping) {
      throw new OmegaExportError(`No client mapping found for payment ${payment.id}`);
    }

    // Generate payment document number
    const paymentDocNumber = this.generatePaymentDocumentNumber(payment);

    return {
      DOKLAD: this.formatOmegaText(paymentDocNumber, 20),
      DATUM: this.formatOmegaDate(payment.paymentDate),
      PARTNER: this.formatOmegaPartnerCode(clientMapping.omegaPartnerName || `CLIENT_${invoice.clientId}`),
      NAZOV: this.formatOmegaText(clientMapping.omegaPartnerName || 'Unknown Client', 75),
      FAKTURA: this.formatOmegaText(invoice.invoiceNumber, 20),
      SUMA: this.formatOmegaAmount(payment.amount),
      MENA: 'EUR', // Default currency
      KURZ: '1,00', // Default exchange rate
      UCET: this.formatBankAccount(bankTransaction),
      VS: bankTransaction?.variableSymbol || invoice.invoiceNumber,
      SS: bankTransaction?.specificSymbol || '',
      KS: bankTransaction?.constantSymbol || '',
      POZNAMKA: this.generatePaymentNote(payment, bankTransaction),
      TYP: this.formatPaymentType(payment.paymentType),
    };
  }

  private generatePaymentDocumentNumber(payment: Payment): string {
    // Generate unique payment document number
    const date = payment.paymentDate;
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const id = payment.id.toString().padStart(4, '0');
    
    return `P${year}${month}${day}${id}`;
  }

  private formatBankAccount(bankTransaction: BankTransaction | null): string {
    if (!bankTransaction) return '';
    
    // Try to extract bank account from counterparty account or use a default
    if (bankTransaction.counterpartyAccount) {
      return this.formatOmegaText(bankTransaction.counterpartyAccount, 30);
    }
    
    return 'BANK_TRANSFER';
  }

  private generatePaymentNote(
    payment: Payment,
    bankTransaction: BankTransaction | null
  ): string {
    const parts: string[] = [];
    
    parts.push(`Payment for invoice ${payment.invoice.invoiceNumber}`);
    
    if (bankTransaction) {
      if (bankTransaction.counterpartyName) {
        parts.push(`From: ${bankTransaction.counterpartyName}`);
      }
      
      if (bankTransaction.description) {
        parts.push(bankTransaction.description);
      }
    }
    
    return this.formatOmegaText(parts.join(' | '), 75);
  }

  private formatPaymentType(paymentType: string): string {
    // Map payment types to Omega codes
    const typeMapping: Record<string, string> = {
      'bank_transfer': 'BANK',
      'cash': 'HOTOVOST',
      'card': 'KARTA',
      'check': 'CEK',
      'other': 'INE',
    };
    
    return typeMapping[paymentType] || 'BANK';
  }

  private validatePayment(payment: Payment): void {
    if (!payment.paymentDate) {
      throw new OmegaExportError(`Payment ${payment.id} missing payment date`);
    }

    if (!payment.amount || payment.amount <= 0) {
      throw new OmegaExportError(`Payment ${payment.id} has invalid amount`);
    }

    if (!payment.invoice) {
      throw new OmegaExportError(`Payment ${payment.id} missing related invoice`);
    }

    this.validateDate(payment.paymentDate);
    this.validateAmount(payment.amount);
  }

  // Export payments and mark as exported
  async exportPayments(options: PaymentExportOptions = {}): Promise<{
    filePath: string;
    paymentCount: number;
    totalAmount: number;
  }> {
    
    // Merge options
    this.options = { ...this.options, ...options };

    // Generate CSV
    const filePath = await this.generateCsv();
    
    // Get the records that were exported
    const records = await this.generateRecords();
    const paymentIds = this.extractPaymentIds(records);

    // Calculate total amount
    const totalAmount = records.reduce((sum, record) => {
      return sum + parseFloat(record.SUMA.replace(',', '.'));
    }, 0);

    // Mark payments as exported to Omega
    if (!options.onlyUnexported) { // Don't mark if this was just a preview
      await this.markPaymentsAsExported(paymentIds);
    }

    logger.info('Payment export completed', {
      filePath,
      paymentCount: paymentIds.length,
      totalAmount,
    });

    return {
      filePath,
      paymentCount: paymentIds.length,
      totalAmount,
    };
  }

  private extractPaymentIds(records: PaymentOmegaRecord[]): number[] {
    // Extract payment IDs from document numbers
    // This is a simplified approach - in practice, you'd want to track IDs more directly
    return records.map((_, index) => index + 1); // Placeholder
  }

  private async markPaymentsAsExported(paymentIds: number[]): Promise<void> {
    if (paymentIds.length === 0) return;

    await db.payment.updateMany({
      where: {
        id: { in: paymentIds },
      },
      data: {
        omegaExported: true,
        omegaExportedAt: new Date(),
      },
    });

    logger.info(`Marked ${paymentIds.length} payments as exported to Omega`);
  }

  // Get export preview without marking as exported
  async getExportPreview(options: PaymentExportOptions = {}): Promise<{
    paymentCount: number;
    totalAmount: number;
    dateRange: { from: Date | null; to: Date | null };
    invoiceCount: number;
  }> {
    
    const tempOptions = { ...this.options, ...options };
    const originalOptions = this.options;
    this.options = tempOptions;

    try {
      const payments = await this.getPaymentsToExport();
      
      let totalAmount = 0;
      let minDate: Date | null = null;
      let maxDate: Date | null = null;
      const uniqueInvoiceIds = new Set<number>();

      for (const payment of payments) {
        totalAmount += payment.amount;
        uniqueInvoiceIds.add(payment.invoiceId);
        
        if (!minDate || payment.paymentDate < minDate) {
          minDate = payment.paymentDate;
        }
        if (!maxDate || payment.paymentDate > maxDate) {
          maxDate = payment.paymentDate;
        }
      }

      return {
        paymentCount: payments.length,
        totalAmount,
        dateRange: {
          from: minDate,
          to: maxDate,
        },
        invoiceCount: uniqueInvoiceIds.size,
      };
    } finally {
      this.options = originalOptions;
    }
  }

  // Get unmatched bank transactions that could be exported as payments
  async getUnmatchedTransactions(): Promise<Array<{
    transactionId: number;
    amount: number;
    date: Date;
    counterparty: string | null;
    variableSymbol: string | null;
    description: string | null;
  }>> {
    
    const transactions = await db.bankTransaction.findMany({
      where: {
        debitCredit: 'CRDT',
        matchedInvoiceId: null,
      },
      orderBy: {
        transactionDate: 'desc',
      },
      take: 100, // Limit to recent transactions
    });

    return transactions.map(tx => ({
      transactionId: tx.id,
      amount: tx.amount,
      date: tx.transactionDate,
      counterparty: tx.counterpartyName,
      variableSymbol: tx.variableSymbol,
      description: tx.description,
    }));
  }
}

// Create singleton instance
export const paymentOmegaGenerator = new PaymentOmegaGenerator();

export default paymentOmegaGenerator;
