import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  
  // Database
  DATABASE_URL: z.string().default('file:./dev.db'),
  
  // UISP CRM API
  UISP_API_URL: z.string().optional(),
  UISP_API_KEY: z.string().optional(),
  
  // Omega
  OMEGA_IMPORT_PATH: z.string().default('./omega-imports'),
  
  // Security
  JWT_SECRET: z.string().default('your-super-secret-jwt-key-change-in-production'),
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  
  // Rate limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // File upload
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  UPLOAD_PATH: z.string().default('./uploads'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
  
  // Scheduler
  SYNC_INTERVAL_MINUTES: z.string().transform(Number).default('30'),
  AUTO_SYNC_ENABLED: z.string().transform(val => val === 'true').default('false'),
  
  // Email notifications (optional)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  NOTIFICATION_EMAIL: z.string().email().optional(),
});

// Validate environment variables
const env = envSchema.parse(process.env);

export const config = {
  // Server
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  isTest: env.NODE_ENV === 'test',
  
  // Database
  database: {
    url: env.DATABASE_URL,
  },
  
  // UISP CRM
  uisp: {
    apiUrl: env.UISP_API_URL,
    apiKey: env.UISP_API_KEY,
  },
  
  // Omega
  omega: {
    importPath: env.OMEGA_IMPORT_PATH,
  },
  
  // Security
  security: {
    jwtSecret: env.JWT_SECRET,
    bcryptRounds: env.BCRYPT_ROUNDS,
  },
  
  // Rate limiting
  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },
  
  // File upload
  upload: {
    maxFileSize: env.MAX_FILE_SIZE,
    uploadPath: env.UPLOAD_PATH,
  },
  
  // Logging
  logging: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
  },
  
  // Scheduler
  scheduler: {
    syncIntervalMinutes: env.SYNC_INTERVAL_MINUTES,
    autoSyncEnabled: env.AUTO_SYNC_ENABLED,
  },
  
  // Email
  email: {
    smtp: {
      host: env.SMTP_HOST,
      port: env.SMTP_PORT,
      user: env.SMTP_USER,
      pass: env.SMTP_PASS,
    },
    notificationEmail: env.NOTIFICATION_EMAIL,
  },
} as const;

export type Config = typeof config;
