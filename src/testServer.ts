import express from 'express';
import cors from 'cors';

const app = express();

// Basic middleware
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Test bank routes
app.get('/api/bank/test', (req, res) => {
  console.log('Test bank endpoint called!');
  res.json({
    success: true,
    message: 'Bank API test endpoint is working!',
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/bank/statements', (req, res) => {
  console.log('Bank statements endpoint called!');
  res.json({
    success: true,
    data: [],
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/bank/transactions', (req, res) => {
  console.log('Bank transactions endpoint called!');
  res.json({
    success: true,
    data: [],
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/bank/supported-types', (req, res) => {
  console.log('Supported types endpoint called!');
  res.json({
    success: true,
    data: [
      {
        type: 'XML',
        extensions: ['.xml'],
        description: 'CAMT.053 štandard'
      },
      {
        type: 'XLS',
        extensions: ['.xls', '.xlsx'],
        description: 'Excel súbory'
      },
      {
        type: 'CSV',
        extensions: ['.csv'],
        description: 'Textové súbory'
      }
    ],
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/bank/stats', (req, res) => {
  console.log('Bank stats endpoint called!');
  res.json({
    success: true,
    data: {
      totalStatements: 0,
      totalTransactions: 0,
      processedToday: 0
    },
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/bank/upload', (req, res) => {
  console.log('Bank upload endpoint called!');
  res.json({
    success: true,
    message: 'File upload would be processed here',
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/bank/preview', (req, res) => {
  console.log('Bank preview endpoint called!');
  res.json({
    success: true,
    data: {
      preview: 'File preview would be shown here'
    },
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/bank/validate', (req, res) => {
  console.log('Bank validate endpoint called!');
  res.json({
    success: true,
    data: {
      valid: true,
      errors: []
    },
    timestamp: new Date().toISOString(),
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Test server is running!',
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint nebol nájdený',
    message: `Cesta ${req.originalUrl} neexistuje`,
    timestamp: new Date().toISOString(),
  });
});

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Test server beží na porte ${PORT}`);
  console.log(`📊 Test health: http://localhost:${PORT}/api/health`);
  console.log(`🏦 Test bank: http://localhost:${PORT}/api/bank/test`);
});
