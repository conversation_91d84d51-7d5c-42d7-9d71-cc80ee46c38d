import express from 'express';
import cors from 'cors';

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Test bank routes
app.get('/api/bank/test', (req, res) => {
  console.log('Test bank endpoint called!');
  res.json({
    success: true,
    message: 'Bank API test endpoint is working!',
    timestamp: new Date().toISOString(),
  });
});

app.get('/api/bank/statements', (req, res) => {
  console.log('Bank statements endpoint called!');
  res.json({
    success: true,
    data: {
      statements: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        pages: 0,
      },
    },
    timestamp: new Date().toISOString(),
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Test server is running!',
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint nebol nájdený',
    message: `Cesta ${req.originalUrl} neexistuje`,
    timestamp: new Date().toISOString(),
  });
});

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Test server beží na porte ${PORT}`);
  console.log(`📊 Test health: http://localhost:${PORT}/api/health`);
  console.log(`🏦 Test bank: http://localhost:${PORT}/api/bank/test`);
});
