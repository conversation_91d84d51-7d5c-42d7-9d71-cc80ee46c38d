import type { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

import { config } from '@/config';
import type { ApiResponse } from '@/types/common';
import { UnauthorizedError, ForbiddenError } from '@/utils/errors';
import { logger } from '@/utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        sessionId: string;
      };
    }
  }
}

// JWT payload interface
interface JwtPayload {
  userId: string;
  role: string;
  sessionId: string;
  iat: number;
  exp: number;
}

// Authentication middleware
export const authenticate = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedError('No token provided');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = jwt.verify(token, config.security.jwtSecret) as JwtPayload;

    // Add user info to request
    req.user = {
      id: decoded.userId,
      role: decoded.role,
      sessionId: decoded.sessionId,
    };

    logger.debug('User authenticated', {
      userId: req.user.id,
      role: req.user.role,
      ip: req.ip,
    });

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid token',
        timestamp: new Date().toISOString(),
      };
      return res.status(401).json(response);
    }

    if (error instanceof jwt.TokenExpiredError) {
      const response: ApiResponse = {
        success: false,
        error: 'Token expired',
        timestamp: new Date().toISOString(),
      };
      return res.status(401).json(response);
    }

    next(error);
  }
};

// Optional authentication middleware (for public endpoints that can benefit from user context)
export const optionalAuthenticate = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, config.security.jwtSecret) as JwtPayload;
      
      req.user = {
        id: decoded.userId,
        role: decoded.role,
        sessionId: decoded.sessionId,
      };
    }
    
    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

// Authorization middleware factory
export const authorize = (roles: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new UnauthorizedError('Authentication required');
    }

    if (roles.length > 0 && !roles.includes(req.user.role)) {
      throw new ForbiddenError('Insufficient permissions');
    }

    next();
  };
};

// API key authentication middleware (for external integrations)
export const authenticateApiKey = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    const response: ApiResponse = {
      success: false,
      error: 'API key required',
      timestamp: new Date().toISOString(),
    };
    return res.status(401).json(response);
  }

  // For now, we'll use a simple API key validation
  // In production, this should be stored securely and hashed
  const validApiKeys = [
    config.uisp.apiKey, // Allow UISP API key for webhook endpoints
    process.env.INTERNAL_API_KEY, // Internal API key for system operations
  ].filter(Boolean);

  if (!validApiKeys.includes(apiKey)) {
    const response: ApiResponse = {
      success: false,
      error: 'Invalid API key',
      timestamp: new Date().toISOString(),
    };
    return res.status(401).json(response);
  }

  logger.debug('API key authenticated', {
    keyPrefix: apiKey.substring(0, 8) + '...',
    ip: req.ip,
  });

  next();
};

// Rate limiting by user
export const rateLimitByUser = (
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const userId = req.user?.id || req.ip;
    const now = Date.now();

    // Clean up expired entries
    for (const [key, value] of userRequests.entries()) {
      if (now > value.resetTime) {
        userRequests.delete(key);
      }
    }

    // Get or create user request data
    let userRequestData = userRequests.get(userId);
    if (!userRequestData || now > userRequestData.resetTime) {
      userRequestData = {
        count: 0,
        resetTime: now + windowMs,
      };
      userRequests.set(userId, userRequestData);
    }

    // Check rate limit
    if (userRequestData.count >= maxRequests) {
      const response: ApiResponse = {
        success: false,
        error: 'Rate limit exceeded',
        message: `Maximum ${maxRequests} requests per ${windowMs / 1000} seconds`,
        timestamp: new Date().toISOString(),
      };

      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': Math.ceil(userRequestData.resetTime / 1000).toString(),
      });

      return res.status(429).json(response);
    }

    // Increment request count
    userRequestData.count++;

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': (maxRequests - userRequestData.count).toString(),
      'X-RateLimit-Reset': Math.ceil(userRequestData.resetTime / 1000).toString(),
    });

    next();
  };
};

// Session validation middleware
export const validateSession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.user?.sessionId) {
    return next();
  }

  try {
    // In a real application, you would check the session in the database
    // For now, we'll just validate that the session ID exists
    // const session = await db.session.findUnique({
    //   where: { id: req.user.sessionId },
    // });

    // if (!session || session.expiresAt < new Date()) {
    //   throw new UnauthorizedError('Session expired');
    // }

    next();
  } catch (error) {
    next(error);
  }
};

// Generate JWT token
export const generateToken = (payload: {
  userId: string;
  role: string;
  sessionId: string;
}): string => {
  return jwt.sign(payload, config.security.jwtSecret, {
    expiresIn: '24h',
    issuer: 'uisp-omega-bridge',
    audience: 'uisp-omega-bridge-users',
  });
};

// Verify JWT token
export const verifyToken = (token: string): JwtPayload => {
  return jwt.verify(token, config.security.jwtSecret) as JwtPayload;
};

// Extract user ID from request
export const getUserId = (req: Request): string | null => {
  return req.user?.id || null;
};

// Check if user has role
export const hasRole = (req: Request, role: string): boolean => {
  return req.user?.role === role;
};

// Check if user has any of the specified roles
export const hasAnyRole = (req: Request, roles: string[]): boolean => {
  return req.user ? roles.includes(req.user.role) : false;
};
