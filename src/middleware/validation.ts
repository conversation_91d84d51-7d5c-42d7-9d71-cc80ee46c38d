import type { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

import type { ApiResponse } from '@/types/common';
import { ValidationError } from '@/utils/errors';

// Validation middleware factory
export const validate = (schema: {
  body?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
}) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: Array<{ field: string; message: string; value?: unknown }> = [];

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body, { abortEarly: false });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: `body.${detail.path.join('.')}`,
          message: detail.message,
          value: detail.context?.value,
        })));
      }
    }

    // Validate query parameters
    if (schema.query) {
      const { error } = schema.query.validate(req.query, { abortEarly: false });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: `query.${detail.path.join('.')}`,
          message: detail.message,
          value: detail.context?.value,
        })));
      }
    }

    // Validate route parameters
    if (schema.params) {
      const { error } = schema.params.validate(req.params, { abortEarly: false });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: `params.${detail.path.join('.')}`,
          message: detail.message,
          value: detail.context?.value,
        })));
      }
    }

    // If there are validation errors, return them
    if (errors.length > 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        message: 'Please check your input data',
        timestamp: new Date().toISOString(),
        details: {
          validationErrors: errors,
        },
      };

      return res.status(400).json(response);
    }

    next();
  };
};

// Common validation schemas
export const commonSchemas = {
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  // Date range
  dateRange: Joi.object({
    from: Joi.date().iso().optional(),
    to: Joi.date().iso().min(Joi.ref('from')).optional(),
  }),

  // ID parameter
  id: Joi.object({
    id: Joi.number().integer().positive().required(),
  }),

  // Search
  search: Joi.object({
    search: Joi.string().min(1).max(255).optional(),
  }),

  // File upload
  fileUpload: Joi.object({
    type: Joi.string().valid('XML', 'SWIFT', 'XLS').required(),
  }),
};

// Invoice validation schemas
export const invoiceSchemas = {
  list: Joi.object({
    ...commonSchemas.pagination.describe().keys,
    ...commonSchemas.search.describe().keys,
    ...commonSchemas.dateRange.describe().keys,
    status: Joi.string().valid('draft', 'sent', 'paid', 'overdue', 'void').optional(),
    clientId: Joi.number().integer().positive().optional(),
    omegaImported: Joi.boolean().optional(),
  }),

  sync: Joi.object({
    fromDate: Joi.date().iso().optional(),
    force: Joi.boolean().default(false),
  }),

  manualCreate: Joi.object({
    uispInvoiceId: Joi.number().integer().positive().required(),
    invoiceNumber: Joi.string().min(1).max(50).required(),
    clientId: Joi.number().integer().positive().required(),
    totalAmount: Joi.number().positive().required(),
    currency: Joi.string().length(3).default('EUR'),
    createdDate: Joi.date().iso().required(),
    dueDate: Joi.date().iso().min(Joi.ref('createdDate')).required(),
    taxableSupplyDate: Joi.date().iso().optional(),
    status: Joi.string().valid('draft', 'sent', 'paid', 'overdue', 'void').required(),
    items: Joi.array().items(
      Joi.object({
        label: Joi.string().min(1).max(200).required(),
        quantity: Joi.number().positive().required(),
        unitPrice: Joi.number().positive().required(),
        vatRate: Joi.string().valid('0', 'N', 'V', 'X', 'Y').required(),
      })
    ).min(1).required(),
  }),
};

// Bank transaction validation schemas
export const bankSchemas = {
  list: Joi.object({
    ...commonSchemas.pagination.describe().keys,
    ...commonSchemas.search.describe().keys,
    ...commonSchemas.dateRange.describe().keys,
    statementId: Joi.number().integer().positive().optional(),
    matched: Joi.boolean().optional(),
    debitCredit: Joi.string().valid('DBIT', 'CRDT').optional(),
    minAmount: Joi.number().optional(),
    maxAmount: Joi.number().min(Joi.ref('minAmount')).optional(),
  }),

  import: Joi.object({
    type: Joi.string().valid('XML', 'SWIFT', 'XLS').required(),
  }),

  manualMatch: Joi.object({
    transactionId: Joi.number().integer().positive().required(),
    invoiceId: Joi.number().integer().positive().required(),
    amount: Joi.number().positive().optional(),
  }),
};

// Payment validation schemas
export const paymentSchemas = {
  list: Joi.object({
    ...commonSchemas.pagination.describe().keys,
    ...commonSchemas.search.describe().keys,
    ...commonSchemas.dateRange.describe().keys,
    invoiceId: Joi.number().integer().positive().optional(),
    paymentType: Joi.string().valid('bank_transfer', 'cash', 'card').optional(),
    omegaImported: Joi.boolean().optional(),
  }),

  match: Joi.object({
    statementId: Joi.number().integer().positive().optional(),
    force: Joi.boolean().default(false),
  }),

  manualCreate: Joi.object({
    invoiceId: Joi.number().integer().positive().required(),
    bankTransactionId: Joi.number().integer().positive().optional(),
    amount: Joi.number().positive().required(),
    paymentDate: Joi.date().iso().required(),
    paymentType: Joi.string().valid('bank_transfer', 'cash', 'card').required(),
  }),
};

// Client mapping validation schemas
export const clientMappingSchemas = {
  list: Joi.object({
    ...commonSchemas.pagination.describe().keys,
    ...commonSchemas.search.describe().keys,
    isActive: Joi.boolean().optional(),
  }),

  create: Joi.object({
    uispClientId: Joi.number().integer().positive().required(),
    omegaPartnerCode: Joi.string().min(1).max(20).optional(),
    omegaPartnerName: Joi.string().min(1).max(75).optional(),
    omegaPartnerIco: Joi.string().pattern(/^\d{8}$/).optional(),
    omegaPartnerDic: Joi.string().pattern(/^\d{10}$/).optional(),
  }),

  update: Joi.object({
    omegaPartnerCode: Joi.string().min(1).max(20).optional(),
    omegaPartnerName: Joi.string().min(1).max(75).optional(),
    omegaPartnerIco: Joi.string().pattern(/^\d{8}$/).optional(),
    omegaPartnerDic: Joi.string().pattern(/^\d{10}$/).optional(),
    isActive: Joi.boolean().optional(),
  }),
};

// Configuration validation schemas
export const configSchemas = {
  update: Joi.object({
    uispApiUrl: Joi.string().uri().optional(),
    uispApiKey: Joi.string().min(1).optional(),
    omegaImportPath: Joi.string().min(1).optional(),
    autoSyncEnabled: Joi.boolean().optional(),
    syncIntervalMinutes: Joi.number().integer().min(1).max(1440).optional(),
  }),
};

// Omega export validation schemas
export const omegaSchemas = {
  export: Joi.object({
    type: Joi.string().valid('invoices', 'payments', 'all').default('all'),
    fromDate: Joi.date().iso().optional(),
    toDate: Joi.date().iso().min(Joi.ref('fromDate')).optional(),
    force: Joi.boolean().default(false),
  }),
};
