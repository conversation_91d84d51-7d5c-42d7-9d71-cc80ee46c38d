import type { Request, Response, NextFunction } from 'express';

import { config } from '@/config';
import type { ApiResponse } from '@/types/common';
import {
  isAppError,
  getErrorMessage,
  getErrorStatusCode,
  isOperationalError,
} from '@/utils/errors';
import { logger } from '@/utils/logger';

// Error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the error
  logger.error('Request error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Determine status code
  const statusCode = getErrorStatusCode(error);
  
  // Determine if error details should be exposed
  const shouldExposeDetails = config.isDevelopment || isOperationalError(error);
  
  // Create error response
  const errorResponse: ApiResponse = {
    success: false,
    error: shouldExposeDetails ? getErrorMessage(error) : 'Internal server error',
    timestamp: new Date().toISOString(),
  };

  // Add additional error details in development
  if (config.isDevelopment) {
    errorResponse.details = {
      stack: error.stack,
      name: error.name,
    };
  }

  // Add validation details for app errors
  if (isAppError(error) && shouldExposeDetails) {
    errorResponse.details = {
      ...errorResponse.details,
      statusCode: error.statusCode,
      isOperational: error.isOperational,
    };
  }

  res.status(statusCode).json(errorResponse);
};

// 404 handler
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = new Error(`Route ${req.originalUrl} not found`);
  (error as any).statusCode = 404;
  next(error);
};

// Async error wrapper
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Validation error handler
export const validationErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (error.name === 'ValidationError' || error.details) {
    const validationErrors = error.details?.map((detail: any) => ({
      field: detail.path?.join('.') || detail.context?.key,
      message: detail.message,
      value: detail.context?.value,
    })) || [];

    const response: ApiResponse = {
      success: false,
      error: 'Validation failed',
      message: 'Please check your input data',
      timestamp: new Date().toISOString(),
      details: {
        validationErrors,
      },
    };

    return res.status(400).json(response);
  }

  next(error);
};

// Database error handler
export const databaseErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Prisma errors
  if (error.code && error.code.startsWith('P')) {
    let message = 'Database operation failed';
    let statusCode = 500;

    switch (error.code) {
      case 'P2002':
        message = 'A record with this data already exists';
        statusCode = 409;
        break;
      case 'P2025':
        message = 'Record not found';
        statusCode = 404;
        break;
      case 'P2003':
        message = 'Foreign key constraint failed';
        statusCode = 400;
        break;
      case 'P2014':
        message = 'Invalid data provided';
        statusCode = 400;
        break;
    }

    const response: ApiResponse = {
      success: false,
      error: message,
      timestamp: new Date().toISOString(),
    };

    if (config.isDevelopment) {
      response.details = {
        code: error.code,
        meta: error.meta,
      };
    }

    return res.status(statusCode).json(response);
  }

  next(error);
};

// Rate limit error handler
export const rateLimitErrorHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const response: ApiResponse = {
    success: false,
    error: 'Too many requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  };

  res.status(429).json(response);
};

// CORS error handler
export const corsErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (error.message && error.message.includes('CORS')) {
    const response: ApiResponse = {
      success: false,
      error: 'CORS policy violation',
      message: 'Cross-origin request not allowed',
      timestamp: new Date().toISOString(),
    };

    return res.status(403).json(response);
  }

  next(error);
};

// Multer error handler (file upload)
export const multerErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    const response: ApiResponse = {
      success: false,
      error: 'File too large',
      message: `Maximum file size is ${config.upload.maxFileSize} bytes`,
      timestamp: new Date().toISOString(),
    };

    return res.status(413).json(response);
  }

  if (error.code === 'LIMIT_FILE_COUNT') {
    const response: ApiResponse = {
      success: false,
      error: 'Too many files',
      message: 'Maximum number of files exceeded',
      timestamp: new Date().toISOString(),
    };

    return res.status(400).json(response);
  }

  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    const response: ApiResponse = {
      success: false,
      error: 'Unexpected file field',
      message: 'File uploaded to unexpected field',
      timestamp: new Date().toISOString(),
    };

    return res.status(400).json(response);
  }

  next(error);
};
