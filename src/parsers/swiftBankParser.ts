import type { BankStatementParseResult, SwiftMT940Statement, SwiftBalance, SwiftStatementLine } from '@/types/bank';
import { BaseBankParser } from './baseBankParser';
import { BankParsingError } from '@/utils/errors';

export class SwiftBankParser extends BaseBankParser {
  constructor(fileName: string) {
    super(fileName, 'SWIFT');
  }

  async parse(content: string | Buffer): Promise<BankStatementParseResult> {
    this.logParsingProgress('Starting SWIFT MT940 parsing');

    try {
      const swiftContent = content instanceof Buffer ? content.toString('utf-8') : content;
      
      // Parse SWIFT MT940 format
      const statement = this.parseSwiftStatement(swiftContent);
      
      this.logParsingProgress('SWIFT statement parsed', {
        accountId: statement.accountIdentification,
        statementNumber: statement.statementNumber,
        transactionCount: statement.statementLines.length,
      });

      // Convert to our format
      const result = this.convertToStandardFormat(statement);
      
      this.validateParseResult(result);
      this.logParsingProgress('SWIFT parsing completed', {
        transactionCount: result.transactions.length,
      });

      return result;

    } catch (error) {
      if (error instanceof BankParsingError) {
        throw error;
      }
      
      throw new BankParsingError(
        `SWIFT parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.fileName
      );
    }
  }

  private parseSwiftStatement(content: string): SwiftMT940Statement {
    const lines = content.split(/\r?\n/).filter(line => line.trim());
    
    let transactionReferenceNumber = '';
    let accountIdentification = '';
    let statementNumber = '';
    let openingBalance: SwiftBalance | null = null;
    let closingBalance: SwiftBalance | null = null;
    let closingAvailableBalance: SwiftBalance | null = null;
    const statementLines: SwiftStatementLine[] = [];
    const informationLines: string[] = [];

    let currentStatementLine: Partial<SwiftStatementLine> | null = null;
    let currentInformationLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (!line) continue;

      try {
        // Parse different SWIFT tags
        if (line.startsWith(':20:')) {
          // Transaction Reference Number
          transactionReferenceNumber = line.substring(4);
          
        } else if (line.startsWith(':25:')) {
          // Account Identification
          accountIdentification = line.substring(4);
          
        } else if (line.startsWith(':28C:')) {
          // Statement Number
          statementNumber = line.substring(5);
          
        } else if (line.startsWith(':60F:') || line.startsWith(':60M:')) {
          // Opening Balance
          openingBalance = this.parseBalance(line.substring(5));
          
        } else if (line.startsWith(':62F:') || line.startsWith(':62M:')) {
          // Closing Balance
          closingBalance = this.parseBalance(line.substring(5));
          
        } else if (line.startsWith(':64:')) {
          // Closing Available Balance
          closingAvailableBalance = this.parseBalance(line.substring(4));
          
        } else if (line.startsWith(':61:')) {
          // Statement Line
          if (currentStatementLine) {
            // Finalize previous statement line
            currentStatementLine.informationToAccountOwner = currentInformationLines;
            statementLines.push(currentStatementLine as SwiftStatementLine);
            currentInformationLines = [];
          }
          
          currentStatementLine = this.parseStatementLine(line.substring(4));
          
        } else if (line.startsWith(':86:')) {
          // Information to Account Owner
          const info = line.substring(4);
          currentInformationLines.push(info);
          
        } else if (currentStatementLine && !line.startsWith(':')) {
          // Continuation of previous field
          currentInformationLines.push(line);
        }
        
      } catch (error) {
        this.logParsingProgress('Error parsing SWIFT line', { 
          lineNumber: i + 1, 
          line, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    // Finalize last statement line
    if (currentStatementLine) {
      currentStatementLine.informationToAccountOwner = currentInformationLines;
      statementLines.push(currentStatementLine as SwiftStatementLine);
    }

    // Validate required fields
    if (!accountIdentification) {
      throw new BankParsingError('Missing account identification (:25:)', this.fileName);
    }

    if (!openingBalance) {
      throw new BankParsingError('Missing opening balance (:60F: or :60M:)', this.fileName);
    }

    if (!closingBalance) {
      throw new BankParsingError('Missing closing balance (:62F: or :62M:)', this.fileName);
    }

    return {
      transactionReferenceNumber,
      accountIdentification,
      statementNumber,
      openingBalance,
      statementLines,
      closingBalance,
      closingAvailableBalance,
      informationToAccountOwner: informationLines,
    };
  }

  private parseBalance(balanceStr: string): SwiftBalance {
    // Format: [C|D]YYMMDDCCCNNNNNNNNN
    // C/D = Credit/Debit mark
    // YYMMDD = Date
    // CCC = Currency
    // NNNNNNNNN = Amount
    
    if (balanceStr.length < 10) {
      throw new BankParsingError(`Invalid balance format: ${balanceStr}`, this.fileName);
    }

    const debitCreditMark = balanceStr[0] as 'C' | 'D';
    const dateStr = balanceStr.substring(1, 7); // YYMMDD
    const currencyAndAmount = balanceStr.substring(7);
    
    // Extract currency (first 3 characters)
    const currency = currencyAndAmount.substring(0, 3);
    const amountStr = currencyAndAmount.substring(3);

    // Parse date
    const year = 2000 + parseInt(dateStr.substring(0, 2));
    const month = parseInt(dateStr.substring(2, 4));
    const day = parseInt(dateStr.substring(4, 6));
    const date = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

    // Parse amount (remove commas and convert to number)
    const amount = this.validateAmount(amountStr.replace(',', '.'));

    return {
      debitCreditMark,
      date,
      currency,
      amount,
    };
  }

  private parseStatementLine(lineStr: string): Partial<SwiftStatementLine> {
    // Format: YYMMDD[MMDD][C|D][F]NNNNNNNNN[NXXXXX][//XXXXXXXXXXXXXXXX][CRLF34x]
    
    let pos = 0;
    
    // Value date (YYMMDD)
    const valueDateStr = lineStr.substring(pos, pos + 6);
    pos += 6;
    
    // Entry date (optional MMDD)
    let entryDate: string | undefined;
    if (lineStr.length > pos + 4 && /^\d{4}/.test(lineStr.substring(pos, pos + 4))) {
      entryDate = lineStr.substring(pos, pos + 4);
      pos += 4;
    }
    
    // Debit/Credit mark
    const debitCreditMark = lineStr[pos] as 'C' | 'D';
    pos += 1;
    
    // Funds code (optional)
    let fundsCode: string | undefined;
    if (lineStr[pos] && /[A-Z]/.test(lineStr[pos])) {
      fundsCode = lineStr[pos];
      pos += 1;
    }
    
    // Amount - find the next non-digit character to determine amount length
    let amountEndPos = pos;
    while (amountEndPos < lineStr.length && /[\d,.]/.test(lineStr[amountEndPos])) {
      amountEndPos++;
    }
    
    const amountStr = lineStr.substring(pos, amountEndPos);
    const amount = this.validateAmount(amountStr.replace(',', '.'));
    pos = amountEndPos;
    
    // Transaction type code (1 character)
    const transactionTypeCode = lineStr[pos] || '';
    pos += 1;
    
    // Reference for account owner (up to next // or end)
    let referenceEndPos = lineStr.indexOf('//', pos);
    if (referenceEndPos === -1) {
      referenceEndPos = lineStr.length;
    }
    
    const referenceForAccountOwner = lineStr.substring(pos, referenceEndPos);
    pos = referenceEndPos;
    
    // Reference of account servicing institution (after //)
    let referenceOfAccountServicingInstitution: string | undefined;
    if (pos < lineStr.length && lineStr.substring(pos, pos + 2) === '//') {
      pos += 2;
      referenceOfAccountServicingInstitution = lineStr.substring(pos);
    }

    // Parse value date
    const year = 2000 + parseInt(valueDateStr.substring(0, 2));
    const month = parseInt(valueDateStr.substring(2, 4));
    const day = parseInt(valueDateStr.substring(4, 6));
    const valueDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

    return {
      valueDate,
      entryDate,
      debitCreditMark,
      fundsCode,
      amount,
      transactionTypeCode,
      referenceForAccountOwner,
      referenceOfAccountServicingInstitution,
    };
  }

  private convertToStandardFormat(swiftStatement: SwiftMT940Statement): BankStatementParseResult {
    // Extract IBAN from account identification
    const accountIban = this.extractIbanFromAccountId(swiftStatement.accountIdentification);
    
    // Convert statement date from closing balance
    const statementDate = new Date(swiftStatement.closingBalance.date);
    
    // Convert transactions
    const transactions = swiftStatement.statementLines.map((line, index) => {
      const transactionDate = new Date(line.valueDate);
      
      // Combine all information for description and symbol extraction
      const allInfo = [
        line.referenceForAccountOwner,
        line.referenceOfAccountServicingInstitution,
        ...(line.informationToAccountOwner || []),
      ].filter(Boolean).join(' ');

      return {
        transactionDate,
        amount: line.amount,
        currency: 'EUR', // Default, could be extracted from balance
        debitCredit: this.parseDebitCredit(line.debitCreditMark),
        variableSymbol: this.extractVariableSymbol(allInfo),
        specificSymbol: this.extractSpecificSymbol(allInfo),
        constantSymbol: this.extractConstantSymbol(allInfo),
        counterpartyName: this.extractCounterpartyName(allInfo),
        counterpartyAccount: this.extractAccountNumber(allInfo),
        counterpartyBankCode: undefined, // Not typically available in MT940
        reference: line.referenceForAccountOwner || line.referenceOfAccountServicingInstitution,
        description: this.cleanText(allInfo) || `Transaction ${line.transactionTypeCode}`,
      };
    });

    return {
      statement: {
        accountIban,
        statementDate,
        openingBalance: swiftStatement.openingBalance.amount,
        closingBalance: swiftStatement.closingBalance.amount,
        fileName: this.fileName,
        fileType: this.fileType,
        processed: false,
        omegaImported: false,
      },
      transactions,
    };
  }

  private extractIbanFromAccountId(accountId: string): string {
    // Account ID might be in format: ************************ or similar
    if (accountId.startsWith('SK') && accountId.length >= 22) {
      return accountId.substring(0, 24); // Standard IBAN length
    }
    
    // If not IBAN format, try to construct it (this is bank-specific)
    if (/^\d+$/.test(accountId)) {
      // Assume it's a Slovak account number, construct IBAN
      return `SK${accountId.padStart(22, '0')}`;
    }
    
    return accountId; // Return as-is if we can't determine format
  }

  private extractCounterpartyName(text: string): string | undefined {
    // Look for name patterns in the text
    const lines = text.split(/\s+/);
    
    // Common patterns for names in SWIFT messages
    const namePatterns = [
      /^[A-Z][a-z]+\s+[A-Z][a-z]+/, // First Last
      /^[A-Z\s]+(?:s\.r\.o\.|a\.s\.|spol\.)/, // Company names
    ];

    for (const line of lines) {
      for (const pattern of namePatterns) {
        if (pattern.test(line)) {
          return this.normalizeCounterpartyName(line);
        }
      }
    }

    // If no pattern matches, try to extract the first meaningful text
    const meaningfulText = text.replace(/\b(VS|SS|KS|REF)\d+\b/g, '').trim();
    if (meaningfulText.length > 3) {
      return this.normalizeCounterpartyName(meaningfulText.split(/\s+/).slice(0, 3).join(' '));
    }

    return undefined;
  }
}
