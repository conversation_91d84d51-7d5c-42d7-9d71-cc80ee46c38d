import type { BankStatementParseResult, BankFileType } from '@/types/bank';
import { BankParsingError } from '@/utils/errors';
import { logger } from '@/utils/logger';

export abstract class BaseBankParser {
  protected fileName: string;
  protected fileType: BankFileType;

  constructor(fileName: string, fileType: BankFileType) {
    this.fileName = fileName;
    this.fileType = fileType;
  }

  // Abstract method that each parser must implement
  abstract parse(content: string | Buffer): Promise<BankStatementParseResult>;

  // Common validation methods
  protected validateIBAN(iban: string): boolean {
    // Basic IBAN validation for Slovak accounts
    const cleanIban = iban.replace(/\s/g, '');
    return /^SK\d{22}$/.test(cleanIban);
  }

  protected validateAmount(amount: string | number): number {
    const numAmount = typeof amount === 'string' ? parseFloat(amount.replace(',', '.')) : amount;
    
    if (isNaN(numAmount)) {
      throw new BankParsingError(`Invalid amount: ${amount}`, this.fileName);
    }
    
    return numAmount;
  }

  protected validateDate(dateStr: string): Date {
    // Handle various date formats
    const formats = [
      /^(\d{4})-(\d{2})-(\d{2})$/, // YYYY-MM-DD
      /^(\d{2})\.(\d{2})\.(\d{4})$/, // DD.MM.YYYY
      /^(\d{2})\/(\d{2})\/(\d{4})$/, // DD/MM/YYYY
      /^(\d{6})$/, // YYMMDD
      /^(\d{8})$/, // YYYYMMDD
    ];

    for (const format of formats) {
      const match = dateStr.match(format);
      if (match) {
        let year: number, month: number, day: number;

        if (format === formats[0]) { // YYYY-MM-DD
          [, year, month, day] = match.map(Number);
        } else if (format === formats[1] || format === formats[2]) { // DD.MM.YYYY or DD/MM/YYYY
          [, day, month, year] = match.map(Number);
        } else if (format === formats[3]) { // YYMMDD
          const yymmdd = match[1];
          year = 2000 + parseInt(yymmdd.substring(0, 2));
          month = parseInt(yymmdd.substring(2, 4));
          day = parseInt(yymmdd.substring(4, 6));
        } else if (format === formats[4]) { // YYYYMMDD
          const yyyymmdd = match[1];
          year = parseInt(yyyymmdd.substring(0, 4));
          month = parseInt(yyyymmdd.substring(4, 6));
          day = parseInt(yyyymmdd.substring(6, 8));
        } else {
          continue;
        }

        const date = new Date(year, month - 1, day);
        
        // Validate the date is valid
        if (date.getFullYear() === year && 
            date.getMonth() === month - 1 && 
            date.getDate() === day) {
          return date;
        }
      }
    }

    throw new BankParsingError(`Invalid date format: ${dateStr}`, this.fileName);
  }

  protected extractVariableSymbol(text: string): string | undefined {
    // Common patterns for variable symbol extraction
    const patterns = [
      /VS[:\s]*(\d+)/i,
      /variabiln[ýy]\s*symbol[:\s]*(\d+)/i,
      /var[:\s]*(\d+)/i,
      /\bVS(\d+)\b/i,
      /\/VS(\d+)/i,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return undefined;
  }

  protected extractSpecificSymbol(text: string): string | undefined {
    const patterns = [
      /SS[:\s]*(\d+)/i,
      /specifick[ýy]\s*symbol[:\s]*(\d+)/i,
      /spec[:\s]*(\d+)/i,
      /\bSS(\d+)\b/i,
      /\/SS(\d+)/i,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return undefined;
  }

  protected extractConstantSymbol(text: string): string | undefined {
    const patterns = [
      /KS[:\s]*(\d+)/i,
      /konstantn[ýy]\s*symbol[:\s]*(\d+)/i,
      /konst[:\s]*(\d+)/i,
      /\bKS(\d+)\b/i,
      /\/KS(\d+)/i,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return undefined;
  }

  protected extractAccountNumber(text: string): string | undefined {
    // Slovak account number patterns
    const patterns = [
      /(\d{1,10}[-\/]\d{4})/g, // account-bank format
      /(SK\d{22})/g, // IBAN format
      /(\d{10,})/g, // long account number
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return undefined;
  }

  protected cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[\r\n\t]/g, ' ') // Replace line breaks and tabs with spaces
      .trim();
  }

  protected parseDebitCredit(indicator: string): 'DBIT' | 'CRDT' {
    const normalized = indicator.toUpperCase().trim();
    
    if (['D', 'DBIT', 'DEBIT', '-'].includes(normalized)) {
      return 'DBIT';
    }
    
    if (['C', 'CRDT', 'CREDIT', '+'].includes(normalized)) {
      return 'CRDT';
    }
    
    throw new BankParsingError(`Invalid debit/credit indicator: ${indicator}`, this.fileName);
  }

  protected logParsingProgress(step: string, details?: Record<string, unknown>): void {
    logger.debug(`Bank parser [${this.fileType}]: ${step}`, {
      fileName: this.fileName,
      ...details,
    });
  }

  protected createParsingError(message: string, lineNumber?: number): BankParsingError {
    return new BankParsingError(message, this.fileName, lineNumber);
  }

  // Helper method to extract bank code from account number or IBAN
  protected extractBankCode(accountOrIban: string): string | undefined {
    // From IBAN (positions 5-8)
    if (accountOrIban.startsWith('SK') && accountOrIban.length >= 8) {
      return accountOrIban.substring(4, 8);
    }
    
    // From account number format (after dash or slash)
    const match = accountOrIban.match(/[-\/](\d{4})$/);
    if (match) {
      return match[1];
    }
    
    return undefined;
  }

  // Helper method to normalize counterparty name
  protected normalizeCounterpartyName(name: string): string {
    return this.cleanText(name)
      .replace(/\s+/g, ' ')
      .substring(0, 75) // Limit length for database
      .trim();
  }

  // Helper method to extract reference information
  protected extractReference(text: string): string | undefined {
    // Look for reference patterns
    const patterns = [
      /REF[:\s]*([A-Z0-9]+)/i,
      /REFERENCE[:\s]*([A-Z0-9]+)/i,
      /\bREF([A-Z0-9]+)\b/i,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return undefined;
  }

  // Validation method for parsed result
  protected validateParseResult(result: BankStatementParseResult): void {
    if (!result.statement.accountIban) {
      throw new BankParsingError('Missing account IBAN in statement', this.fileName);
    }

    if (!this.validateIBAN(result.statement.accountIban)) {
      throw new BankParsingError(`Invalid IBAN: ${result.statement.accountIban}`, this.fileName);
    }

    if (result.transactions.length === 0) {
      logger.warn('No transactions found in bank statement', {
        fileName: this.fileName,
        fileType: this.fileType,
      });
    }

    // Validate each transaction
    result.transactions.forEach((transaction, index) => {
      if (!transaction.transactionDate) {
        throw new BankParsingError(`Missing transaction date at index ${index}`, this.fileName);
      }

      if (typeof transaction.amount !== 'number' || isNaN(transaction.amount)) {
        throw new BankParsingError(`Invalid amount at index ${index}: ${transaction.amount}`, this.fileName);
      }

      if (!['DBIT', 'CRDT'].includes(transaction.debitCredit)) {
        throw new BankParsingError(`Invalid debit/credit indicator at index ${index}: ${transaction.debitCredit}`, this.fileName);
      }
    });

    logger.info('Bank statement parsing validation completed', {
      fileName: this.fileName,
      fileType: this.fileType,
      transactionCount: result.transactions.length,
      errors: result.errors?.length || 0,
      warnings: result.warnings?.length || 0,
    });
  }
}
