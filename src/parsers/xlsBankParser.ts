import * as XLSX from 'xlsx';

import type { BankStatementParseResult } from '@/types/bank';
import { BaseBankParser } from './baseBankParser';
import { BankParsingError } from '@/utils/errors';

interface XlsRow {
  [key: string]: any;
}

export class XlsBankParser extends BaseBankParser {
  constructor(fileName: string) {
    super(fileName, 'XLS');
  }

  async parse(content: string | Buffer): Promise<BankStatementParseResult> {
    this.logParsingProgress('Starting XLS parsing');

    try {
      const buffer = content instanceof Buffer ? content : Buffer.from(content, 'binary');
      
      // Parse Excel file
      const workbook = XLSX.read(buffer, { 
        type: 'buffer',
        cellDates: true,
        cellNF: false,
        cellText: false,
      });

      // Get first worksheet
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new BankParsingError('No worksheets found in Excel file', this.fileName);
      }

      const worksheet = workbook.Sheets[sheetName];
      
      // Convert to JSON
      const rows = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        blankrows: false,
      }) as any[][];

      this.logParsingProgress('Excel file parsed', {
        sheetName,
        rowCount: rows.length,
      });

      // Parse the data
      const result = this.parseExcelData(rows);
      
      this.validateParseResult(result);
      this.logParsingProgress('XLS parsing completed', {
        transactionCount: result.transactions.length,
      });

      return result;

    } catch (error) {
      if (error instanceof BankParsingError) {
        throw error;
      }
      
      throw new BankParsingError(
        `XLS parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.fileName
      );
    }
  }

  private parseExcelData(rows: any[][]): BankStatementParseResult {
    if (rows.length < 2) {
      throw new BankParsingError('Excel file must contain at least header and one data row', this.fileName);
    }

    // Find header row (usually first non-empty row)
    let headerRowIndex = 0;
    while (headerRowIndex < rows.length && (!rows[headerRowIndex] || rows[headerRowIndex].length === 0)) {
      headerRowIndex++;
    }

    if (headerRowIndex >= rows.length) {
      throw new BankParsingError('No header row found in Excel file', this.fileName);
    }

    const headers = rows[headerRowIndex].map((h: any) => String(h).trim().toLowerCase());
    
    this.logParsingProgress('Headers found', { headers });

    // Map column indices based on header names
    const columnMap = this.mapColumns(headers);
    
    // Extract account information from filename or first data rows
    const accountInfo = this.extractAccountInfo(rows, headerRowIndex);
    
    // Parse data rows
    const transactions = [];
    const errors: string[] = [];
    
    for (let i = headerRowIndex + 1; i < rows.length; i++) {
      const row = rows[i];
      
      if (!row || row.length === 0 || this.isEmptyRow(row)) {
        continue;
      }

      try {
        const transaction = this.parseDataRow(row, columnMap, i);
        if (transaction) {
          transactions.push(transaction);
        }
      } catch (error) {
        const errorMsg = `Error parsing row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        this.logParsingProgress('Row parsing error', { rowIndex: i + 1, error: errorMsg });
      }
    }

    return {
      statement: {
        accountIban: accountInfo.iban,
        statementDate: accountInfo.statementDate,
        openingBalance: accountInfo.openingBalance,
        closingBalance: accountInfo.closingBalance,
        fileName: this.fileName,
        fileType: this.fileType,
        processed: false,
        omegaImported: false,
      },
      transactions,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  private mapColumns(headers: string[]): Record<string, number> {
    const columnMap: Record<string, number> = {};

    // Define possible column name variations
    const columnMappings = {
      processingDate: ['dátum spracovania', 'datum spracovania', 'processing date', 'dátum', 'datum', 'date'],
      valueDate: ['dátum zúčtovania', 'datum zuctovania', 'value date', 'valuta'],
      amount: ['suma', 'amount', 'hodnota', 'value'],
      currency: ['mena', 'currency', 'cur'],
      type: ['typ', 'type', 'druh'],
      accountNumber: ['číslo účtu', 'cislo uctu', 'account number', 'účet', 'ucet'],
      counterpartyName: ['príjemca', 'prijemca', 'platiteľ', 'platitel', 'counterparty', 'názov druhého účtu', 'nazov druheho uctu'],
      counterpartyAccount: ['protiúčet', 'protiucet', 'counterparty account'],
      bankCode: ['kód banky', 'kod banky', 'bank code'],
      swiftCode: ['swift kód', 'swift kod', 'swift code'],
      variableSymbol: ['variabilný symbol', 'variabilny symbol', 'variable symbol', 'vs'],
      specificSymbol: ['špecifický symbol', 'specificky symbol', 'specific symbol', 'ss'],
      constantSymbol: ['konštantný symbol', 'konstantny symbol', 'constant symbol', 'ks'],
      reference: ['referencia platiteľa', 'referencia platitela', 'reference'],
      description: ['informácia pre príjemcu', 'informacia pre prijemcu', 'description', 'popis transakcie'],
      paymentType: ['typ platby', 'payment type'],
      originalAmount: ['pôvodná suma', 'povodna suma', 'original amount'],
      originalCurrency: ['pôvodná mena', 'povodna mena', 'original currency'],
      fee: ['poplatok', 'fee'],
    };

    // Map each column type to its index
    for (const [key, variations] of Object.entries(columnMappings)) {
      for (let i = 0; i < headers.length; i++) {
        const header = headers[i];
        if (variations.some(variation => header.includes(variation))) {
          columnMap[key] = i;
          break;
        }
      }
    }

    this.logParsingProgress('Column mapping completed', { columnMap });
    return columnMap;
  }

  private extractAccountInfo(rows: any[][], headerRowIndex: number): {
    iban: string;
    statementDate: Date;
    openingBalance: number;
    closingBalance: number;
  } {
    // Try to extract IBAN from filename
    let iban = '';
    const ibanMatch = this.fileName.match(/SK\d{22}/);
    if (ibanMatch) {
      iban = ibanMatch[0];
    }

    // Try to extract date from filename
    let statementDate = new Date();
    const dateMatch = this.fileName.match(/(\d{8})/);
    if (dateMatch) {
      const dateStr = dateMatch[1];
      const year = parseInt(dateStr.substring(0, 4));
      const month = parseInt(dateStr.substring(4, 6));
      const day = parseInt(dateStr.substring(6, 8));
      statementDate = new Date(year, month - 1, day);
    }

    // Default balances (XLS format might not contain this info)
    let openingBalance = 0;
    let closingBalance = 0;

    // Try to find balance information in the first few rows
    for (let i = 0; i < Math.min(headerRowIndex, 10); i++) {
      const row = rows[i];
      if (!row) continue;
      
      const rowText = row.join(' ').toLowerCase();
      
      // Look for opening balance
      const openingMatch = rowText.match(/(?:počiatočný|pociatocny|opening).*?(\d+[,.]?\d*)/);
      if (openingMatch) {
        openingBalance = this.validateAmount(openingMatch[1]);
      }
      
      // Look for closing balance
      const closingMatch = rowText.match(/(?:konečný|konecny|closing).*?(\d+[,.]?\d*)/);
      if (closingMatch) {
        closingBalance = this.validateAmount(closingMatch[1]);
      }
    }

    return {
      iban: iban || 'UNKNOWN',
      statementDate,
      openingBalance,
      closingBalance,
    };
  }

  private parseDataRow(row: any[], columnMap: Record<string, number>, rowIndex: number): any {
    const getValue = (key: string): any => {
      const index = columnMap[key];
      return index !== undefined ? row[index] : undefined;
    };

    // Parse transaction date
    let transactionDate: Date;
    const processingDate = getValue('processingDate');
    const valueDate = getValue('valueDate');
    
    if (processingDate) {
      transactionDate = this.parseExcelDate(processingDate);
    } else if (valueDate) {
      transactionDate = this.parseExcelDate(valueDate);
    } else {
      throw new BankParsingError(`Missing transaction date in row ${rowIndex + 1}`, this.fileName);
    }

    // Parse amount
    const amountValue = getValue('amount');
    if (amountValue === undefined || amountValue === '') {
      throw new BankParsingError(`Missing amount in row ${rowIndex + 1}`, this.fileName);
    }
    
    const amount = Math.abs(this.validateAmount(amountValue));

    // Determine debit/credit from amount sign or type
    let debitCredit: 'DBIT' | 'CRDT' = 'CRDT';
    const typeValue = getValue('type');
    
    if (typeof amountValue === 'number' && amountValue < 0) {
      debitCredit = 'DBIT';
    } else if (typeof typeValue === 'string') {
      const typeStr = typeValue.toLowerCase();
      if (typeStr.includes('debet') || typeStr.includes('výdaj') || typeStr.includes('vydaj')) {
        debitCredit = 'DBIT';
      }
    }

    // Extract other fields
    const currency = getValue('currency') || 'EUR';
    const counterpartyName = getValue('counterpartyName');
    const counterpartyAccount = getValue('counterpartyAccount');
    const variableSymbol = getValue('variableSymbol');
    const specificSymbol = getValue('specificSymbol');
    const constantSymbol = getValue('constantSymbol');
    const reference = getValue('reference');
    const description = getValue('description') || getValue('paymentType') || 'Bank transaction';

    return {
      transactionDate,
      amount,
      currency,
      debitCredit,
      variableSymbol: variableSymbol ? String(variableSymbol) : undefined,
      specificSymbol: specificSymbol ? String(specificSymbol) : undefined,
      constantSymbol: constantSymbol ? String(constantSymbol) : undefined,
      counterpartyName: counterpartyName ? this.normalizeCounterpartyName(String(counterpartyName)) : undefined,
      counterpartyAccount: counterpartyAccount ? String(counterpartyAccount) : undefined,
      counterpartyBankCode: this.extractBankCode(String(counterpartyAccount || '')),
      reference: reference ? String(reference) : undefined,
      description: this.cleanText(String(description)),
    };
  }

  private parseExcelDate(dateValue: any): Date {
    if (dateValue instanceof Date) {
      return dateValue;
    }
    
    if (typeof dateValue === 'number') {
      // Excel serial date
      return new Date((dateValue - 25569) * 86400 * 1000);
    }
    
    if (typeof dateValue === 'string') {
      return this.validateDate(dateValue);
    }
    
    throw new BankParsingError(`Invalid date value: ${dateValue}`, this.fileName);
  }

  private isEmptyRow(row: any[]): boolean {
    return row.every(cell => cell === undefined || cell === null || cell === '');
  }
}
