import xml2js from 'xml2js';

import type { 
  BankStatementParseResult, 
  SepaDocument, 
  SepaEntry, 
  SepaTransactionDetails,
  SepaRemittanceInformation 
} from '@/types/bank';
import { BaseBankParser } from './baseBankParser';
import { BankParsingError } from '@/utils/errors';

export class XmlBankParser extends BaseBankParser {
  constructor(fileName: string) {
    super(fileName, 'XML');
  }

  async parse(content: string | Buffer): Promise<BankStatementParseResult> {
    this.logParsingProgress('Starting XML parsing');

    try {
      const xmlContent = content instanceof Buffer ? content.toString('utf-8') : content;
      
      // Parse XML
      const parser = new xml2js.Parser({
        explicitArray: false,
        ignoreAttrs: false,
        mergeAttrs: true,
        explicitRoot: true,
      });

      const parsed = await parser.parseStringPromise(xmlContent) as SepaDocument;
      
      this.logParsingProgress('XML parsed successfully');

      // Extract statement data
      const document = parsed.Document;
      if (!document || !document.BkToCstmrStmt) {
        throw new BankParsingError('Invalid XML structure: missing BkToCstmrStmt', this.fileName);
      }

      const bkToCstmrStmt = Array.isArray(document.BkToCstmrStmt) 
        ? document.BkToCstmrStmt[0] 
        : document.BkToCstmrStmt;

      if (!bkToCstmrStmt.Stmt) {
        throw new BankParsingError('Invalid XML structure: missing Stmt', this.fileName);
      }

      const stmt = Array.isArray(bkToCstmrStmt.Stmt) 
        ? bkToCstmrStmt.Stmt[0] 
        : bkToCstmrStmt.Stmt;

      // Extract account information
      const account = stmt.Acct;
      if (!account || !account.Id) {
        throw new BankParsingError('Missing account information', this.fileName);
      }

      const accountIban = account.Id.IBAN || account.Id.Othr?.Id || '';
      if (!accountIban) {
        throw new BankParsingError('Missing account IBAN', this.fileName);
      }

      // Extract balances
      const balances = Array.isArray(stmt.Bal) ? stmt.Bal : [stmt.Bal];
      const openingBalance = this.findBalance(balances, 'OPBD') || 0;
      const closingBalance = this.findBalance(balances, 'CLBD') || 0;

      // Extract statement date
      const statementDate = stmt.CreDtTm ? new Date(stmt.CreDtTm) : new Date();

      this.logParsingProgress('Statement header parsed', {
        accountIban,
        openingBalance,
        closingBalance,
        statementDate,
      });

      // Parse transactions
      const entries = stmt.Ntry ? (Array.isArray(stmt.Ntry) ? stmt.Ntry : [stmt.Ntry]) : [];
      const transactions = [];
      const errors: string[] = [];
      const warnings: string[] = [];

      for (let i = 0; i < entries.length; i++) {
        try {
          const transaction = this.parseEntry(entries[i], i);
          if (transaction) {
            transactions.push(transaction);
          }
        } catch (error) {
          const errorMsg = `Error parsing transaction ${i}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
          this.logParsingProgress('Transaction parsing error', { index: i, error: errorMsg });
        }
      }

      const result: BankStatementParseResult = {
        statement: {
          accountIban,
          statementDate,
          openingBalance,
          closingBalance,
          fileName: this.fileName,
          fileType: this.fileType,
          processed: false,
          omegaImported: false,
        },
        transactions,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
      };

      this.validateParseResult(result);
      this.logParsingProgress('XML parsing completed', {
        transactionCount: transactions.length,
        errorCount: errors.length,
      });

      return result;

    } catch (error) {
      if (error instanceof BankParsingError) {
        throw error;
      }
      
      throw new BankParsingError(
        `XML parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.fileName
      );
    }
  }

  private findBalance(balances: any[], type: string): number {
    const balance = balances.find(b => b.Tp?.CdOrPrtry?.Cd === type);
    if (!balance) return 0;

    const amount = balance.Amt;
    if (!amount) return 0;

    const value = typeof amount === 'object' ? amount._ : amount;
    return this.validateAmount(value);
  }

  private parseEntry(entry: SepaEntry, index: number): any {
    // Extract basic transaction info
    const amount = this.parseAmount(entry.Amt);
    const debitCredit = entry.CdtDbtInd;
    const transactionDate = this.parseEntryDate(entry);

    // Extract transaction details
    const entryDetails = entry.NtryDtls;
    let transactionDetails: SepaTransactionDetails | undefined;
    
    if (entryDetails) {
      const txDtls = Array.isArray(entryDetails) ? entryDetails[0] : entryDetails;
      transactionDetails = Array.isArray(txDtls.TxDtls) ? txDtls.TxDtls[0] : txDtls.TxDtls;
    }

    // Extract counterparty information
    const counterpartyInfo = this.extractCounterpartyInfo(transactionDetails);
    
    // Extract payment symbols and references
    const paymentInfo = this.extractPaymentInfo(transactionDetails);
    
    // Extract description
    const description = this.extractDescription(entry, transactionDetails);

    return {
      transactionDate,
      amount: Math.abs(amount),
      currency: this.extractCurrency(entry.Amt),
      debitCredit: this.parseDebitCredit(debitCredit),
      variableSymbol: paymentInfo.variableSymbol,
      specificSymbol: paymentInfo.specificSymbol,
      constantSymbol: paymentInfo.constantSymbol,
      counterpartyName: counterpartyInfo.name,
      counterpartyAccount: counterpartyInfo.account,
      counterpartyBankCode: counterpartyInfo.bankCode,
      reference: entry.NtryRef || paymentInfo.reference,
      description: this.cleanText(description),
    };
  }

  private parseAmount(amtObj: any): number {
    if (!amtObj) {
      throw new BankParsingError('Missing amount in transaction', this.fileName);
    }

    const value = typeof amtObj === 'object' ? amtObj._ : amtObj;
    return this.validateAmount(value);
  }

  private extractCurrency(amtObj: any): string {
    if (typeof amtObj === 'object' && amtObj.Ccy) {
      return amtObj.Ccy;
    }
    return 'EUR'; // Default currency
  }

  private parseEntryDate(entry: SepaEntry): Date {
    // Try different date fields
    const dateFields = [
      entry.BookgDt?.Dt,
      entry.BookgDt?.DtTm,
      entry.ValDt?.Dt,
      entry.ValDt?.DtTm,
    ];

    for (const dateField of dateFields) {
      if (dateField) {
        try {
          return new Date(dateField);
        } catch {
          // Try next field
        }
      }
    }

    throw new BankParsingError('Missing or invalid transaction date', this.fileName);
  }

  private extractCounterpartyInfo(txDetails?: SepaTransactionDetails): {
    name?: string;
    account?: string;
    bankCode?: string;
  } {
    if (!txDetails || !txDetails.RltdPties) {
      return {};
    }

    const parties = txDetails.RltdPties;
    
    // Extract debtor or creditor info
    const party = parties.Dbtr || parties.Cdtr;
    const partyAccount = parties.DbtrAcct || parties.CdtrAcct;

    const name = party?.Nm;
    let account: string | undefined;
    let bankCode: string | undefined;

    if (partyAccount?.Id) {
      if (partyAccount.Id.IBAN) {
        account = partyAccount.Id.IBAN;
        bankCode = this.extractBankCode(account);
      } else if (partyAccount.Id.Othr?.Id) {
        account = partyAccount.Id.Othr.Id;
      }
    }

    return {
      name: name ? this.normalizeCounterpartyName(name) : undefined,
      account,
      bankCode,
    };
  }

  private extractPaymentInfo(txDetails?: SepaTransactionDetails): {
    variableSymbol?: string;
    specificSymbol?: string;
    constantSymbol?: string;
    reference?: string;
  } {
    if (!txDetails) {
      return {};
    }

    // Extract from references
    const refs = txDetails.Refs;
    let variableSymbol: string | undefined;
    let specificSymbol: string | undefined;
    let constantSymbol: string | undefined;
    let reference: string | undefined;

    if (refs) {
      reference = refs.EndToEndId || refs.TxId || refs.InstrId;
    }

    // Extract from remittance information
    const rmtInf = txDetails.RmtInf;
    if (rmtInf) {
      const unstructured = Array.isArray(rmtInf.Ustrd) ? rmtInf.Ustrd.join(' ') : rmtInf.Ustrd;
      
      if (unstructured) {
        variableSymbol = this.extractVariableSymbol(unstructured);
        specificSymbol = this.extractSpecificSymbol(unstructured);
        constantSymbol = this.extractConstantSymbol(unstructured);
        
        if (!reference) {
          reference = this.extractReference(unstructured);
        }
      }
    }

    return {
      variableSymbol,
      specificSymbol,
      constantSymbol,
      reference,
    };
  }

  private extractDescription(entry: SepaEntry, txDetails?: SepaTransactionDetails): string {
    const parts: string[] = [];

    // Add entry additional info
    if (entry.AddtlNtryInf) {
      parts.push(entry.AddtlNtryInf);
    }

    // Add transaction additional info
    if (txDetails?.AddtlTxInf) {
      parts.push(txDetails.AddtlTxInf);
    }

    // Add remittance information
    if (txDetails?.RmtInf?.Ustrd) {
      const ustrd = Array.isArray(txDetails.RmtInf.Ustrd) 
        ? txDetails.RmtInf.Ustrd.join(' ') 
        : txDetails.RmtInf.Ustrd;
      parts.push(ustrd);
    }

    // Add bank transaction code description
    if (txDetails?.BkTxCd?.Prtry) {
      parts.push(txDetails.BkTxCd.Prtry);
    }

    return parts.join(' ').trim() || 'Bank transaction';
  }
}
