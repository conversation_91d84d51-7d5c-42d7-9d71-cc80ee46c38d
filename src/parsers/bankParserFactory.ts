import type { BankFileType, BankStatementParseResult } from '@/types/bank';
import { BaseBankParser } from './baseBankParser';
import { XmlBankParser } from './xmlBankParser';
import { SwiftBankParser } from './swiftBankParser';
import { XlsBankParser } from './xlsBankParser';
import { BankParsingError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import { getFileExtension } from '@/utils/helpers';

export class BankParserFactory {
  // Create parser based on file type
  static createParser(fileName: string, fileType?: BankFileType): BaseBankParser {
    const detectedType = fileType || this.detectFileType(fileName);
    
    switch (detectedType) {
      case 'XML':
        return new XmlBankParser(fileName);
      case 'SWIFT':
        return new SwiftBankParser(fileName);
      case 'XLS':
        return new XlsBankParser(fileName);
      default:
        throw new BankParsingError(`Unsupported file type: ${detectedType}`, fileName);
    }
  }

  // Auto-detect file type based on filename and content
  static detectFileType(fileName: string): BankFileType {
    const extension = getFileExtension(fileName).toLowerCase();
    
    switch (extension) {
      case 'xml':
        return 'XML';
      case 'sta':
      case 'mt940':
      case 'swift':
        return 'SWIFT';
      case 'xls':
      case 'xlsx':
        return 'XLS';
      default:
        // Try to detect from filename patterns
        const lowerFileName = fileName.toLowerCase();
        
        if (lowerFileName.includes('mt940') || lowerFileName.includes('swift')) {
          return 'SWIFT';
        }
        
        if (lowerFileName.includes('sepa') || lowerFileName.includes('xml')) {
          return 'XML';
        }
        
        throw new BankParsingError(`Cannot detect file type from filename: ${fileName}`, fileName);
    }
  }

  // Parse bank statement file
  static async parseFile(
    fileName: string, 
    content: string | Buffer, 
    fileType?: BankFileType
  ): Promise<BankStatementParseResult> {
    
    logger.info('Starting bank file parsing', {
      fileName,
      fileType: fileType || 'auto-detect',
      contentSize: content.length,
    });

    try {
      const parser = this.createParser(fileName, fileType);
      const result = await parser.parse(content);
      
      logger.info('Bank file parsing completed successfully', {
        fileName,
        fileType: result.statement.fileType,
        transactionCount: result.transactions.length,
        hasErrors: !!result.errors?.length,
        hasWarnings: !!result.warnings?.length,
      });

      return result;
      
    } catch (error) {
      logger.error('Bank file parsing failed', {
        fileName,
        fileType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw error;
    }
  }

  // Validate file before parsing
  static validateFile(fileName: string, content: string | Buffer): {
    isValid: boolean;
    errors: string[];
    detectedType?: BankFileType;
  } {
    const errors: string[] = [];
    
    try {
      // Check file size
      if (content.length === 0) {
        errors.push('File is empty');
      }
      
      if (content.length > 50 * 1024 * 1024) { // 50MB limit
        errors.push('File is too large (max 50MB)');
      }

      // Try to detect file type
      let detectedType: BankFileType | undefined;
      try {
        detectedType = this.detectFileType(fileName);
      } catch (error) {
        errors.push(`Cannot detect file type: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Basic content validation
      if (detectedType && content.length > 0) {
        const contentStr = content instanceof Buffer ? content.toString('utf-8') : content;
        
        switch (detectedType) {
          case 'XML':
            if (!contentStr.includes('<?xml') && !contentStr.includes('<Document')) {
              errors.push('File does not appear to be valid XML');
            }
            break;
            
          case 'SWIFT':
            if (!contentStr.includes(':20:') && !contentStr.includes(':25:')) {
              errors.push('File does not appear to be valid SWIFT MT940 format');
            }
            break;
            
          case 'XLS':
            // For XLS files, we can't easily validate content without parsing
            // The XLSX library will handle validation during parsing
            break;
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        detectedType,
      };
      
    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      return {
        isValid: false,
        errors,
      };
    }
  }

  // Get supported file types
  static getSupportedFileTypes(): Array<{
    type: BankFileType;
    extensions: string[];
    description: string;
  }> {
    return [
      {
        type: 'XML',
        extensions: ['xml'],
        description: 'SEPA XML bank statements',
      },
      {
        type: 'SWIFT',
        extensions: ['sta', 'mt940', 'swift'],
        description: 'SWIFT MT940 bank statements',
      },
      {
        type: 'XLS',
        extensions: ['xls', 'xlsx'],
        description: 'Excel bank statements',
      },
    ];
  }

  // Check if file type is supported
  static isFileTypeSupported(fileName: string): boolean {
    try {
      this.detectFileType(fileName);
      return true;
    } catch {
      return false;
    }
  }

  // Get file type description
  static getFileTypeDescription(fileType: BankFileType): string {
    const supportedTypes = this.getSupportedFileTypes();
    const typeInfo = supportedTypes.find(t => t.type === fileType);
    return typeInfo?.description || `${fileType} bank statements`;
  }

  // Parse multiple files
  static async parseMultipleFiles(
    files: Array<{
      fileName: string;
      content: string | Buffer;
      fileType?: BankFileType;
    }>
  ): Promise<Array<{
    fileName: string;
    result?: BankStatementParseResult;
    error?: string;
  }>> {
    
    const results = [];
    
    for (const file of files) {
      try {
        const result = await this.parseFile(file.fileName, file.content, file.fileType);
        results.push({
          fileName: file.fileName,
          result,
        });
      } catch (error) {
        results.push({
          fileName: file.fileName,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
    
    return results;
  }

  // Extract preview information without full parsing
  static async getFilePreview(
    fileName: string, 
    content: string | Buffer, 
    fileType?: BankFileType
  ): Promise<{
    fileType: BankFileType;
    accountIban?: string;
    statementDate?: Date;
    transactionCount?: number;
    fileSize: number;
  }> {
    
    const detectedType = fileType || this.detectFileType(fileName);
    const contentStr = content instanceof Buffer ? content.toString('utf-8') : content;
    
    const preview = {
      fileType: detectedType,
      fileSize: content.length,
      accountIban: undefined as string | undefined,
      statementDate: undefined as Date | undefined,
      transactionCount: undefined as number | undefined,
    };

    try {
      switch (detectedType) {
        case 'XML':
          // Quick XML preview
          const ibanMatch = contentStr.match(/<IBAN>([^<]+)<\/IBAN>/);
          if (ibanMatch) {
            preview.accountIban = ibanMatch[1];
          }
          
          const entryMatches = contentStr.match(/<Ntry>/g);
          if (entryMatches) {
            preview.transactionCount = entryMatches.length;
          }
          break;
          
        case 'SWIFT':
          // Quick SWIFT preview
          const accountMatch = contentStr.match(/:25:([^\r\n]+)/);
          if (accountMatch) {
            preview.accountIban = accountMatch[1].trim();
          }
          
          const transactionMatches = contentStr.match(/:61:/g);
          if (transactionMatches) {
            preview.transactionCount = transactionMatches.length;
          }
          break;
          
        case 'XLS':
          // For XLS, we need to actually parse to get meaningful preview
          // This is a simplified version
          const ibanFileMatch = fileName.match(/SK\d{22}/);
          if (ibanFileMatch) {
            preview.accountIban = ibanFileMatch[0];
          }
          
          const dateFileMatch = fileName.match(/(\d{8})/);
          if (dateFileMatch) {
            const dateStr = dateFileMatch[1];
            const year = parseInt(dateStr.substring(0, 4));
            const month = parseInt(dateStr.substring(4, 6));
            const day = parseInt(dateStr.substring(6, 8));
            preview.statementDate = new Date(year, month - 1, day);
          }
          break;
      }
    } catch (error) {
      logger.warn('Error generating file preview', {
        fileName,
        fileType: detectedType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return preview;
  }
}
