import { Router } from 'express';

const router = Router();

// Simple test endpoint
router.get('/statements', (req, res) => {
  res.json({
    success: true,
    data: {
      statements: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        pages: 0,
      },
    },
    timestamp: new Date().toISOString(),
  });
});

router.get('/transactions', (req, res) => {
  res.json({
    success: true,
    data: {
      transactions: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        pages: 0,
      },
    },
    timestamp: new Date().toISOString(),
  });
});

router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Bank API is working!',
    timestamp: new Date().toISOString(),
  });
});

export default router;
