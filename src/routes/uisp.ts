import { Router } from 'express';
import Jo<PERSON> from 'joi';

import { uispApi } from '@/services/uispApi';
import { invoiceSyncService } from '@/services/invoiceSync';
import type { ApiResponse } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';

const router = Router();

// Apply authentication to all UISP routes
router.use(requireAuth);

// Test UISP connection
router.get('/test-connection', asyncHandler(async (req, res) => {
  const isConnected = await uispApi.testConnection();
  
  const response: ApiResponse<{
    connected: boolean;
    apiStatus: ReturnType<typeof uispApi.getApiStatus>;
  }> = {
    success: true,
    data: {
      connected: isConnected,
      apiStatus: uispApi.getApiStatus(),
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get UISP organizations
router.get('/organizations', asyncHandler(async (req, res) => {
  const organizations = await uispApi.getOrganizations();
  
  const response: ApiResponse<typeof organizations> = {
    success: true,
    data: organizations,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get UISP clients
const getClientsSchema = Joi.object({
  organizationId: Joi.number().optional(),
  isActive: Joi.boolean().optional(),
  limit: Joi.number().min(1).max(1000).default(100),
  offset: Joi.number().min(0).default(0),
});

router.get('/clients', validateRequest({ query: getClientsSchema }), asyncHandler(async (req, res) => {
  const { organizationId, isActive, limit, offset } = req.query as any;
  
  const clients = await uispApi.getClients({
    organizationId,
    isActive,
    limit,
    offset,
  });
  
  const response: ApiResponse<typeof clients> = {
    success: true,
    data: clients,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get specific UISP client
const getClientSchema = Joi.object({
  id: Joi.number().required(),
});

router.get('/clients/:id', validateRequest({ params: getClientSchema }), asyncHandler(async (req, res) => {
  const { id } = req.params as any;
  
  const client = await uispApi.getClient(id);
  
  const response: ApiResponse<typeof client> = {
    success: true,
    data: client,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get UISP invoices
const getInvoicesSchema = Joi.object({
  clientId: Joi.number().optional(),
  status: Joi.number().optional(),
  createdDateFrom: Joi.date().iso().optional(),
  createdDateTo: Joi.date().iso().optional(),
  limit: Joi.number().min(1).max(1000).default(100),
  offset: Joi.number().min(0).default(0),
});

router.get('/invoices', validateRequest({ query: getInvoicesSchema }), asyncHandler(async (req, res) => {
  const params = req.query as any;
  
  const invoices = await uispApi.getInvoices(params);
  
  const response: ApiResponse<typeof invoices> = {
    success: true,
    data: invoices,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get specific UISP invoice
router.get('/invoices/:id', validateRequest({ params: getClientSchema }), asyncHandler(async (req, res) => {
  const { id } = req.params as any;
  
  const invoice = await uispApi.getInvoice(id);
  
  const response: ApiResponse<typeof invoice> = {
    success: true,
    data: invoice,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get recent invoices
const getRecentInvoicesSchema = Joi.object({
  days: Joi.number().min(1).max(365).default(30),
  status: Joi.number().optional(),
  limit: Joi.number().min(1).max(1000).default(100),
});

router.get('/invoices/recent', validateRequest({ query: getRecentInvoicesSchema }), asyncHandler(async (req, res) => {
  const { days, status, limit } = req.query as any;
  
  const invoices = await uispApi.getRecentInvoices(days, { status, limit });
  
  const response: ApiResponse<typeof invoices> = {
    success: true,
    data: invoices,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get unpaid invoices
router.get('/invoices/unpaid', validateRequest({ query: getInvoicesSchema }), asyncHandler(async (req, res) => {
  const params = req.query as any;
  
  const invoices = await uispApi.getUnpaidInvoices(params);
  
  const response: ApiResponse<typeof invoices> = {
    success: true,
    data: invoices,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Sync invoices from UISP
const syncInvoicesSchema = Joi.object({
  fromDate: Joi.date().iso().optional(),
  toDate: Joi.date().iso().optional(),
  clientId: Joi.number().optional(),
  force: Joi.boolean().default(false),
  dryRun: Joi.boolean().default(false),
});

router.post('/sync/invoices', validateRequest({ body: syncInvoicesSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const result = await invoiceSyncService.syncInvoices(options);
  
  const response: ApiResponse<typeof result> = {
    success: true,
    data: result,
    message: `Synced ${result.created} new and updated ${result.updated} existing invoices`,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Sync specific invoice by ID
const syncInvoiceSchema = Joi.object({
  uispInvoiceId: Joi.number().required(),
  force: Joi.boolean().default(false),
});

router.post('/sync/invoice', validateRequest({ body: syncInvoiceSchema }), asyncHandler(async (req, res) => {
  const { uispInvoiceId, force } = req.body;
  
  const result = await invoiceSyncService.syncInvoiceById(uispInvoiceId, force);
  
  const response: ApiResponse<{ result: typeof result }> = {
    success: true,
    data: { result },
    message: `Invoice ${result === 'created' ? 'created' : result === 'updated' ? 'updated' : 'skipped'}`,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get sync statistics
router.get('/sync/stats', asyncHandler(async (req, res) => {
  const stats = await invoiceSyncService.getSyncStats();
  
  const response: ApiResponse<typeof stats> = {
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Update UISP API credentials
const updateCredentialsSchema = Joi.object({
  apiUrl: Joi.string().uri().required(),
  apiKey: Joi.string().min(10).required(),
});

router.put('/credentials', validateRequest({ body: updateCredentialsSchema }), asyncHandler(async (req, res) => {
  const { apiUrl, apiKey } = req.body;
  
  // Test connection with new credentials
  const testApi = new (uispApi.constructor as any)(apiUrl, apiKey);
  const isConnected = await testApi.testConnection();
  
  if (!isConnected) {
    return res.status(400).json({
      success: false,
      error: 'Invalid credentials - connection test failed',
      timestamp: new Date().toISOString(),
    });
  }
  
  // Update credentials
  uispApi.updateCredentials(apiUrl, apiKey);
  
  const response: ApiResponse<{
    message: string;
    apiStatus: ReturnType<typeof uispApi.getApiStatus>;
  }> = {
    success: true,
    data: {
      message: 'UISP API credentials updated successfully',
      apiStatus: uispApi.getApiStatus(),
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get UISP API status
router.get('/status', asyncHandler(async (req, res) => {
  const apiStatus = uispApi.getApiStatus();
  const isConnected = apiStatus.isConfigured ? await uispApi.testConnection() : false;
  
  const response: ApiResponse<{
    apiStatus: typeof apiStatus;
    connected: boolean;
    lastSync: Date | null;
  }> = {
    success: true,
    data: {
      apiStatus,
      connected: isConnected,
      lastSync: null, // Would need to track this in database
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
