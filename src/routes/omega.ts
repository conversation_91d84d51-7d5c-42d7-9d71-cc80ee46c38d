import { Router } from 'express';
import Joi from 'joi';

import { omegaExportService } from '@/services/omegaExport';
import type { ApiResponse } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';

const router = Router();

// Apply authentication to all omega routes
router.use(requireAuth);

// Export invoices to Omega CSV
const exportInvoicesSchema = Joi.object({
  fromDate: Joi.date().iso().optional(),
  toDate: Joi.date().iso().optional(),
  invoiceIds: Joi.array().items(Joi.number()).optional(),
  clientIds: Joi.array().items(Joi.number()).optional(),
  onlyPaid: Joi.boolean().default(false),
  onlyUnexported: Joi.boolean().default(true),
});

router.post('/export/invoices', validateRequest({ body: exportInvoicesSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const result = await omegaExportService.exportInvoices(options);
  
  const response: ApiResponse<typeof result> = {
    success: true,
    data: result,
    message: `Exported ${result.recordCount} invoice records to ${result.fileName}`,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Export payments to Omega CSV
const exportPaymentsSchema = Joi.object({
  fromDate: Joi.date().iso().optional(),
  toDate: Joi.date().iso().optional(),
  paymentIds: Joi.array().items(Joi.number()).optional(),
  invoiceIds: Joi.array().items(Joi.number()).optional(),
  onlyUnexported: Joi.boolean().default(true),
  includePartialPayments: Joi.boolean().default(false),
});

router.post('/export/payments', validateRequest({ body: exportPaymentsSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const result = await omegaExportService.exportPayments(options);
  
  const response: ApiResponse<typeof result> = {
    success: true,
    data: result,
    message: `Exported ${result.recordCount} payment records to ${result.fileName}`,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Batch export both invoices and payments
const batchExportSchema = Joi.object({
  invoiceOptions: exportInvoicesSchema.optional(),
  paymentOptions: exportPaymentsSchema.optional(),
  exportInvoices: Joi.boolean().default(true),
  exportPayments: Joi.boolean().default(true),
  cleanupOldFiles: Joi.boolean().default(false),
  daysToKeep: Joi.number().min(1).max(365).default(30),
});

router.post('/export/batch', validateRequest({ body: batchExportSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const result = await omegaExportService.batchExport(options);
  
  const exportCount = (result.invoices?.recordCount || 0) + (result.payments?.recordCount || 0);
  
  const response: ApiResponse<typeof result> = {
    success: true,
    data: result,
    message: `Batch export completed: ${exportCount} total records exported`,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get invoice export preview
router.post('/preview/invoices', validateRequest({ body: exportInvoicesSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const preview = await omegaExportService.getInvoiceExportPreview(options);
  
  const response: ApiResponse<typeof preview> = {
    success: true,
    data: preview,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get payment export preview
router.post('/preview/payments', validateRequest({ body: exportPaymentsSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const preview = await omegaExportService.getPaymentExportPreview(options);
  
  const response: ApiResponse<typeof preview> = {
    success: true,
    data: preview,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get export statistics
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = await omegaExportService.getExportStats();
  
  const response: ApiResponse<typeof stats> = {
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// List export files
router.get('/files', asyncHandler(async (req, res) => {
  const files = await omegaExportService.listExportFiles();
  
  const response: ApiResponse<typeof files> = {
    success: true,
    data: files,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get file content
const getFileSchema = Joi.object({
  fileName: Joi.string().required(),
});

router.get('/files/:fileName', validateRequest({ params: getFileSchema }), asyncHandler(async (req, res) => {
  const { fileName } = req.params as any;
  
  try {
    const content = await omegaExportService.getFileContent(fileName);
    
    // Set appropriate headers for CSV download
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    
    res.send(content);
    
  } catch (error) {
    res.status(404).json({
      success: false,
      error: error instanceof Error ? error.message : 'File not found',
      timestamp: new Date().toISOString(),
    });
  }
}));

// Delete export file
router.delete('/files/:fileName', validateRequest({ params: getFileSchema }), asyncHandler(async (req, res) => {
  const { fileName } = req.params as any;
  
  await omegaExportService.deleteExportFile(fileName);
  
  const response: ApiResponse<{ message: string }> = {
    success: true,
    data: { message: 'Export file deleted successfully' },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Validate export configuration
router.get('/validate', asyncHandler(async (req, res) => {
  const validation = await omegaExportService.validateConfiguration();
  
  const response: ApiResponse<typeof validation> = {
    success: validation.isValid,
    data: validation,
    timestamp: new Date().toISOString(),
  };

  if (!validation.isValid) {
    res.status(400);
  }

  res.json(response);
}));

// Get export status
router.get('/status', asyncHandler(async (req, res) => {
  const [stats, validation] = await Promise.all([
    omegaExportService.getExportStats(),
    omegaExportService.validateConfiguration(),
  ]);
  
  const response: ApiResponse<{
    stats: typeof stats;
    validation: typeof validation;
    isReady: boolean;
  }> = {
    success: true,
    data: {
      stats,
      validation,
      isReady: validation.isValid && validation.errors.length === 0,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
