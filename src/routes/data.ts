import { Router } from 'express';
import Jo<PERSON> from 'joi';

import db from '@/database/client';
import type { ApiResponse } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';
import { validateRequest } from '@/middleware/validation';

const router = Router();

// Get local clients with pagination and search
const getClientsSchema = Joi.object({
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(100).default(20),
  search: Joi.string().allow('').optional(),
  isActive: Joi.boolean().optional(),
});

router.get('/clients', validateRequest({ query: getClientsSchema }), asyncHandler(async (req, res) => {
  const { page, limit, search, isActive } = req.query as any;
  const offset = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  
  if (search) {
    where.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { companyName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  const [clients, totalCount] = await Promise.all([
    db.client.findMany({
      where,
      skip: offset,
      take: limit,
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' },
      ],
      include: {
        _count: {
          select: {
            invoices: true,
          },
        },
      },
    }),
    db.client.count({ where }),
  ]);

  const response: ApiResponse<{
    clients: typeof clients;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> = {
    success: true,
    data: {
      clients,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get specific client with invoices
router.get('/clients/:id', asyncHandler(async (req, res) => {
  const clientId = parseInt(req.params.id);

  if (isNaN(clientId)) {
    return res.status(400).json({
      success: false,
      error: 'Neplatné ID klienta',
      timestamp: new Date().toISOString(),
    });
  }

  const client = await db.client.findUnique({
    where: { id: clientId },
    include: {
      invoices: {
        orderBy: { createdDate: 'desc' },
        take: 10, // Last 10 invoices
      },
      _count: {
        select: {
          invoices: true,
        },
      },
    },
  });

  if (!client) {
    return res.status(404).json({
      success: false,
      error: 'Klient nebol nájdený',
      timestamp: new Date().toISOString(),
    });
  }

  const response: ApiResponse<typeof client> = {
    success: true,
    data: client,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get local invoices with pagination and filters
const getInvoicesSchema = Joi.object({
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(100).default(20),
  status: Joi.string().valid('DRAFT', 'UNPAID', 'PARTIALLY_PAID', 'PAID', 'OVERDUE', 'VOID').optional(),
  clientId: Joi.number().optional(),
  search: Joi.string().allow('').optional(),
  dateFrom: Joi.date().optional(),
  dateTo: Joi.date().optional(),
});

router.get('/invoices', validateRequest({ query: getInvoicesSchema }), asyncHandler(async (req, res) => {
  const { page, limit, status, clientId, search, dateFrom, dateTo } = req.query as any;
  const offset = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  
  if (status) {
    where.status = status;
  }

  if (clientId) {
    where.clientId = clientId;
  }

  if (search) {
    where.OR = [
      { invoiceNumber: { contains: search, mode: 'insensitive' } },
      { client: {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { companyName: { contains: search, mode: 'insensitive' } },
        ],
      }},
    ];
  }

  if (dateFrom || dateTo) {
    where.createdDate = {};
    if (dateFrom) where.createdDate.gte = new Date(dateFrom);
    if (dateTo) where.createdDate.lte = new Date(dateTo);
  }

  const [invoices, totalCount] = await Promise.all([
    db.invoice.findMany({
      where,
      skip: offset,
      take: limit,
      orderBy: { createdDate: 'desc' },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            companyName: true,
            email: true,
          },
        },
        _count: {
          select: {
            payments: true,
          },
        },
      },
    }),
    db.invoice.count({ where }),
  ]);

  const response: ApiResponse<{
    invoices: typeof invoices;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> = {
    success: true,
    data: {
      invoices,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get specific invoice with details
router.get('/invoices/:id', asyncHandler(async (req, res) => {
  const invoiceId = parseInt(req.params.id);

  if (isNaN(invoiceId)) {
    return res.status(400).json({
      success: false,
      error: 'Neplatné ID faktúry',
      timestamp: new Date().toISOString(),
    });
  }

  const invoice = await db.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      client: true,
      payments: {
        orderBy: { paymentDate: 'desc' },
      },
    },
  });

  if (!invoice) {
    return res.status(404).json({
      success: false,
      error: 'Faktúra nebola nájdená',
      timestamp: new Date().toISOString(),
    });
  }

  const response: ApiResponse<typeof invoice> = {
    success: true,
    data: invoice,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get invoice statistics
router.get('/invoices/stats', asyncHandler(async (req, res) => {
  const [
    totalInvoices,
    unpaidInvoices,
    paidInvoices,
    overdueInvoices,
    totalAmount,
    unpaidAmount,
  ] = await Promise.all([
    db.invoice.count(),
    db.invoice.count({ where: { status: 'UNPAID' } }),
    db.invoice.count({ where: { status: 'PAID' } }),
    db.invoice.count({ where: { status: 'OVERDUE' } }),
    db.invoice.aggregate({
      _sum: { totalAmount: true },
    }),
    db.invoice.aggregate({
      where: { status: { in: ['UNPAID', 'OVERDUE'] } },
      _sum: { totalAmount: true },
    }),
  ]);

  const response: ApiResponse<{
    totalInvoices: number;
    unpaidInvoices: number;
    paidInvoices: number;
    overdueInvoices: number;
    totalAmount: number;
    unpaidAmount: number;
  }> = {
    success: true,
    data: {
      totalInvoices,
      unpaidInvoices,
      paidInvoices,
      overdueInvoices,
      totalAmount: totalAmount._sum.totalAmount || 0,
      unpaidAmount: unpaidAmount._sum.totalAmount || 0,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get client statistics
router.get('/clients/stats', asyncHandler(async (req, res) => {
  const [
    totalClients,
    activeClients,
    inactiveClients,
  ] = await Promise.all([
    db.client.count(),
    db.client.count({ where: { isActive: true } }),
    db.client.count({ where: { isActive: false } }),
  ]);

  const response: ApiResponse<{
    totalClients: number;
    activeClients: number;
    inactiveClients: number;
  }> = {
    success: true,
    data: {
      totalClients,
      activeClients,
      inactiveClients,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
