import { Router } from 'express';
import multer from 'multer';
import <PERSON><PERSON> from 'joi';

import { bankImportService } from '@/services/bankImport';
import { BankParserFactory } from '@/parsers/bankParserFactory';
import type { ApiResponse, BankFileType } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';
import db from '@/database/client';

const router = Router();

// Apply authentication to all bank routes (temporarily disabled for testing)
// router.use(requireAuth);

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check if file type is supported
    const isSupported = BankParserFactory.isFileTypeSupported(file.originalname);
    if (isSupported) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'));
    }
  },
});

// Get supported file types
router.get('/supported-types', asyncHandler(async (req, res) => {
  const supportedTypes = BankParserFactory.getSupportedFileTypes();
  
  const response: ApiResponse<typeof supportedTypes> = {
    success: true,
    data: supportedTypes,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Upload and parse bank statement file
router.post('/upload', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded',
      timestamp: new Date().toISOString(),
    });
  }

  const { force = false, dryRun = false } = req.body;
  const fileType = req.body.fileType as BankFileType | undefined;

  try {
    const result = await bankImportService.importFromContent(
      req.file.originalname,
      req.file.buffer,
      {
        fileType,
        force: force === 'true',
        dryRun: dryRun === 'true',
      }
    );

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: `${dryRun === 'true' ? 'Preview: Would import' : 'Imported'} ${result.transactions.length} transactions`,
      timestamp: new Date().toISOString(),
    };

    res.json(response);

  } catch (error) {
    res.status(400).json({
      success: false,
      error: error instanceof Error ? error.message : 'Import failed',
      timestamp: new Date().toISOString(),
    });
  }
}));

// Get file preview without importing
router.post('/preview', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded',
      timestamp: new Date().toISOString(),
    });
  }

  const fileType = req.body.fileType as BankFileType | undefined;

  try {
    const preview = await bankImportService.getFilePreview(
      req.file.originalname,
      req.file.buffer,
      fileType
    );

    const response: ApiResponse<typeof preview> = {
      success: true,
      data: preview,
      timestamp: new Date().toISOString(),
    };

    res.json(response);

  } catch (error) {
    res.status(400).json({
      success: false,
      error: error instanceof Error ? error.message : 'Preview failed',
      timestamp: new Date().toISOString(),
    });
  }
}));

// Validate file without importing
router.post('/validate', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded',
      timestamp: new Date().toISOString(),
    });
  }

  const validation = bankImportService.validateFile(req.file.originalname, req.file.buffer);

  const response: ApiResponse<typeof validation> = {
    success: validation.isValid,
    data: validation,
    timestamp: new Date().toISOString(),
  };

  if (!validation.isValid) {
    res.status(400);
  }

  res.json(response);
}));

// Get bank statements
const getStatementsSchema = Joi.object({
  accountIban: Joi.string().optional(),
  fromDate: Joi.date().iso().optional(),
  toDate: Joi.date().iso().optional(),
  processed: Joi.boolean().optional(),
  limit: Joi.number().min(1).max(100).default(50),
  offset: Joi.number().min(0).default(0),
});

router.get('/statements', validateRequest({ query: getStatementsSchema }), asyncHandler(async (req, res) => {
  const { accountIban, fromDate, toDate, processed, limit, offset } = req.query as any;

  const where: any = {};
  
  if (accountIban) {
    where.accountIban = accountIban;
  }
  
  if (fromDate || toDate) {
    where.statementDate = {};
    if (fromDate) where.statementDate.gte = new Date(fromDate);
    if (toDate) where.statementDate.lte = new Date(toDate);
  }
  
  if (processed !== undefined) {
    where.processed = processed;
  }

  const statements = await db.bankStatement.findMany({
    where,
    include: {
      _count: {
        select: { transactions: true },
      },
    },
    orderBy: { statementDate: 'desc' },
    take: limit,
    skip: offset,
  });

  const response: ApiResponse<typeof statements> = {
    success: true,
    data: statements,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get specific bank statement
const getStatementSchema = Joi.object({
  id: Joi.number().required(),
});

router.get('/statements/:id', validateRequest({ params: getStatementSchema }), asyncHandler(async (req, res) => {
  const { id } = req.params as any;

  const statement = await db.bankStatement.findUnique({
    where: { id },
    include: {
      transactions: {
        orderBy: { transactionDate: 'desc' },
      },
    },
  });

  if (!statement) {
    return res.status(404).json({
      success: false,
      error: 'Bank statement not found',
      timestamp: new Date().toISOString(),
    });
  }

  const response: ApiResponse<typeof statement> = {
    success: true,
    data: statement,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Delete bank statement
router.delete('/statements/:id', validateRequest({ params: getStatementSchema }), asyncHandler(async (req, res) => {
  const { id } = req.params as any;

  await bankImportService.deleteStatement(id);

  const response: ApiResponse<{ message: string }> = {
    success: true,
    data: { message: 'Bank statement deleted successfully' },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get bank transactions
const getTransactionsSchema = Joi.object({
  statementId: Joi.number().optional(),
  debitCredit: Joi.string().valid('DBIT', 'CRDT').optional(),
  matched: Joi.boolean().optional(),
  fromDate: Joi.date().iso().optional(),
  toDate: Joi.date().iso().optional(),
  minAmount: Joi.number().optional(),
  maxAmount: Joi.number().optional(),
  counterpartyName: Joi.string().optional(),
  variableSymbol: Joi.string().optional(),
  limit: Joi.number().min(1).max(100).default(50),
  offset: Joi.number().min(0).default(0),
});

router.get('/transactions', validateRequest({ query: getTransactionsSchema }), asyncHandler(async (req, res) => {
  const {
    statementId,
    debitCredit,
    matched,
    fromDate,
    toDate,
    minAmount,
    maxAmount,
    counterpartyName,
    variableSymbol,
    limit,
    offset,
  } = req.query as any;

  const where: any = {};
  
  if (statementId) {
    where.statementId = statementId;
  }
  
  if (debitCredit) {
    where.debitCredit = debitCredit;
  }
  
  if (matched !== undefined) {
    where.matchedInvoiceId = matched ? { not: null } : null;
  }
  
  if (fromDate || toDate) {
    where.transactionDate = {};
    if (fromDate) where.transactionDate.gte = new Date(fromDate);
    if (toDate) where.transactionDate.lte = new Date(toDate);
  }
  
  if (minAmount !== undefined || maxAmount !== undefined) {
    where.amount = {};
    if (minAmount !== undefined) where.amount.gte = minAmount;
    if (maxAmount !== undefined) where.amount.lte = maxAmount;
  }
  
  if (counterpartyName) {
    where.counterpartyName = { contains: counterpartyName, mode: 'insensitive' };
  }
  
  if (variableSymbol) {
    where.variableSymbol = { contains: variableSymbol };
  }

  const transactions = await db.bankTransaction.findMany({
    where,
    include: {
      statement: {
        select: {
          accountIban: true,
          statementDate: true,
        },
      },
      matchedInvoice: {
        select: {
          id: true,
          invoiceNumber: true,
          totalAmount: true,
        },
      },
    },
    orderBy: { transactionDate: 'desc' },
    take: limit,
    skip: offset,
  });

  const response: ApiResponse<typeof transactions> = {
    success: true,
    data: transactions,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get import statistics
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = await bankImportService.getImportStats();

  const response: ApiResponse<typeof stats> = {
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
