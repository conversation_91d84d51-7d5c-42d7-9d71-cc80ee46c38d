import { Router } from 'express';
import Joi from 'joi';

import { paymentMatcherService } from '@/services/paymentMatcher';
import type { ApiResponse } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';
import db from '@/database/client';

const router = Router();

// Apply authentication to all payment routes
router.use(requireAuth);

// Match payments automatically
const matchPaymentsSchema = Joi.object({
  statementId: Joi.number().optional(),
  transactionId: Joi.number().optional(),
  invoiceId: Joi.number().optional(),
  force: Joi.boolean().default(false),
  dryRun: Joi.boolean().default(false),
  maxDaysDifference: Joi.number().min(1).max(365).default(7),
  fuzzyNameMatching: Joi.boolean().default(true),
  allowPartialMatches: Joi.boolean().default(false),
});

router.post('/match', validateRequest({ body: matchPaymentsSchema }), asyncHandler(async (req, res) => {
  const options = req.body;
  
  const results = await paymentMatcherService.matchPayments(options);
  
  const matchedCount = results.filter(r => r.invoice).length;
  
  const response: ApiResponse<typeof results> = {
    success: true,
    data: results,
    message: `${options.dryRun ? 'Preview: Would match' : 'Matched'} ${matchedCount} of ${results.length} transactions`,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Manual payment matching
const manualMatchSchema = Joi.object({
  transactionId: Joi.number().required(),
  invoiceId: Joi.number().required(),
  amount: Joi.number().positive().optional(),
});

router.post('/match/manual', validateRequest({ body: manualMatchSchema }), asyncHandler(async (req, res) => {
  const { transactionId, invoiceId, amount } = req.body;
  
  const payment = await paymentMatcherService.manualMatch(transactionId, invoiceId, amount);
  
  const response: ApiResponse<typeof payment> = {
    success: true,
    data: payment,
    message: 'Payment manually matched successfully',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Unmatch payment
const unmatchPaymentSchema = Joi.object({
  transactionId: Joi.number().required(),
});

router.post('/unmatch', validateRequest({ body: unmatchPaymentSchema }), asyncHandler(async (req, res) => {
  const { transactionId } = req.body;
  
  await paymentMatcherService.unmatchPayment(transactionId);
  
  const response: ApiResponse<{ message: string }> = {
    success: true,
    data: { message: 'Payment unmatched successfully' },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get matching statistics
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = await paymentMatcherService.getMatchingStats();
  
  const response: ApiResponse<typeof stats> = {
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get unmatched transactions
router.get('/unmatched', asyncHandler(async (req, res) => {
  const transactions = await paymentMatcherService.getUnmatchedTransactions();
  
  const response: ApiResponse<typeof transactions> = {
    success: true,
    data: transactions,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get matched payments
router.get('/matched', asyncHandler(async (req, res) => {
  const payments = await paymentMatcherService.getMatchedPayments();
  
  const response: ApiResponse<typeof payments> = {
    success: true,
    data: payments,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get payments
const getPaymentsSchema = Joi.object({
  invoiceId: Joi.number().optional(),
  bankTransactionId: Joi.number().optional(),
  fromDate: Joi.date().iso().optional(),
  toDate: Joi.date().iso().optional(),
  paymentType: Joi.string().optional(),
  omegaExported: Joi.boolean().optional(),
  limit: Joi.number().min(1).max(100).default(50),
  offset: Joi.number().min(0).default(0),
});

router.get('/', validateRequest({ query: getPaymentsSchema }), asyncHandler(async (req, res) => {
  const {
    invoiceId,
    bankTransactionId,
    fromDate,
    toDate,
    paymentType,
    omegaExported,
    limit,
    offset,
  } = req.query as any;

  const where: any = {};
  
  if (invoiceId) {
    where.invoiceId = invoiceId;
  }
  
  if (bankTransactionId) {
    where.bankTransactionId = bankTransactionId;
  }
  
  if (fromDate || toDate) {
    where.paymentDate = {};
    if (fromDate) where.paymentDate.gte = new Date(fromDate);
    if (toDate) where.paymentDate.lte = new Date(toDate);
  }
  
  if (paymentType) {
    where.paymentType = paymentType;
  }
  
  if (omegaExported !== undefined) {
    where.omegaExported = omegaExported;
  }

  const payments = await db.payment.findMany({
    where,
    include: {
      invoice: {
        select: {
          id: true,
          invoiceNumber: true,
          totalAmount: true,
          clientId: true,
        },
      },
      bankTransaction: {
        select: {
          id: true,
          amount: true,
          transactionDate: true,
          counterpartyName: true,
          variableSymbol: true,
        },
      },
    },
    orderBy: { paymentDate: 'desc' },
    take: limit,
    skip: offset,
  });

  const response: ApiResponse<typeof payments> = {
    success: true,
    data: payments,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get specific payment
const getPaymentSchema = Joi.object({
  id: Joi.number().required(),
});

router.get('/:id', validateRequest({ params: getPaymentSchema }), asyncHandler(async (req, res) => {
  const { id } = req.params as any;

  const payment = await db.payment.findUnique({
    where: { id },
    include: {
      invoice: {
        include: {
          clientMapping: true,
          items: true,
        },
      },
      bankTransaction: {
        include: {
          statement: true,
        },
      },
    },
  });

  if (!payment) {
    return res.status(404).json({
      success: false,
      error: 'Payment not found',
      timestamp: new Date().toISOString(),
    });
  }

  const response: ApiResponse<typeof payment> = {
    success: true,
    data: payment,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Delete payment
router.delete('/:id', validateRequest({ params: getPaymentSchema }), asyncHandler(async (req, res) => {
  const { id } = req.params as any;

  const payment = await db.payment.findUnique({
    where: { id },
    include: { bankTransaction: true },
  });

  if (!payment) {
    return res.status(404).json({
      success: false,
      error: 'Payment not found',
      timestamp: new Date().toISOString(),
    });
  }

  // Use unmatch service if payment has bank transaction
  if (payment.bankTransactionId) {
    await paymentMatcherService.unmatchPayment(payment.bankTransactionId);
  } else {
    // Direct delete for manual payments
    await db.payment.delete({ where: { id } });
  }

  const response: ApiResponse<{ message: string }> = {
    success: true,
    data: { message: 'Payment deleted successfully' },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Get payment suggestions for a transaction
const getSuggestionsSchema = Joi.object({
  transactionId: Joi.number().required(),
  maxSuggestions: Joi.number().min(1).max(20).default(5),
});

router.get('/suggestions/:transactionId', validateRequest({ params: getSuggestionsSchema }), asyncHandler(async (req, res) => {
  const { transactionId, maxSuggestions } = req.params as any;

  // Get the transaction
  const transaction = await db.bankTransaction.findUnique({
    where: { id: transactionId },
  });

  if (!transaction) {
    return res.status(404).json({
      success: false,
      error: 'Transaction not found',
      timestamp: new Date().toISOString(),
    });
  }

  // Run matching to get suggestions
  const results = await paymentMatcherService.matchPayments({
    transactionId,
    dryRun: true,
  });

  // Get potential matches based on amount and date
  const potentialMatches = await db.invoice.findMany({
    where: {
      status: { in: ['sent', 'overdue'] },
      totalAmount: {
        gte: transaction.amount * 0.8, // 20% tolerance
        lte: transaction.amount * 1.2,
      },
    },
    include: {
      clientMapping: true,
    },
    take: maxSuggestions,
    orderBy: {
      dueDate: 'asc',
    },
  });

  const response: ApiResponse<{
    matchResult: typeof results[0];
    potentialMatches: typeof potentialMatches;
  }> = {
    success: true,
    data: {
      matchResult: results[0],
      potentialMatches,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
