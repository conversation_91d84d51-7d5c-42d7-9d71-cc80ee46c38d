import { Router } from 'express';

import { config } from '@/config';
import db from '@/database/client';
import type { ApiResponse } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';

const router = Router();

// Health check endpoint
router.get('/health', asyncHandler(async (req, res) => {
  const response: ApiResponse<{
    status: string;
    timestamp: string;
    version: string;
    environment: string;
    database: boolean;
  }> = {
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.nodeEnv,
      database: await db.checkConnection(),
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// System status endpoint
router.get('/status', asyncHandler(async (req, res) => {
  const [stats, recentLogs] = await Promise.all([
    db.getStats(),
    db.getRecentLogs(10),
  ]);

  const response: ApiResponse<{
    system: {
      uptime: number;
      memory: NodeJS.MemoryUsage;
      environment: string;
      nodeVersion: string;
    };
    database: {
      connected: boolean;
      stats: typeof stats;
    };
    recentActivity: typeof recentLogs;
  }> = {
    success: true,
    data: {
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: config.nodeEnv,
        nodeVersion: process.version,
      },
      database: {
        connected: await db.checkConnection(),
        stats,
      },
      recentActivity: recentLogs,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// API info endpoint
router.get('/info', (req, res) => {
  const response: ApiResponse<{
    name: string;
    version: string;
    description: string;
    environment: string;
    features: string[];
    endpoints: {
      health: string;
      status: string;
      docs: string;
    };
  }> = {
    success: true,
    data: {
      name: 'UISP-Omega Bridge API',
      version: '1.0.0',
      description: 'Bridge application for synchronizing invoices and payments between UISP CRM and KROS Omega',
      environment: config.nodeEnv,
      features: [
        'Invoice synchronization from UISP CRM',
        'Bank statement processing (XML, SWIFT, XLS)',
        'Automatic payment matching',
        'Omega CSV export generation',
        'Scheduled operations',
        'Web dashboard',
      ],
      endpoints: {
        health: '/api/health',
        status: '/api/status',
        docs: '/api/docs',
      },
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
});

// Configuration endpoint (read-only for security)
router.get('/config', asyncHandler(async (req, res) => {
  const config = await db.config.findFirst();

  const response: ApiResponse<{
    autoSyncEnabled: boolean;
    syncIntervalMinutes: number;
    omegaImportPath: string;
    hasUispConfig: boolean;
  }> = {
    success: true,
    data: {
      autoSyncEnabled: config?.autoSyncEnabled || false,
      syncIntervalMinutes: config?.syncIntervalMinutes || 30,
      omegaImportPath: config?.omegaImportPath || './omega-imports',
      hasUispConfig: !!(config?.uispApiUrl && config?.uispApiKey),
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Statistics endpoint
router.get('/stats', asyncHandler(async (req, res) => {
  const [syncStats, unmatchedTransactions, unpaidInvoices] = await Promise.all([
    db.getSyncStats(),
    db.getUnmatchedTransactions(),
    db.getUnpaidInvoices(),
  ]);

  const response: ApiResponse<{
    sync: typeof syncStats;
    unmatched: {
      transactions: number;
      invoices: number;
    };
    recent: {
      unmatchedTransactions: typeof unmatchedTransactions;
      unpaidInvoices: typeof unpaidInvoices;
    };
  }> = {
    success: true,
    data: {
      sync: syncStats,
      unmatched: {
        transactions: unmatchedTransactions.length,
        invoices: unpaidInvoices.length,
      },
      recent: {
        unmatchedTransactions: unmatchedTransactions.slice(0, 5),
        unpaidInvoices: unpaidInvoices.slice(0, 5),
      },
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Logs endpoint
router.get('/logs', asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit as string) || 50;
  const operationType = req.query.operationType as string;
  const status = req.query.status as string;

  let whereClause: any = {};
  
  if (operationType) {
    whereClause.operationType = operationType;
  }
  
  if (status) {
    whereClause.status = status;
  }

  const logs = await db.operationLog.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc',
    },
    take: limit,
  });

  const response: ApiResponse<typeof logs> = {
    success: true,
    data: logs,
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Test database connection
router.get('/test-db', asyncHandler(async (req, res) => {
  const isConnected = await db.checkConnection();
  
  if (!isConnected) {
    return res.status(503).json({
      success: false,
      error: 'Database connection failed',
      timestamp: new Date().toISOString(),
    });
  }

  const response: ApiResponse<{ message: string }> = {
    success: true,
    data: { message: 'Database connection successful' },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
