import { Router } from 'express';
import Jo<PERSON> from 'joi';

import { config } from '@/config';
import db from '@/database/client';
import type { ApiResponse } from '@/types/common';
import { asyncHandler } from '@/middleware/errorHandler';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';

const router = Router();

// Apply authentication to all config routes
router.use(requireAuth);

// Get current configuration (without sensitive data)
router.get('/', asyncHandler(async (req, res) => {
  // Get configuration from database
  const dbConfig = await db.config.findFirst();
  
  const response: ApiResponse<{
    uisp: {
      hasApiUrl: boolean;
      hasApiKey: boolean;
      isConfigured: boolean;
    };
    omega: {
      importPath: string;
      hasImportPath: boolean;
    };
    sync: {
      autoSyncEnabled: boolean;
      syncIntervalMinutes: number;
    };
    database: {
      connected: boolean;
    };
    environment: string;
  }> = {
    success: true,
    data: {
      uisp: {
        hasApiUrl: !!(dbConfig?.uispApiUrl || config.uisp.apiUrl),
        hasApiKey: !!(dbConfig?.uispApiKey || config.uisp.apiKey),
        isConfigured: !!(dbConfig?.uispApiUrl || config.uisp.apiUrl) && !!(dbConfig?.uispApiKey || config.uisp.apiKey),
      },
      omega: {
        importPath: dbConfig?.omegaImportPath || config.omega.importPath,
        hasImportPath: !!(dbConfig?.omegaImportPath || config.omega.importPath),
      },
      sync: {
        autoSyncEnabled: dbConfig?.autoSyncEnabled ?? false,
        syncIntervalMinutes: dbConfig?.syncIntervalMinutes ?? 60,
      },
      database: {
        connected: await db.checkConnection(),
      },
      environment: config.nodeEnv,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Update UISP configuration
const updateUispConfigSchema = Joi.object({
  apiUrl: Joi.string().uri().required(),
  apiKey: Joi.string().min(10).required(),
});

router.put('/uisp', validateRequest({ body: updateUispConfigSchema }), asyncHandler(async (req, res) => {
  const { apiUrl, apiKey } = req.body;
  
  // Update or create configuration
  const updatedConfig = await db.config.upsert({
    where: { id: 1 }, // Assuming single config record
    update: {
      uispApiUrl: apiUrl,
      uispApiKey: apiKey,
      updatedAt: new Date(),
    },
    create: {
      uispApiUrl: apiUrl,
      uispApiKey: apiKey,
    },
  });

  const response: ApiResponse<{
    message: string;
    hasApiUrl: boolean;
    hasApiKey: boolean;
  }> = {
    success: true,
    data: {
      message: 'UISP configuration updated successfully',
      hasApiUrl: !!updatedConfig.uispApiUrl,
      hasApiKey: !!updatedConfig.uispApiKey,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Update Omega configuration
const updateOmegaConfigSchema = Joi.object({
  importPath: Joi.string().required(),
});

router.put('/omega', validateRequest({ body: updateOmegaConfigSchema }), asyncHandler(async (req, res) => {
  const { importPath } = req.body;
  
  // Update or create configuration
  const updatedConfig = await db.config.upsert({
    where: { id: 1 },
    update: {
      omegaImportPath: importPath,
      updatedAt: new Date(),
    },
    create: {
      omegaImportPath: importPath,
    },
  });

  const response: ApiResponse<{
    message: string;
    importPath: string;
  }> = {
    success: true,
    data: {
      message: 'Omega configuration updated successfully',
      importPath: updatedConfig.omegaImportPath || '',
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Update sync configuration
const updateSyncConfigSchema = Joi.object({
  autoSyncEnabled: Joi.boolean().required(),
  syncIntervalMinutes: Joi.number().min(5).max(1440).required(), // 5 minutes to 24 hours
});

router.put('/sync', validateRequest({ body: updateSyncConfigSchema }), asyncHandler(async (req, res) => {
  const { autoSyncEnabled, syncIntervalMinutes } = req.body;
  
  // Update or create configuration
  const updatedConfig = await db.config.upsert({
    where: { id: 1 },
    update: {
      autoSyncEnabled,
      syncIntervalMinutes,
      updatedAt: new Date(),
    },
    create: {
      autoSyncEnabled,
      syncIntervalMinutes,
    },
  });

  const response: ApiResponse<{
    message: string;
    autoSyncEnabled: boolean;
    syncIntervalMinutes: number;
  }> = {
    success: true,
    data: {
      message: 'Sync configuration updated successfully',
      autoSyncEnabled: updatedConfig.autoSyncEnabled,
      syncIntervalMinutes: updatedConfig.syncIntervalMinutes,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Test UISP connection with current config
router.post('/uisp/test', asyncHandler(async (req, res) => {
  const dbConfig = await db.config.findFirst();
  
  if (!dbConfig?.uispApiUrl || !dbConfig?.uispApiKey) {
    return res.status(400).json({
      success: false,
      error: 'UISP configuration not found',
      timestamp: new Date().toISOString(),
    });
  }

  try {
    // Import UispApiClient here to avoid circular dependency
    const { UispApiClient } = await import('@/services/uispApi');
    const testApi = new UispApiClient(dbConfig.uispApiUrl, dbConfig.uispApiKey);
    const isConnected = await testApi.testConnection();
    
    const response: ApiResponse<{
      connected: boolean;
      message: string;
    }> = {
      success: isConnected,
      data: {
        connected: isConnected,
        message: isConnected ? 'UISP connection successful' : 'UISP connection failed',
      },
      timestamp: new Date().toISOString(),
    };

    if (!isConnected) {
      res.status(400);
    }

    res.json(response);
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Connection test failed',
      timestamp: new Date().toISOString(),
    });
  }
}));

// Test Omega export path
router.post('/omega/test', asyncHandler(async (req, res) => {
  const dbConfig = await db.config.findFirst();
  const importPath = dbConfig?.omegaImportPath || config.omega.importPath;
  
  if (!importPath) {
    return res.status(400).json({
      success: false,
      error: 'Omega import path not configured',
      timestamp: new Date().toISOString(),
    });
  }

  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    // Test if directory exists and is writable
    await fs.access(importPath);
    
    // Try to write a test file
    const testFile = path.join(importPath, 'test_write.tmp');
    await fs.writeFile(testFile, 'test');
    await fs.unlink(testFile);
    
    const response: ApiResponse<{
      accessible: boolean;
      writable: boolean;
      message: string;
      path: string;
    }> = {
      success: true,
      data: {
        accessible: true,
        writable: true,
        message: 'Omega import path is accessible and writable',
        path: importPath,
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
    
  } catch (error) {
    res.status(400).json({
      success: false,
      error: `Omega path test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      data: {
        accessible: false,
        writable: false,
        path: importPath,
      },
      timestamp: new Date().toISOString(),
    });
  }
}));

// Get system information
router.get('/system', asyncHandler(async (req, res) => {
  const response: ApiResponse<{
    node: {
      version: string;
      platform: string;
      arch: string;
    };
    memory: NodeJS.MemoryUsage;
    uptime: number;
    environment: string;
    database: {
      connected: boolean;
      stats: any;
    };
  }> = {
    success: true,
    data: {
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      environment: config.nodeEnv,
      database: {
        connected: await db.checkConnection(),
        stats: await db.getStats(),
      },
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// Reset configuration
router.post('/reset', asyncHandler(async (req, res) => {
  // Delete all configuration
  await db.config.deleteMany();
  
  const response: ApiResponse<{ message: string }> = {
    success: true,
    data: {
      message: 'Configuration reset successfully',
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
