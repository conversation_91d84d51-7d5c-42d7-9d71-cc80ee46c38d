import { PrismaClient } from '@prisma/client';

import { logger } from '@/utils/logger';

const prisma = new PrismaClient();

async function main(): Promise<void> {
  logger.info('Starting database seeding...');

  try {
    // Create default configuration
    const existingConfig = await prisma.config.findFirst();
    if (!existingConfig) {
      await prisma.config.create({
        data: {
          uispApiUrl: process.env.UISP_API_URL || '',
          uispApiKey: process.env.UISP_API_KEY || '',
          omegaImportPath: process.env.OMEGA_IMPORT_PATH || './omega-imports',
          autoSyncEnabled: process.env.AUTO_SYNC_ENABLED === 'true',
          syncIntervalMinutes: parseInt(process.env.SYNC_INTERVAL_MINUTES || '30'),
        },
      });
      logger.info('Created default configuration');
    } else {
      logger.info('Configuration already exists, skipping...');
    }

    // Create sample client mappings (for testing)
    const sampleMappings = [
      {
        uispClientId: 1,
        omegaPartnerCode: 'PART001',
        omegaPartnerName: 'Test Partner s.r.o.',
        omegaPartnerIco: '12345678',
        omegaPartnerDic: '1234567890',
      },
      {
        uispClientId: 2,
        omegaPartnerCode: 'PART002',
        omegaPartnerName: 'Another Company a.s.',
        omegaPartnerIco: '87654321',
        omegaPartnerDic: '0987654321',
      },
    ];

    for (const mapping of sampleMappings) {
      const existing = await prisma.clientMapping.findUnique({
        where: { uispClientId: mapping.uispClientId },
      });

      if (!existing) {
        await prisma.clientMapping.create({
          data: mapping,
        });
        logger.info(`Created client mapping for UISP client ${mapping.uispClientId}`);
      }
    }

    // Create sample operation log entries
    const sampleLogs = [
      {
        operationType: 'sync_invoices',
        status: 'success',
        message: 'Successfully synced invoices from UISP CRM',
        details: JSON.stringify({ invoiceCount: 5, duration: 1500 }),
        duration: 1500,
        recordCount: 5,
      },
      {
        operationType: 'import_bank',
        status: 'success',
        message: 'Successfully imported bank statement',
        details: JSON.stringify({ fileName: 'statement_example.xml', transactionCount: 10 }),
        duration: 800,
        recordCount: 10,
      },
      {
        operationType: 'match_payments',
        status: 'success',
        message: 'Successfully matched payments with invoices',
        details: JSON.stringify({ matchedCount: 3, unmatchedCount: 2 }),
        duration: 300,
        recordCount: 5,
      },
    ];

    for (const log of sampleLogs) {
      await prisma.operationLog.create({
        data: log,
      });
    }
    logger.info('Created sample operation logs');

    // Create sample invoices (for testing)
    const clientMapping = await prisma.clientMapping.findFirst();
    if (clientMapping) {
      const sampleInvoices = [
        {
          uispInvoiceId: 1001,
          invoiceNumber: 'INV-2024-001',
          clientId: clientMapping.uispClientId,
          totalAmount: 120.0,
          currency: 'EUR',
          createdDate: new Date('2024-01-15'),
          dueDate: new Date('2024-02-15'),
          status: 'sent',
          rawData: JSON.stringify({
            id: 1001,
            number: 'INV-2024-001',
            total: 120.0,
            items: [
              { label: 'Service A', quantity: 1, price: 100.0, vatRate: 'V' },
              { label: 'Service B', quantity: 2, price: 10.0, vatRate: 'V' },
            ],
          }),
        },
        {
          uispInvoiceId: 1002,
          invoiceNumber: 'INV-2024-002',
          clientId: clientMapping.uispClientId,
          totalAmount: 240.0,
          currency: 'EUR',
          createdDate: new Date('2024-01-20'),
          dueDate: new Date('2024-02-20'),
          status: 'paid',
          paidDate: new Date('2024-02-18'),
          rawData: JSON.stringify({
            id: 1002,
            number: 'INV-2024-002',
            total: 240.0,
            items: [
              { label: 'Product X', quantity: 2, price: 100.0, vatRate: 'V' },
              { label: 'Shipping', quantity: 1, price: 20.0, vatRate: 'V' },
            ],
          }),
        },
      ];

      for (const invoice of sampleInvoices) {
        const existing = await prisma.invoice.findUnique({
          where: { uispInvoiceId: invoice.uispInvoiceId },
        });

        if (!existing) {
          const createdInvoice = await prisma.invoice.create({
            data: invoice,
          });

          // Create invoice items
          const rawData = JSON.parse(invoice.rawData);
          for (const item of rawData.items) {
            await prisma.invoiceItem.create({
              data: {
                invoiceId: createdInvoice.id,
                label: item.label,
                quantity: item.quantity,
                unitPrice: item.price,
                vatRate: item.vatRate,
                totalAmount: item.quantity * item.price,
              },
            });
          }

          logger.info(`Created sample invoice ${invoice.invoiceNumber}`);
        }
      }
    }

    // Create sample bank statement (for testing)
    const sampleStatement = {
      accountIban: '************************',
      statementDate: new Date('2024-01-31'),
      openingBalance: 1000.0,
      closingBalance: 1200.0,
      fileName: 'sample_statement.xml',
      fileType: 'XML',
      processed: true,
    };

    const existingStatement = await prisma.bankStatement.findFirst({
      where: { fileName: sampleStatement.fileName },
    });

    if (!existingStatement) {
      const createdStatement = await prisma.bankStatement.create({
        data: sampleStatement,
      });

      // Create sample transactions
      const sampleTransactions = [
        {
          statementId: createdStatement.id,
          transactionDate: new Date('2024-01-15'),
          amount: 120.0,
          currency: 'EUR',
          debitCredit: 'CRDT',
          variableSymbol: 'INV-2024-001',
          counterpartyName: 'Test Partner s.r.o.',
          counterpartyAccount: '*********************',
          description: 'Payment for invoice INV-2024-001',
        },
        {
          statementId: createdStatement.id,
          transactionDate: new Date('2024-01-20'),
          amount: 240.0,
          currency: 'EUR',
          debitCredit: 'CRDT',
          variableSymbol: 'INV-2024-002',
          counterpartyName: 'Test Partner s.r.o.',
          counterpartyAccount: '*********************',
          description: 'Payment for invoice INV-2024-002',
        },
        {
          statementId: createdStatement.id,
          transactionDate: new Date('2024-01-25'),
          amount: 80.0,
          currency: 'EUR',
          debitCredit: 'CRDT',
          variableSymbol: '12345',
          counterpartyName: 'Unknown Customer',
          counterpartyAccount: '*********************',
          description: 'Unmatched payment',
        },
      ];

      for (const transaction of sampleTransactions) {
        await prisma.bankTransaction.create({
          data: transaction,
        });
      }

      logger.info('Created sample bank statement with transactions');
    }

    logger.info('Database seeding completed successfully');
  } catch (error) {
    logger.error('Error during database seeding:', error);
    throw error;
  }
}

main()
  .catch(e => {
    logger.error('Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
