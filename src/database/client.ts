import { PrismaClient } from '@prisma/client';

import { config } from '@/config';
import { logger } from '@/utils/logger';

// Extend PrismaClient with custom methods and middleware
class DatabaseClient extends PrismaClient {
  constructor() {
    super({
      log: config.isDevelopment 
        ? ['query', 'info', 'warn', 'error']
        : ['warn', 'error'],
      errorFormat: 'pretty',
    });

    // Add middleware for logging
    this.$use(async (params, next) => {
      const start = Date.now();
      
      try {
        const result = await next(params);
        const duration = Date.now() - start;
        
        if (config.isDevelopment) {
          logger.debug('Database operation completed', {
            model: params.model,
            action: params.action,
            duration,
          });
        }
        
        return result;
      } catch (error) {
        const duration = Date.now() - start;
        
        logger.error('Database operation failed', {
          model: params.model,
          action: params.action,
          duration,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        
        throw error;
      }
    });

    // Add middleware for soft deletes (if needed in the future)
    this.$use(async (params, next) => {
      // Skip soft delete logic for now, but structure is ready
      return next(params);
    });
  }

  // Custom method to check database connection
  async checkConnection(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database connection check failed:', error);
      return false;
    }
  }

  // Custom method to get database statistics
  async getStats(): Promise<{
    invoices: number;
    bankTransactions: number;
    payments: number;
    clientMappings: number;
    operationLogs: number;
  }> {
    const [
      invoices,
      bankTransactions,
      payments,
      clientMappings,
      operationLogs,
    ] = await Promise.all([
      this.invoice.count(),
      this.bankTransaction.count(),
      this.payment.count(),
      this.clientMapping.count(),
      this.operationLog.count(),
    ]);

    return {
      invoices,
      bankTransactions,
      payments,
      clientMappings,
      operationLogs,
    };
  }

  // Custom method to clean up old logs
  async cleanupOldLogs(daysToKeep: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const result = await this.operationLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    logger.info(`Cleaned up ${result.count} old operation logs`);
    return result.count;
  }

  // Custom method to get recent operation logs
  async getRecentLogs(limit: number = 50): Promise<Array<{
    id: number;
    operationType: string;
    status: string;
    message: string;
    duration: number | null;
    recordCount: number | null;
    createdAt: Date;
  }>> {
    return this.operationLog.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
      select: {
        id: true,
        operationType: true,
        status: true,
        message: true,
        duration: true,
        recordCount: true,
        createdAt: true,
      },
    });
  }

  // Custom method to get unmatched transactions
  async getUnmatchedTransactions(): Promise<Array<{
    id: number;
    transactionDate: Date;
    amount: number;
    variableSymbol: string | null;
    counterpartyName: string | null;
    description: string | null;
  }>> {
    return this.bankTransaction.findMany({
      where: {
        matchedInvoiceId: null,
        debitCredit: 'CRDT', // Only incoming payments
      },
      orderBy: {
        transactionDate: 'desc',
      },
      select: {
        id: true,
        transactionDate: true,
        amount: true,
        variableSymbol: true,
        counterpartyName: true,
        description: true,
      },
    });
  }

  // Custom method to get unpaid invoices
  async getUnpaidInvoices(): Promise<Array<{
    id: number;
    invoiceNumber: string;
    totalAmount: number;
    dueDate: Date;
    clientMapping: {
      omegaPartnerName: string | null;
    } | null;
  }>> {
    return this.invoice.findMany({
      where: {
        status: {
          in: ['sent', 'overdue'],
        },
      },
      orderBy: {
        dueDate: 'asc',
      },
      select: {
        id: true,
        invoiceNumber: true,
        totalAmount: true,
        dueDate: true,
        clientMapping: {
          select: {
            omegaPartnerName: true,
          },
        },
      },
    });
  }

  // Custom method to get sync statistics
  async getSyncStats(): Promise<{
    totalInvoices: number;
    syncedInvoices: number;
    totalTransactions: number;
    matchedTransactions: number;
    totalPayments: number;
    exportedPayments: number;
  }> {
    const [
      totalInvoices,
      syncedInvoices,
      totalTransactions,
      matchedTransactions,
      totalPayments,
      exportedPayments,
    ] = await Promise.all([
      this.invoice.count(),
      this.invoice.count({ where: { omegaImported: true } }),
      this.bankTransaction.count(),
      this.bankTransaction.count({ where: { matchedInvoiceId: { not: null } } }),
      this.payment.count(),
      this.payment.count({ where: { omegaImported: true } }),
    ]);

    return {
      totalInvoices,
      syncedInvoices,
      totalTransactions,
      matchedTransactions,
      totalPayments,
      exportedPayments,
    };
  }

  // Graceful shutdown
  async disconnect(): Promise<void> {
    try {
      await this.$disconnect();
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
    }
  }
}

// Create singleton instance
const db = new DatabaseClient();

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await db.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await db.disconnect();
  process.exit(0);
});

export { db };
export default db;
