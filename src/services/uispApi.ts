import axios, { AxiosInstance, AxiosResponse } from 'axios';

import { config } from '@/config';
import type {
  UispInvoiceReadOnly,
  UispInvoiceNew,
  UispClient,
  UispInvoiceListParams,
  UispClientListParams,
  UispOrganization,
  UispBankAccount,
  UispTax,
} from '@/types/uisp';
import { UispApiError } from '@/utils/errors';
import { logger, logApiCall } from '@/utils/logger';
import { retry, timeout } from '@/utils/helpers';

export class UispApiClient {
  private client: AxiosInstance;
  private readonly baseUrl: string;
  private readonly apiKey: string;

  constructor(apiUrl?: string, apiKey?: string) {
    this.baseUrl = apiUrl || config.uisp.apiUrl || '';
    this.apiKey = apiKey || config.uisp.apiKey || '';

    if (!this.baseUrl || !this.apiKey) {
      throw new UispApiError('UISP API URL and API Key are required');
    }

    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000, // 30 seconds
      headers: {
        'Content-Type': 'application/json',
        'X-Auth-App-Key': this.apiKey,
      },
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('UISP API request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('UISP API request error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging and error handling
    this.client.interceptors.response.use(
      (response) => {
        logApiCall(
          response.config.method?.toUpperCase() || 'GET',
          response.config.url || '',
          response.status,
          Date.now() - (response.config as any).startTime
        );
        return response;
      },
      (error) => {
        const status = error.response?.status || 0;
        const message = error.response?.data?.message || error.message;
        
        logApiCall(
          error.config?.method?.toUpperCase() || 'GET',
          error.config?.url || '',
          status,
          Date.now() - (error.config as any).startTime,
          { error: message }
        );

        // Transform axios errors to UispApiError
        throw new UispApiError(
          message,
          status,
          error.response?.data
        );
      }
    );

    // Add timing to requests
    this.client.interceptors.request.use((config) => {
      (config as any).startTime = Date.now();
      return config;
    });
  }

  // Test API connection
  async testConnection(): Promise<boolean> {
    try {
      await this.getOrganizations();
      return true;
    } catch (error) {
      logger.error('UISP API connection test failed:', error);
      return false;
    }
  }

  // Organizations
  async getOrganizations(): Promise<UispOrganization[]> {
    const response = await this.makeRequest<UispOrganization[]>('GET', '/organizations');
    return response.data;
  }

  async getOrganization(id: number): Promise<UispOrganization> {
    const response = await this.makeRequest<UispOrganization>('GET', `/organizations/${id}`);
    return response.data;
  }

  // Clients
  async getClients(params: UispClientListParams = {}): Promise<UispClient[]> {
    const response = await this.makeRequest<UispClient[]>('GET', '/clients', { params });
    return response.data;
  }

  async getClient(id: number): Promise<UispClient> {
    const response = await this.makeRequest<UispClient>('GET', `/clients/${id}`);
    return response.data;
  }

  async createClient(client: Partial<UispClient>): Promise<UispClient> {
    const response = await this.makeRequest<UispClient>('POST', '/clients', client);
    return response.data;
  }

  async updateClient(id: number, client: Partial<UispClient>): Promise<UispClient> {
    const response = await this.makeRequest<UispClient>('PATCH', `/clients/${id}`, client);
    return response.data;
  }

  // Invoices
  async getInvoices(params: UispInvoiceListParams = {}): Promise<UispInvoiceReadOnly[]> {
    const response = await this.makeRequest<UispInvoiceReadOnly[]>('GET', '/invoices', { params });
    return response.data;
  }

  async getInvoice(id: number): Promise<UispInvoiceReadOnly> {
    const response = await this.makeRequest<UispInvoiceReadOnly>('GET', `/invoices/${id}`);
    return response.data;
  }

  async createInvoice(clientId: number, invoice: UispInvoiceNew): Promise<UispInvoiceReadOnly> {
    const response = await this.makeRequest<UispInvoiceReadOnly>(
      'POST',
      `/clients/${clientId}/invoices`,
      invoice
    );
    return response.data;
  }

  async updateInvoice(id: number, invoice: Partial<UispInvoiceNew>): Promise<UispInvoiceReadOnly> {
    const response = await this.makeRequest<UispInvoiceReadOnly>('PATCH', `/invoices/${id}`, invoice);
    return response.data;
  }

  async deleteInvoice(id: number): Promise<void> {
    await this.makeRequest('DELETE', `/invoices/${id}`);
  }

  // Get invoices for specific client
  async getClientInvoices(clientId: number, params: Omit<UispInvoiceListParams, 'clientId') = {}): Promise<UispInvoiceReadOnly[]> {
    const response = await this.makeRequest<UispInvoiceReadOnly[]>(
      'GET',
      `/clients/${clientId}/invoices`,
      { params }
    );
    return response.data;
  }

  // Get invoices by date range
  async getInvoicesByDateRange(from: Date, to: Date, params: Omit<UispInvoiceListParams, 'createdDateFrom' | 'createdDateTo'> = {}): Promise<UispInvoiceReadOnly[]> {
    return this.getInvoices({
      ...params,
      createdDateFrom: from.toISOString(),
      createdDateTo: to.toISOString(),
    });
  }

  // Get recent invoices
  async getRecentInvoices(days: number = 30, params: Omit<UispInvoiceListParams, 'createdDateFrom'> = {}): Promise<UispInvoiceReadOnly[]> {
    const from = new Date();
    from.setDate(from.getDate() - days);
    
    return this.getInvoices({
      ...params,
      createdDateFrom: from.toISOString(),
    });
  }

  // Get unpaid invoices
  async getUnpaidInvoices(params: Omit<UispInvoiceListParams, 'status'> = {}): Promise<UispInvoiceReadOnly[]> {
    return this.getInvoices({
      ...params,
      status: 1, // UNPAID
    });
  }

  // Bank accounts
  async getBankAccounts(organizationId: number): Promise<UispBankAccount[]> {
    const response = await this.makeRequest<UispBankAccount[]>(
      'GET',
      `/organizations/${organizationId}/bank-accounts`
    );
    return response.data;
  }

  // Taxes
  async getTaxes(): Promise<UispTax[]> {
    const response = await this.makeRequest<UispTax[]>('GET', '/taxes');
    return response.data;
  }

  // Batch operations
  async getInvoicesBatch(ids: number[]): Promise<UispInvoiceReadOnly[]> {
    const batchSize = 10; // UISP API might have limits
    const batches = [];
    
    for (let i = 0; i < ids.length; i += batchSize) {
      const batch = ids.slice(i, i + batchSize);
      const promises = batch.map(id => this.getInvoice(id));
      batches.push(Promise.all(promises));
    }
    
    const results = await Promise.all(batches);
    return results.flat();
  }

  async getClientsBatch(ids: number[]): Promise<UispClient[]> {
    const batchSize = 10;
    const batches = [];
    
    for (let i = 0; i < ids.length; i += batchSize) {
      const batch = ids.slice(i, i + batchSize);
      const promises = batch.map(id => this.getClient(id));
      batches.push(Promise.all(promises));
    }
    
    const results = await Promise.all(batches);
    return results.flat();
  }

  // Pagination helper
  async getAllInvoices(params: UispInvoiceListParams = {}): Promise<UispInvoiceReadOnly[]> {
    const allInvoices: UispInvoiceReadOnly[] = [];
    let offset = 0;
    const limit = params.limit || 100;
    
    while (true) {
      const invoices = await this.getInvoices({
        ...params,
        limit,
        offset,
      });
      
      allInvoices.push(...invoices);
      
      // If we got fewer invoices than the limit, we've reached the end
      if (invoices.length < limit) {
        break;
      }
      
      offset += limit;
    }
    
    return allInvoices;
  }

  async getAllClients(params: UispClientListParams = {}): Promise<UispClient[]> {
    const allClients: UispClient[] = [];
    let offset = 0;
    const limit = params.limit || 100;
    
    while (true) {
      const clients = await this.getClients({
        ...params,
        limit,
        offset,
      });
      
      allClients.push(...clients);
      
      if (clients.length < limit) {
        break;
      }
      
      offset += limit;
    }
    
    return allClients;
  }

  // Private helper method for making requests with retry logic
  private async makeRequest<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    url: string,
    data?: any,
    options?: any
  ): Promise<AxiosResponse<T>> {
    return retry(async () => {
      return timeout(
        this.client.request<T>({
          method,
          url,
          data,
          ...options,
        }),
        30000 // 30 second timeout
      );
    }, 3, 1000); // Retry 3 times with 1 second delay
  }

  // Update API credentials
  updateCredentials(apiUrl: string, apiKey: string): void {
    this.client.defaults.baseURL = apiUrl;
    this.client.defaults.headers['X-Auth-App-Key'] = apiKey;
    
    logger.info('UISP API credentials updated', {
      apiUrl: apiUrl.replace(/\/+$/, ''), // Remove trailing slashes
    });
  }

  // Get API status
  getApiStatus(): {
    baseUrl: string;
    hasApiKey: boolean;
    isConfigured: boolean;
  } {
    return {
      baseUrl: this.baseUrl,
      hasApiKey: !!this.apiKey,
      isConfigured: !!(this.baseUrl && this.apiKey),
    };
  }
}

// Create singleton instance
export const uispApi = new UispApiClient();

export default uispApi;
