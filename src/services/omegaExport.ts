import fs from 'fs/promises';
import path from 'path';

import { config } from '@/config';
import db from '@/database/client';
import { InvoiceOmegaGenerator, type InvoiceExportOptions } from '@/generators/invoiceOmegaGenerator';
import { PaymentOmegaGenerator, type PaymentExportOptions } from '@/generators/paymentOmegaGenerator';
import { OmegaDocumentType } from '@/types/common';
import { OmegaExportError, NotFoundError } from '@/utils/errors';
import { logger, logOperation } from '@/utils/logger';
import { ensureDirectoryExists } from '@/utils/helpers';

export interface OmegaExportResult {
  filePath: string;
  fileName: string;
  recordCount: number;
  fileSize: number;
  exportDate: Date;
}

export interface BatchExportOptions {
  invoiceOptions?: InvoiceExportOptions;
  paymentOptions?: PaymentExportOptions;
  exportInvoices?: boolean;
  exportPayments?: boolean;
  cleanupOldFiles?: boolean;
  daysToKeep?: number;
}

export interface ExportStats {
  totalExports: number;
  lastExportDate: Date | null;
  exportsByType: Record<string, number>;
  totalFileSize: number;
  exportPath: string;
}

export class OmegaExportService {
  private invoiceGenerator: InvoiceOmegaGenerator;
  private paymentGenerator: PaymentOmegaGenerator;

  constructor() {
    this.invoiceGenerator = new InvoiceOmegaGenerator();
    this.paymentGenerator = new PaymentOmegaGenerator();
  }

  // Export invoices to Omega CSV
  async exportInvoices(options: InvoiceExportOptions = {}): Promise<OmegaExportResult> {
    const startTime = Date.now();
    
    logOperation('export_omega_invoices', 'start', {
      options,
    });

    try {
      const result = await this.invoiceGenerator.exportInvoices(options);
      
      const fileStats = await fs.stat(result.filePath);
      const exportResult: OmegaExportResult = {
        filePath: result.filePath,
        fileName: path.basename(result.filePath),
        recordCount: result.recordCount,
        fileSize: fileStats.size,
        exportDate: new Date(),
      };

      const duration = Date.now() - startTime;
      
      logOperation('export_omega_invoices', 'success', {
        ...exportResult,
        invoiceCount: result.invoiceCount,
        duration,
      });

      logger.info('Invoice export to Omega completed', {
        ...exportResult,
        invoiceCount: result.invoiceCount,
        duration,
      });

      return exportResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('export_omega_invoices', 'error', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Export payments to Omega CSV
  async exportPayments(options: PaymentExportOptions = {}): Promise<OmegaExportResult> {
    const startTime = Date.now();
    
    logOperation('export_omega_payments', 'start', {
      options,
    });

    try {
      const result = await this.paymentGenerator.exportPayments(options);
      
      const fileStats = await fs.stat(result.filePath);
      const exportResult: OmegaExportResult = {
        filePath: result.filePath,
        fileName: path.basename(result.filePath),
        recordCount: result.paymentCount,
        fileSize: fileStats.size,
        exportDate: new Date(),
      };

      const duration = Date.now() - startTime;
      
      logOperation('export_omega_payments', 'success', {
        ...exportResult,
        paymentCount: result.paymentCount,
        totalAmount: result.totalAmount,
        duration,
      });

      logger.info('Payment export to Omega completed', {
        ...exportResult,
        paymentCount: result.paymentCount,
        totalAmount: result.totalAmount,
        duration,
      });

      return exportResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('export_omega_payments', 'error', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Batch export both invoices and payments
  async batchExport(options: BatchExportOptions = {}): Promise<{
    invoices?: OmegaExportResult;
    payments?: OmegaExportResult;
    cleanupResult?: { deletedFiles: number };
  }> {
    
    const startTime = Date.now();
    
    logOperation('export_omega_batch', 'start', {
      options,
    });

    try {
      const results: any = {};

      // Export invoices
      if (options.exportInvoices !== false) {
        try {
          results.invoices = await this.exportInvoices(options.invoiceOptions);
          logger.info('Batch export: invoices completed');
        } catch (error) {
          logger.error('Batch export: invoice export failed:', error);
          throw new OmegaExportError(`Invoice export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Export payments
      if (options.exportPayments !== false) {
        try {
          results.payments = await this.exportPayments(options.paymentOptions);
          logger.info('Batch export: payments completed');
        } catch (error) {
          logger.error('Batch export: payment export failed:', error);
          throw new OmegaExportError(`Payment export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Cleanup old files
      if (options.cleanupOldFiles) {
        try {
          const daysToKeep = options.daysToKeep || 30;
          const deletedInvoiceFiles = await this.invoiceGenerator.cleanupOldFiles(daysToKeep);
          const deletedPaymentFiles = await this.paymentGenerator.cleanupOldFiles(daysToKeep);
          
          results.cleanupResult = {
            deletedFiles: deletedInvoiceFiles + deletedPaymentFiles,
          };
          
          logger.info('Batch export: cleanup completed', results.cleanupResult);
        } catch (error) {
          logger.warn('Batch export: cleanup failed:', error);
          // Don't fail the entire operation for cleanup errors
        }
      }

      const duration = Date.now() - startTime;
      
      logOperation('export_omega_batch', 'success', {
        hasInvoices: !!results.invoices,
        hasPayments: !!results.payments,
        duration,
      });

      logger.info('Batch export to Omega completed', {
        invoiceRecords: results.invoices?.recordCount || 0,
        paymentRecords: results.payments?.recordCount || 0,
        duration,
      });

      return results;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('export_omega_batch', 'error', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Get export preview for invoices
  async getInvoiceExportPreview(options: InvoiceExportOptions = {}): Promise<{
    invoiceCount: number;
    recordCount: number;
    totalAmount: number;
    dateRange: { from: Date | null; to: Date | null };
  }> {
    return this.invoiceGenerator.getExportPreview(options);
  }

  // Get export preview for payments
  async getPaymentExportPreview(options: PaymentExportOptions = {}): Promise<{
    paymentCount: number;
    totalAmount: number;
    dateRange: { from: Date | null; to: Date | null };
    invoiceCount: number;
  }> {
    return this.paymentGenerator.getExportPreview(options);
  }

  // Get export statistics
  async getExportStats(): Promise<ExportStats> {
    try {
      await ensureDirectoryExists(config.omega.importPath);
      
      const files = await fs.readdir(config.omega.importPath);
      const csvFiles = files.filter(file => file.endsWith('.csv'));
      
      let totalFileSize = 0;
      let lastExportDate: Date | null = null;
      const exportsByType: Record<string, number> = {
        invoices: 0,
        payments: 0,
        other: 0,
      };

      for (const file of csvFiles) {
        const filePath = path.join(config.omega.importPath, file);
        const stats = await fs.stat(filePath);
        
        totalFileSize += stats.size;
        
        if (!lastExportDate || stats.mtime > lastExportDate) {
          lastExportDate = stats.mtime;
        }

        // Categorize by filename pattern
        if (file.includes('T01_Faktury')) {
          exportsByType.invoices++;
        } else if (file.includes('T13_Predajky')) {
          exportsByType.payments++;
        } else {
          exportsByType.other++;
        }
      }

      return {
        totalExports: csvFiles.length,
        lastExportDate,
        exportsByType,
        totalFileSize,
        exportPath: config.omega.importPath,
      };

    } catch (error) {
      logger.error('Error getting export stats:', error);
      return {
        totalExports: 0,
        lastExportDate: null,
        exportsByType: { invoices: 0, payments: 0, other: 0 },
        totalFileSize: 0,
        exportPath: config.omega.importPath,
      };
    }
  }

  // List export files
  async listExportFiles(): Promise<Array<{
    fileName: string;
    filePath: string;
    size: number;
    createdDate: Date;
    type: 'invoices' | 'payments' | 'other';
  }>> {
    
    try {
      await ensureDirectoryExists(config.omega.importPath);
      
      const files = await fs.readdir(config.omega.importPath);
      const csvFiles = files.filter(file => file.endsWith('.csv'));
      
      const fileList = [];
      
      for (const file of csvFiles) {
        const filePath = path.join(config.omega.importPath, file);
        const stats = await fs.stat(filePath);
        
        let type: 'invoices' | 'payments' | 'other' = 'other';
        if (file.includes('T01_Faktury')) {
          type = 'invoices';
        } else if (file.includes('T13_Predajky')) {
          type = 'payments';
        }
        
        fileList.push({
          fileName: file,
          filePath,
          size: stats.size,
          createdDate: stats.mtime,
          type,
        });
      }
      
      // Sort by creation date (newest first)
      fileList.sort((a, b) => b.createdDate.getTime() - a.createdDate.getTime());
      
      return fileList;

    } catch (error) {
      logger.error('Error listing export files:', error);
      return [];
    }
  }

  // Delete export file
  async deleteExportFile(fileName: string): Promise<void> {
    const filePath = path.join(config.omega.importPath, fileName);
    
    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
      
      logger.info('Export file deleted', { fileName, filePath });
      
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        throw new NotFoundError('Export file', fileName);
      }
      throw new OmegaExportError(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get file content for preview
  async getFileContent(fileName: string): Promise<string> {
    const filePath = path.join(config.omega.importPath, fileName);
    
    try {
      await fs.access(filePath);
      const content = await fs.readFile(filePath, 'utf-8');
      return content;
      
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        throw new NotFoundError('Export file', fileName);
      }
      throw new OmegaExportError(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Validate export configuration
  async validateConfiguration(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if export path exists and is writable
      await ensureDirectoryExists(config.omega.importPath);
      
      // Try to write a test file
      const testFile = path.join(config.omega.importPath, 'test_write.tmp');
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);
      
    } catch (error) {
      errors.push(`Export path not writable: ${config.omega.importPath}`);
    }

    // Check if there are any invoices to export
    const invoiceCount = await db.invoice.count({
      where: { omegaImported: false },
    });
    
    if (invoiceCount === 0) {
      warnings.push('No unexported invoices found');
    }

    // Check if there are any payments to export
    const paymentCount = await db.payment.count({
      where: { omegaExported: false },
    });
    
    if (paymentCount === 0) {
      warnings.push('No unexported payments found');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}

// Create singleton instance
export const omegaExportService = new OmegaExportService();

export default omegaExportService;
