import * as XLSX from 'xlsx';
import { parseStringPromise } from 'xml2js';
import crypto from 'crypto';

export interface ParsedBankTransaction {
  transactionDate: Date;
  amount: number;
  currency: string;
  debitCredit: 'DBIT' | 'CRDT';
  variableSymbol?: string;
  specificSymbol?: string;
  constantSymbol?: string;
  counterpartyName?: string;
  counterpartyAccount?: string;
  counterpartyBankCode?: string;
  reference?: string;
  description?: string;
}

export interface ParsedBankStatement {
  bankName: string;
  accountNumber: string;
  statementDate: Date;
  openingBalance: number;
  closingBalance: number;
  currency: string;
  transactions: ParsedBankTransaction[];
  fileName: string;
  fileHash: string;
}

export class BankStatementParser {
  /**
   * Parse bank statement file based on its type
   */
  static async parseFile(
    fileBuffer: Buffer,
    fileName: string,
    fileType: 'XML' | 'XLS' | 'XLSX' | 'CSV'
  ): Promise<ParsedBankStatement> {
    const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

    switch (fileType.toUpperCase()) {
      case 'XML':
        return this.parseXMLStatement(fileBuffer, fileName, fileHash);
      case 'XLS':
      case 'XLSX':
        return this.parseExcelStatement(fileBuffer, fileName, fileHash);
      case 'CSV':
        return this.parseCSVStatement(fileBuffer, fileName, fileHash);
      default:
        throw new Error(`Nepodporovaný formát súboru: ${fileType}`);
    }
  }

  /**
   * Parse Tatrabanka XML format (CAMT.053)
   */
  private static async parseXMLStatement(
    fileBuffer: Buffer,
    fileName: string,
    fileHash: string
  ): Promise<ParsedBankStatement> {
    try {
      const xmlContent = fileBuffer.toString('utf-8');
      const parsed = await parseStringPromise(xmlContent);

      // Navigate through CAMT.053 structure
      const document = parsed.Document || parsed;
      const bankToCustomerStatement = document.BkToCstmrStmt || document;
      const statement = bankToCustomerStatement.Stmt?.[0] || bankToCustomerStatement;

      // Extract account information
      const account = statement.Acct?.[0] || {};
      const accountNumber = account.Id?.[0]?.IBAN?.[0] || account.Id?.[0]?.Othr?.[0]?.Id?.[0] || '';

      // Extract balances
      const balances = statement.Bal || [];
      let openingBalance = 0;
      let closingBalance = 0;

      balances.forEach((bal: any) => {
        const amount = parseFloat(bal.Amt?.[0]?._ || bal.Amt?.[0] || '0');
        const type = bal.Tp?.[0]?.CdOrPrtry?.[0]?.Cd?.[0] || '';
        
        if (type === 'OPBD') openingBalance = amount;
        if (type === 'CLBD') closingBalance = amount;
      });

      // Extract transactions
      const entries = statement.Ntry || [];
      const transactions: ParsedBankTransaction[] = [];

      entries.forEach((entry: any) => {
        const amount = parseFloat(entry.Amt?.[0]?._ || entry.Amt?.[0] || '0');
        const currency = entry.Amt?.[0]?.$ ? entry.Amt[0].$.Ccy : 'EUR';
        const debitCredit = entry.CdtDbtInd?.[0] === 'CRDT' ? 'CRDT' : 'DBIT';
        const bookingDate = entry.BookgDt?.[0]?.Dt?.[0] || entry.ValDt?.[0]?.Dt?.[0];

        // Extract transaction details
        const details = entry.NtryDtls?.[0]?.TxDtls?.[0] || {};
        const remittanceInfo = details.RmtInf?.[0] || {};
        const relatedParties = details.RltdPties?.[0] || {};

        transactions.push({
          transactionDate: new Date(bookingDate),
          amount: Math.abs(amount),
          currency,
          debitCredit,
          variableSymbol: remittanceInfo.Ustrd?.[0]?.match(/VS:(\d+)/)?.[1],
          specificSymbol: remittanceInfo.Ustrd?.[0]?.match(/SS:(\d+)/)?.[1],
          constantSymbol: remittanceInfo.Ustrd?.[0]?.match(/KS:(\d+)/)?.[1],
          counterpartyName: relatedParties.Dbtr?.[0]?.Nm?.[0] || relatedParties.Cdtr?.[0]?.Nm?.[0],
          counterpartyAccount: relatedParties.DbtrAcct?.[0]?.Id?.[0]?.IBAN?.[0] || 
                              relatedParties.CdtrAcct?.[0]?.Id?.[0]?.IBAN?.[0],
          reference: details.Refs?.[0]?.EndToEndId?.[0],
          description: remittanceInfo.Ustrd?.[0] || entry.AddtlNtryInf?.[0],
        });
      });

      return {
        bankName: 'Tatra banka',
        accountNumber,
        statementDate: new Date(statement.CreDtTm?.[0] || Date.now()),
        openingBalance,
        closingBalance,
        currency: 'EUR',
        transactions,
        fileName,
        fileHash,
      };
    } catch (error) {
      throw new Error(`Chyba pri parsovaní XML súboru: ${error instanceof Error ? error.message : 'Neznáma chyba'}`);
    }
  }

  /**
   * Parse Excel format (XLS/XLSX)
   */
  private static async parseExcelStatement(
    fileBuffer: Buffer,
    fileName: string,
    fileHash: string
  ): Promise<ParsedBankStatement> {
    try {
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];

      // Find header row and extract column indices
      let headerRowIndex = -1;
      let columnMap: { [key: string]: number } = {};

      for (let i = 0; i < Math.min(10, data.length); i++) {
        const row = data[i];
        if (row && row.some((cell: any) => 
          typeof cell === 'string' && 
          (cell.toLowerCase().includes('dátum') || 
           cell.toLowerCase().includes('suma') || 
           cell.toLowerCase().includes('účet'))
        )) {
          headerRowIndex = i;
          row.forEach((header: any, index: number) => {
            if (typeof header === 'string') {
              const h = header.toLowerCase();
              if (h.includes('dátum')) columnMap.date = index;
              if (h.includes('suma') || h.includes('čiastka')) columnMap.amount = index;
              if (h.includes('mena')) columnMap.currency = index;
              if (h.includes('vs') || h.includes('variabilný')) columnMap.vs = index;
              if (h.includes('ss') || h.includes('špecifický')) columnMap.ss = index;
              if (h.includes('ks') || h.includes('konštantný')) columnMap.ks = index;
              if (h.includes('protiúčet') || h.includes('účet')) columnMap.account = index;
              if (h.includes('názov') || h.includes('meno')) columnMap.name = index;
              if (h.includes('popis') || h.includes('účel')) columnMap.description = index;
            }
          });
          break;
        }
      }

      if (headerRowIndex === -1) {
        throw new Error('Nenašiel sa hlavičkový riadok v Excel súbore');
      }

      // Parse transactions
      const transactions: ParsedBankTransaction[] = [];
      for (let i = headerRowIndex + 1; i < data.length; i++) {
        const row = data[i];
        if (!row || row.length === 0) continue;

        const dateValue = row[columnMap.date];
        const amountValue = row[columnMap.amount];
        
        if (!dateValue || !amountValue) continue;

        const transactionDate = typeof dateValue === 'number' 
          ? XLSX.SSF.parse_date_code(dateValue)
          : new Date(dateValue);

        const amount = Math.abs(parseFloat(amountValue.toString()));
        const debitCredit = parseFloat(amountValue.toString()) >= 0 ? 'CRDT' : 'DBIT';

        transactions.push({
          transactionDate,
          amount,
          currency: row[columnMap.currency]?.toString() || 'EUR',
          debitCredit,
          variableSymbol: row[columnMap.vs]?.toString(),
          specificSymbol: row[columnMap.ss]?.toString(),
          constantSymbol: row[columnMap.ks]?.toString(),
          counterpartyAccount: row[columnMap.account]?.toString(),
          counterpartyName: row[columnMap.name]?.toString(),
          description: row[columnMap.description]?.toString(),
        });
      }

      return {
        bankName: 'Tatra banka',
        accountNumber: 'Neznámy', // Excel usually doesn't contain account info
        statementDate: new Date(),
        openingBalance: 0,
        closingBalance: 0,
        currency: 'EUR',
        transactions,
        fileName,
        fileHash,
      };
    } catch (error) {
      throw new Error(`Chyba pri parsovaní Excel súboru: ${error instanceof Error ? error.message : 'Neznáma chyba'}`);
    }
  }

  /**
   * Parse CSV format
   */
  private static async parseCSVStatement(
    fileBuffer: Buffer,
    fileName: string,
    fileHash: string
  ): Promise<ParsedBankStatement> {
    try {
      const csvContent = fileBuffer.toString('utf-8');
      const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line);
      
      if (lines.length < 2) {
        throw new Error('CSV súbor musí obsahovať aspoň hlavičku a jeden riadok dát');
      }

      // Parse header
      const headers = lines[0].split(';').map(h => h.replace(/"/g, '').trim());
      const columnMap: { [key: string]: number } = {};

      headers.forEach((header, index) => {
        const h = header.toLowerCase();
        if (h.includes('dátum') || h.includes('date')) columnMap.date = index;
        if (h.includes('suma') || h.includes('amount') || h.includes('čiastka')) columnMap.amount = index;
        if (h.includes('mena') || h.includes('currency')) columnMap.currency = index;
        if (h.includes('vs') || h.includes('variabilný')) columnMap.vs = index;
        if (h.includes('ss') || h.includes('špecifický')) columnMap.ss = index;
        if (h.includes('ks') || h.includes('konštantný')) columnMap.ks = index;
        if (h.includes('protiúčet') || h.includes('účet')) columnMap.account = index;
        if (h.includes('názov') || h.includes('meno') || h.includes('name')) columnMap.name = index;
        if (h.includes('popis') || h.includes('účel') || h.includes('description')) columnMap.description = index;
      });

      // Parse transactions
      const transactions: ParsedBankTransaction[] = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(';').map(v => v.replace(/"/g, '').trim());
        
        if (values.length < headers.length) continue;

        const dateStr = values[columnMap.date];
        const amountStr = values[columnMap.amount];
        
        if (!dateStr || !amountStr) continue;

        const transactionDate = new Date(dateStr);
        const amount = Math.abs(parseFloat(amountStr.replace(',', '.')));
        const debitCredit = parseFloat(amountStr.replace(',', '.')) >= 0 ? 'CRDT' : 'DBIT';

        transactions.push({
          transactionDate,
          amount,
          currency: values[columnMap.currency] || 'EUR',
          debitCredit,
          variableSymbol: values[columnMap.vs],
          specificSymbol: values[columnMap.ss],
          constantSymbol: values[columnMap.ks],
          counterpartyAccount: values[columnMap.account],
          counterpartyName: values[columnMap.name],
          description: values[columnMap.description],
        });
      }

      return {
        bankName: 'Tatra banka',
        accountNumber: 'Neznámy',
        statementDate: new Date(),
        openingBalance: 0,
        closingBalance: 0,
        currency: 'EUR',
        transactions,
        fileName,
        fileHash,
      };
    } catch (error) {
      throw new Error(`Chyba pri parsovaní CSV súboru: ${error instanceof Error ? error.message : 'Neznáma chyba'}`);
    }
  }
}

export default BankStatementParser;
