import type { Invoice, InvoiceItem, ClientMapping } from '@prisma/client';

import db from '@/database/client';
import type { UispInvoiceReadOnly, UispClient, UispInvoiceStatus } from '@/types/uisp';
import type { VATRate } from '@/types/common';
import { uispApi } from './uispApi';
import { NotFoundError, ConflictError, UispApiError } from '@/utils/errors';
import { logger, logOperation } from '@/utils/logger';
import { parseDate, formatDate } from '@/utils/helpers';

export interface SyncInvoicesOptions {
  fromDate?: Date;
  toDate?: Date;
  clientId?: number;
  force?: boolean;
  dryRun?: boolean;
}

export interface SyncResult {
  processed: number;
  created: number;
  updated: number;
  skipped: number;
  errors: Array<{
    invoiceId: number;
    error: string;
  }>;
}

export class InvoiceSyncService {
  // Main sync method
  async syncInvoices(options: SyncInvoicesOptions = {}): Promise<SyncResult> {
    const startTime = Date.now();
    
    logOperation('sync_invoices', 'start', {
      options,
    });

    try {
      // Test UISP API connection
      const isConnected = await uispApi.testConnection();
      if (!isConnected) {
        throw new UispApiError('Cannot connect to UISP API');
      }

      // Get invoices from UISP
      const uispInvoices = await this.getUispInvoices(options);
      
      logger.info(`Found ${uispInvoices.length} invoices in UISP CRM`, {
        fromDate: options.fromDate,
        toDate: options.toDate,
        clientId: options.clientId,
      });

      const result: SyncResult = {
        processed: 0,
        created: 0,
        updated: 0,
        skipped: 0,
        errors: [],
      };

      // Process each invoice
      for (const uispInvoice of uispInvoices) {
        try {
          result.processed++;
          
          const syncResult = await this.syncSingleInvoice(uispInvoice, options);
          
          if (syncResult === 'created') {
            result.created++;
          } else if (syncResult === 'updated') {
            result.updated++;
          } else {
            result.skipped++;
          }
          
        } catch (error) {
          logger.error(`Error syncing invoice ${uispInvoice.id}:`, error);
          result.errors.push({
            invoiceId: uispInvoice.id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      const duration = Date.now() - startTime;
      
      logOperation('sync_invoices', 'success', {
        ...result,
        duration,
      });

      logger.info('Invoice sync completed', {
        ...result,
        duration,
      });

      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('sync_invoices', 'error', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Sync single invoice
  private async syncSingleInvoice(
    uispInvoice: UispInvoiceReadOnly,
    options: SyncInvoicesOptions
  ): Promise<'created' | 'updated' | 'skipped'> {
    
    // Check if invoice already exists
    const existingInvoice = await db.invoice.findUnique({
      where: { uispInvoiceId: uispInvoice.id },
      include: { items: true },
    });

    // Skip if exists and not forcing update
    if (existingInvoice && !options.force) {
      logger.debug(`Skipping existing invoice ${uispInvoice.number}`);
      return 'skipped';
    }

    // Skip if dry run
    if (options.dryRun) {
      logger.debug(`Dry run: would ${existingInvoice ? 'update' : 'create'} invoice ${uispInvoice.number}`);
      return existingInvoice ? 'updated' : 'created';
    }

    // Get or create client mapping
    const clientMapping = await this.getOrCreateClientMapping(uispInvoice.clientId, uispInvoice.client);

    // Prepare invoice data
    const invoiceData = {
      uispInvoiceId: uispInvoice.id,
      invoiceNumber: uispInvoice.number,
      clientId: clientMapping.uispClientId,
      totalAmount: uispInvoice.total,
      currency: uispInvoice.currencyCode,
      createdDate: new Date(uispInvoice.createdDate),
      dueDate: new Date(uispInvoice.dueDate),
      taxableSupplyDate: uispInvoice.taxableSupplyDate ? new Date(uispInvoice.taxableSupplyDate) : null,
      paidDate: uispInvoice.paidDate ? new Date(uispInvoice.paidDate) : null,
      status: this.mapUispStatus(uispInvoice.status),
      rawData: JSON.stringify(uispInvoice),
    };

    if (existingInvoice) {
      // Update existing invoice
      await db.invoice.update({
        where: { id: existingInvoice.id },
        data: {
          ...invoiceData,
          updatedAt: new Date(),
        },
      });

      // Delete existing items
      await db.invoiceItem.deleteMany({
        where: { invoiceId: existingInvoice.id },
      });

      // Create new items
      await this.createInvoiceItems(existingInvoice.id, uispInvoice.items);

      logger.debug(`Updated invoice ${uispInvoice.number}`);
      return 'updated';
      
    } else {
      // Create new invoice
      const newInvoice = await db.invoice.create({
        data: invoiceData,
      });

      // Create invoice items
      await this.createInvoiceItems(newInvoice.id, uispInvoice.items);

      logger.debug(`Created invoice ${uispInvoice.number}`);
      return 'created';
    }
  }

  // Get invoices from UISP based on options
  private async getUispInvoices(options: SyncInvoicesOptions): Promise<UispInvoiceReadOnly[]> {
    const params: any = {};

    if (options.fromDate) {
      params.createdDateFrom = options.fromDate.toISOString();
    }

    if (options.toDate) {
      params.createdDateTo = options.toDate.toISOString();
    }

    if (options.clientId) {
      params.clientId = options.clientId;
    }

    // Get all invoices (handles pagination internally)
    return uispApi.getAllInvoices(params);
  }

  // Get or create client mapping
  private async getOrCreateClientMapping(
    uispClientId: number,
    uispClient?: UispClient
  ): Promise<ClientMapping> {
    
    // Try to find existing mapping
    let clientMapping = await db.clientMapping.findUnique({
      where: { uispClientId },
    });

    if (clientMapping) {
      return clientMapping;
    }

    // Get client data from UISP if not provided
    if (!uispClient) {
      try {
        uispClient = await uispApi.getClient(uispClientId);
      } catch (error) {
        logger.warn(`Could not fetch client ${uispClientId} from UISP:`, error);
        // Create minimal mapping
        return db.clientMapping.create({
          data: {
            uispClientId,
            omegaPartnerName: `UISP Client ${uispClientId}`,
          },
        });
      }
    }

    // Create new client mapping
    clientMapping = await db.clientMapping.create({
      data: {
        uispClientId,
        omegaPartnerName: this.getClientName(uispClient),
        omegaPartnerIco: uispClient.companyRegistrationNumber || undefined,
        omegaPartnerDic: uispClient.companyTaxId || undefined,
      },
    });

    logger.info(`Created client mapping for ${this.getClientName(uispClient)}`, {
      uispClientId,
      omegaPartnerName: clientMapping.omegaPartnerName,
    });

    return clientMapping;
  }

  // Create invoice items
  private async createInvoiceItems(
    invoiceId: number,
    uispItems: UispInvoiceReadOnly['items']
  ): Promise<void> {
    
    const items = uispItems.map(item => ({
      invoiceId,
      label: item.label,
      quantity: item.quantity,
      unitPrice: item.price,
      vatRate: this.mapVatRate(item.tax1 || 0),
      totalAmount: item.total,
    }));

    await db.invoiceItem.createMany({
      data: items,
    });
  }

  // Map UISP invoice status to our status
  private mapUispStatus(uispStatus: number): string {
    switch (uispStatus) {
      case UispInvoiceStatus.DRAFT:
        return 'draft';
      case UispInvoiceStatus.UNPAID:
        return 'sent';
      case UispInvoiceStatus.PARTIAL:
        return 'sent';
      case UispInvoiceStatus.PAID:
        return 'paid';
      case UispInvoiceStatus.OVERDUE:
        return 'overdue';
      case UispInvoiceStatus.VOID:
        return 'void';
      case UispInvoiceStatus.PROFORMA:
        return 'draft';
      default:
        return 'sent';
    }
  }

  // Map VAT rate from percentage to Slovak VAT code
  private mapVatRate(vatPercentage: number): VATRate {
    switch (vatPercentage) {
      case 0:
        return VATRate.ZERO;
      case 5:
        return VATRate.LOWER2;
      case 10:
        return VATRate.LOWER;
      case 20:
        return VATRate.HIGHER;
      default:
        return VATRate.EXEMPT;
    }
  }

  // Get client display name
  private getClientName(client: UispClient): string {
    if (client.companyName) {
      return client.companyName;
    }
    
    if (client.firstName && client.lastName) {
      return `${client.firstName} ${client.lastName}`;
    }
    
    return `Client ${client.id}`;
  }

  // Sync specific invoice by ID
  async syncInvoiceById(uispInvoiceId: number, force: boolean = false): Promise<'created' | 'updated' | 'skipped'> {
    try {
      const uispInvoice = await uispApi.getInvoice(uispInvoiceId);
      return this.syncSingleInvoice(uispInvoice, { force });
    } catch (error) {
      if (error instanceof UispApiError && error.statusCode === 404) {
        throw new NotFoundError('Invoice', uispInvoiceId);
      }
      throw error;
    }
  }

  // Get sync statistics
  async getSyncStats(): Promise<{
    totalInvoices: number;
    syncedToday: number;
    pendingSync: number;
    lastSyncDate: Date | null;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [totalInvoices, syncedToday, lastSyncLog] = await Promise.all([
      db.invoice.count(),
      db.invoice.count({
        where: {
          createdAt: {
            gte: today,
          },
        },
      }),
      db.operationLog.findFirst({
        where: {
          operationType: 'sync_invoices',
          status: 'success',
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);

    // Estimate pending sync (this is approximate)
    const pendingSync = 0; // Would need to query UISP API to get accurate count

    return {
      totalInvoices,
      syncedToday,
      pendingSync,
      lastSyncDate: lastSyncLog?.createdAt || null,
    };
  }

  // Manual invoice creation (for testing or manual data entry)
  async createManualInvoice(invoiceData: {
    uispInvoiceId: number;
    invoiceNumber: string;
    clientId: number;
    totalAmount: number;
    currency?: string;
    createdDate: Date;
    dueDate: Date;
    taxableSupplyDate?: Date;
    status: string;
    items: Array<{
      label: string;
      quantity: number;
      unitPrice: number;
      vatRate: VATRate;
    }>;
  }): Promise<Invoice> {
    
    // Check if invoice already exists
    const existing = await db.invoice.findUnique({
      where: { uispInvoiceId: invoiceData.uispInvoiceId },
    });

    if (existing) {
      throw new ConflictError(`Invoice with UISP ID ${invoiceData.uispInvoiceId} already exists`);
    }

    // Ensure client mapping exists
    const clientMapping = await db.clientMapping.findUnique({
      where: { uispClientId: invoiceData.clientId },
    });

    if (!clientMapping) {
      throw new NotFoundError('Client mapping', invoiceData.clientId);
    }

    // Create invoice
    const invoice = await db.invoice.create({
      data: {
        uispInvoiceId: invoiceData.uispInvoiceId,
        invoiceNumber: invoiceData.invoiceNumber,
        clientId: invoiceData.clientId,
        totalAmount: invoiceData.totalAmount,
        currency: invoiceData.currency || 'EUR',
        createdDate: invoiceData.createdDate,
        dueDate: invoiceData.dueDate,
        taxableSupplyDate: invoiceData.taxableSupplyDate,
        status: invoiceData.status,
        rawData: JSON.stringify({ manual: true, ...invoiceData }),
      },
    });

    // Create invoice items
    const items = invoiceData.items.map(item => ({
      invoiceId: invoice.id,
      label: item.label,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      vatRate: item.vatRate,
      totalAmount: item.quantity * item.unitPrice,
    }));

    await db.invoiceItem.createMany({
      data: items,
    });

    logger.info(`Created manual invoice ${invoiceData.invoiceNumber}`, {
      invoiceId: invoice.id,
      uispInvoiceId: invoiceData.uispInvoiceId,
    });

    return invoice;
  }
}

// Create singleton instance
export const invoiceSyncService = new InvoiceSyncService();

export default invoiceSyncService;
