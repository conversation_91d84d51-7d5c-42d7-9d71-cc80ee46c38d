import { PrismaClient } from '@prisma/client';
import BankStatementParser, { ParsedBankStatement } from './bankParser';

const prisma = new PrismaClient();

export interface BankImportResult {
  success: boolean;
  statementId?: number;
  transactionsImported: number;
  duplicatesSkipped: number;
  errors: string[];
  message: string;
}

export class BankImportService {
  /**
   * Import bank statement from file buffer
   */
  static async importBankStatement(
    fileBuffer: Buffer,
    fileName: string,
    fileType: 'XML' | 'XLS' | 'XLSX' | 'CSV'
  ): Promise<BankImportResult> {
    const result: BankImportResult = {
      success: false,
      transactionsImported: 0,
      duplicatesSkipped: 0,
      errors: [],
      message: '',
    };

    try {
      // Parse the bank statement file
      const parsedStatement = await BankStatementParser.parseFile(fileBuffer, fileName, fileType);
      
      // Check if this file was already imported
      const existingStatement = await prisma.bankStatement.findUnique({
        where: { fileHash: parsedStatement.fileHash },
      });

      if (existingStatement) {
        result.errors.push('Tento súbor už bol importovaný');
        result.message = 'Súbor už existuje v databáze';
        return result;
      }

      // Create bank statement record
      const bankStatement = await prisma.bankStatement.create({
        data: {
          fileName: parsedStatement.fileName,
          fileHash: parsedStatement.fileHash,
          bankName: parsedStatement.bankName,
          accountNumber: parsedStatement.accountNumber,
          statementDate: parsedStatement.statementDate,
          openingBalance: parsedStatement.openingBalance,
          closingBalance: parsedStatement.closingBalance,
          currency: parsedStatement.currency,
          totalTransactions: parsedStatement.transactions.length,
          fileType: fileType,
        },
      });

      result.statementId = bankStatement.id;

      // Import transactions
      let importedCount = 0;
      let duplicatesCount = 0;

      for (const transaction of parsedStatement.transactions) {
        try {
          // Check for duplicate transactions (same date, amount, and description)
          const existingTransaction = await prisma.bankTransaction.findFirst({
            where: {
              transactionDate: transaction.transactionDate,
              amount: transaction.amount,
              description: transaction.description,
              variableSymbol: transaction.variableSymbol,
            },
          });

          if (existingTransaction) {
            duplicatesCount++;
            continue;
          }

          // Create transaction record
          await prisma.bankTransaction.create({
            data: {
              statementId: bankStatement.id,
              transactionDate: transaction.transactionDate,
              amount: transaction.amount,
              currency: transaction.currency,
              debitCredit: transaction.debitCredit,
              variableSymbol: transaction.variableSymbol,
              specificSymbol: transaction.specificSymbol,
              constantSymbol: transaction.constantSymbol,
              counterpartyName: transaction.counterpartyName,
              counterpartyAccount: transaction.counterpartyAccount,
              counterpartyBankCode: transaction.counterpartyBankCode,
              reference: transaction.reference,
              description: transaction.description,
            },
          });

          importedCount++;
        } catch (error) {
          result.errors.push(`Chyba pri importe transakcie: ${error instanceof Error ? error.message : 'Neznáma chyba'}`);
        }
      }

      // Update statement with actual imported count
      await prisma.bankStatement.update({
        where: { id: bankStatement.id },
        data: { 
          totalTransactions: importedCount,
          processed: true,
        },
      });

      result.success = true;
      result.transactionsImported = importedCount;
      result.duplicatesSkipped = duplicatesCount;
      result.message = `Úspešne importovaných ${importedCount} transakcií z ${parsedStatement.transactions.length}`;

      return result;
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : 'Neznáma chyba pri importe');
      result.message = 'Chyba pri importe bankového výpisu';
      return result;
    }
  }

  /**
   * Get bank statements with pagination
   */
  static async getBankStatements(page: number = 1, limit: number = 20) {
    const offset = (page - 1) * limit;

    const [statements, totalCount] = await Promise.all([
      prisma.bankStatement.findMany({
        skip: offset,
        take: limit,
        orderBy: { statementDate: 'desc' },
        include: {
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      }),
      prisma.bankStatement.count(),
    ]);

    return {
      statements,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    };
  }

  /**
   * Get bank transactions with pagination and filters
   */
  static async getBankTransactions(
    page: number = 1,
    limit: number = 20,
    filters: {
      statementId?: number;
      dateFrom?: Date;
      dateTo?: Date;
      amountFrom?: number;
      amountTo?: number;
      search?: string;
      isMatched?: boolean;
    } = {}
  ) {
    const offset = (page - 1) * limit;
    const where: any = {};

    if (filters.statementId) {
      where.statementId = filters.statementId;
    }

    if (filters.dateFrom || filters.dateTo) {
      where.transactionDate = {};
      if (filters.dateFrom) where.transactionDate.gte = filters.dateFrom;
      if (filters.dateTo) where.transactionDate.lte = filters.dateTo;
    }

    if (filters.amountFrom || filters.amountTo) {
      where.amount = {};
      if (filters.amountFrom) where.amount.gte = filters.amountFrom;
      if (filters.amountTo) where.amount.lte = filters.amountTo;
    }

    if (filters.search) {
      where.OR = [
        { description: { contains: filters.search, mode: 'insensitive' } },
        { counterpartyName: { contains: filters.search, mode: 'insensitive' } },
        { variableSymbol: { contains: filters.search, mode: 'insensitive' } },
        { reference: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (filters.isMatched !== undefined) {
      if (filters.isMatched) {
        where.matchedInvoiceId = { not: null };
      } else {
        where.matchedInvoiceId = null;
      }
    }

    const [transactions, totalCount] = await Promise.all([
      prisma.bankTransaction.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: { transactionDate: 'desc' },
        include: {
          bankStatement: {
            select: {
              id: true,
              fileName: true,
              bankName: true,
              accountNumber: true,
            },
          },
          matchedInvoice: {
            select: {
              id: true,
              invoiceNumber: true,
              totalAmount: true,
              client: {
                select: {
                  firstName: true,
                  lastName: true,
                  companyName: true,
                },
              },
            },
          },
        },
      }),
      prisma.bankTransaction.count({ where }),
    ]);

    return {
      transactions,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    };
  }

  /**
   * Match bank transaction with invoice
   */
  static async matchTransactionWithInvoice(transactionId: number, invoiceId: number) {
    try {
      const transaction = await prisma.bankTransaction.findUnique({
        where: { id: transactionId },
      });

      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
      });

      if (!transaction) {
        throw new Error('Transakcia nebola nájdená');
      }

      if (!invoice) {
        throw new Error('Faktúra nebola nájdená');
      }

      // Update transaction with matched invoice
      await prisma.bankTransaction.update({
        where: { id: transactionId },
        data: { matchedInvoiceId: invoiceId },
      });

      // Create payment record
      await prisma.payment.create({
        data: {
          invoiceId: invoiceId,
          amount: transaction.amount,
          paymentDate: transaction.transactionDate,
          paymentMethod: 'BANK_TRANSFER',
          reference: transaction.reference || transaction.variableSymbol || '',
          bankTransactionId: transactionId,
        },
      });

      return {
        success: true,
        message: 'Transakcia bola úspešne spárovaná s faktúrou',
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Chyba pri spárovaní',
      };
    }
  }

  /**
   * Auto-match transactions with invoices based on variable symbol
   */
  static async autoMatchTransactions(statementId?: number) {
    const where: any = {
      matchedInvoiceId: null,
      variableSymbol: { not: null },
    };

    if (statementId) {
      where.statementId = statementId;
    }

    const unmatchedTransactions = await prisma.bankTransaction.findMany({
      where,
      include: {
        bankStatement: true,
      },
    });

    let matchedCount = 0;
    const errors: string[] = [];

    for (const transaction of unmatchedTransactions) {
      try {
        if (!transaction.variableSymbol) continue;

        // Try to find invoice by variable symbol (assuming VS = invoice number or UISP ID)
        const invoice = await prisma.invoice.findFirst({
          where: {
            OR: [
              { invoiceNumber: transaction.variableSymbol },
              { uispId: parseInt(transaction.variableSymbol) || 0 },
            ],
          },
        });

        if (invoice) {
          const matchResult = await this.matchTransactionWithInvoice(transaction.id, invoice.id);
          if (matchResult.success) {
            matchedCount++;
          } else {
            errors.push(`Chyba pri spárovaní transakcie ${transaction.id}: ${matchResult.message}`);
          }
        }
      } catch (error) {
        errors.push(`Chyba pri automatickom spárovaní transakcie ${transaction.id}: ${error instanceof Error ? error.message : 'Neznáma chyba'}`);
      }
    }

    return {
      success: true,
      matchedCount,
      totalProcessed: unmatchedTransactions.length,
      errors,
      message: `Automaticky spárovaných ${matchedCount} z ${unmatchedTransactions.length} transakcií`,
    };
  }
}

export default BankImportService;
