import type { BankTransaction, Invoice, Payment, ClientMapping } from '@prisma/client';

import db from '@/database/client';
import { PaymentMatchingError, NotFoundError } from '@/utils/errors';
import { logger, logOperation } from '@/utils/logger';
import { normalizeString, getDaysDifference } from '@/utils/helpers';

export interface MatchingOptions {
  statementId?: number;
  transactionId?: number;
  invoiceId?: number;
  force?: boolean;
  dryRun?: boolean;
  maxDaysDifference?: number;
  fuzzyNameMatching?: boolean;
  allowPartialMatches?: boolean;
}

export interface MatchResult {
  transaction: BankTransaction;
  invoice?: Invoice;
  matchType: 'exact' | 'fuzzy' | 'manual' | 'none';
  confidence: number;
  reasons: string[];
}

export interface MatchingStats {
  totalTransactions: number;
  matchedTransactions: number;
  exactMatches: number;
  fuzzyMatches: number;
  manualMatches: number;
  unmatchedTransactions: number;
  matchingRate: number;
}

export class PaymentMatcherService {
  // Main matching method
  async matchPayments(options: MatchingOptions = {}): Promise<MatchResult[]> {
    const startTime = Date.now();
    
    logOperation('match_payments', 'start', {
      options,
    });

    try {
      // Get transactions to match
      const transactions = await this.getTransactionsToMatch(options);
      
      logger.info(`Found ${transactions.length} transactions to match`, {
        statementId: options.statementId,
        transactionId: options.transactionId,
      });

      const results: MatchResult[] = [];

      // Process each transaction
      for (const transaction of transactions) {
        try {
          const result = await this.matchSingleTransaction(transaction, options);
          results.push(result);
          
          // Create payment record if match found and not dry run
          if (result.invoice && !options.dryRun) {
            await this.createPaymentRecord(transaction, result.invoice, result.matchType);
          }
          
        } catch (error) {
          logger.error(`Error matching transaction ${transaction.id}:`, error);
          results.push({
            transaction,
            matchType: 'none',
            confidence: 0,
            reasons: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`],
          });
        }
      }

      const duration = Date.now() - startTime;
      const matchedCount = results.filter(r => r.invoice).length;
      
      logOperation('match_payments', 'success', {
        totalTransactions: results.length,
        matchedTransactions: matchedCount,
        duration,
      });

      logger.info('Payment matching completed', {
        totalTransactions: results.length,
        matchedTransactions: matchedCount,
        matchingRate: results.length > 0 ? (matchedCount / results.length) * 100 : 0,
        duration,
      });

      return results;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('match_payments', 'error', {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Match single transaction with invoices
  private async matchSingleTransaction(
    transaction: BankTransaction,
    options: MatchingOptions
  ): Promise<MatchResult> {
    
    // Skip if already matched and not forcing
    if (transaction.matchedInvoiceId && !options.force) {
      const existingInvoice = await db.invoice.findUnique({
        where: { id: transaction.matchedInvoiceId },
      });
      
      return {
        transaction,
        invoice: existingInvoice || undefined,
        matchType: 'manual', // Assume existing matches are manual
        confidence: 100,
        reasons: ['Already matched'],
      };
    }

    // Only match incoming payments (credits)
    if (transaction.debitCredit !== 'CRDT') {
      return {
        transaction,
        matchType: 'none',
        confidence: 0,
        reasons: ['Not an incoming payment'],
      };
    }

    // Try different matching strategies
    const strategies = [
      () => this.matchByVariableSymbol(transaction),
      () => this.matchByAmountAndDate(transaction, options),
      () => this.matchByCounterpartyName(transaction, options),
      () => this.matchByReference(transaction),
    ];

    for (const strategy of strategies) {
      const result = await strategy();
      if (result.invoice && result.confidence >= 70) {
        return result;
      }
    }

    // No match found
    return {
      transaction,
      matchType: 'none',
      confidence: 0,
      reasons: ['No matching invoice found'],
    };
  }

  // Strategy 1: Match by variable symbol (invoice number)
  private async matchByVariableSymbol(transaction: BankTransaction): Promise<MatchResult> {
    if (!transaction.variableSymbol) {
      return {
        transaction,
        matchType: 'none',
        confidence: 0,
        reasons: ['No variable symbol'],
      };
    }

    // Try exact match with invoice number
    const invoice = await db.invoice.findFirst({
      where: {
        invoiceNumber: transaction.variableSymbol,
        status: { in: ['sent', 'overdue'] },
      },
      include: {
        clientMapping: true,
      },
    });

    if (invoice) {
      // Check if amount matches (with small tolerance)
      const amountDifference = Math.abs(invoice.totalAmount - transaction.amount);
      const tolerance = Math.max(0.01, invoice.totalAmount * 0.01); // 1% or 1 cent
      
      if (amountDifference <= tolerance) {
        return {
          transaction,
          invoice,
          matchType: 'exact',
          confidence: 100,
          reasons: ['Variable symbol matches invoice number', 'Amount matches'],
        };
      } else {
        return {
          transaction,
          invoice,
          matchType: 'fuzzy',
          confidence: 85,
          reasons: [
            'Variable symbol matches invoice number',
            `Amount differs by ${amountDifference.toFixed(2)} ${transaction.currency}`,
          ],
        };
      }
    }

    // Try partial match (VS might contain invoice number)
    const invoices = await db.invoice.findMany({
      where: {
        status: { in: ['sent', 'overdue'] },
        invoiceNumber: {
          contains: transaction.variableSymbol,
        },
      },
      include: {
        clientMapping: true,
      },
    });

    if (invoices.length === 1) {
      const invoice = invoices[0];
      const amountDifference = Math.abs(invoice.totalAmount - transaction.amount);
      
      return {
        transaction,
        invoice,
        matchType: 'fuzzy',
        confidence: 75,
        reasons: [
          'Variable symbol partially matches invoice number',
          `Amount differs by ${amountDifference.toFixed(2)} ${transaction.currency}`,
        ],
      };
    }

    return {
      transaction,
      matchType: 'none',
      confidence: 0,
      reasons: ['Variable symbol does not match any invoice'],
    };
  }

  // Strategy 2: Match by amount and date proximity
  private async matchByAmountAndDate(
    transaction: BankTransaction,
    options: MatchingOptions
  ): Promise<MatchResult> {
    
    const maxDays = options.maxDaysDifference || 7; // Default 7 days tolerance
    
    // Find invoices with matching amount
    const invoices = await db.invoice.findMany({
      where: {
        totalAmount: transaction.amount,
        status: { in: ['sent', 'overdue'] },
        dueDate: {
          gte: new Date(transaction.transactionDate.getTime() - maxDays * 24 * 60 * 60 * 1000),
          lte: new Date(transaction.transactionDate.getTime() + maxDays * 24 * 60 * 60 * 1000),
        },
      },
      include: {
        clientMapping: true,
      },
    });

    if (invoices.length === 1) {
      const invoice = invoices[0];
      const daysDifference = getDaysDifference(transaction.transactionDate, invoice.dueDate);
      const confidence = Math.max(50, 90 - daysDifference * 5); // Decrease confidence with time difference
      
      return {
        transaction,
        invoice,
        matchType: 'fuzzy',
        confidence,
        reasons: [
          'Amount matches exactly',
          `Due date within ${daysDifference} days of payment`,
        ],
      };
    }

    if (invoices.length > 1) {
      // Multiple matches - try to narrow down by counterparty
      if (transaction.counterpartyName) {
        const bestMatch = this.findBestCounterpartyMatch(invoices, transaction.counterpartyName);
        if (bestMatch) {
          return {
            transaction,
            invoice: bestMatch,
            matchType: 'fuzzy',
            confidence: 70,
            reasons: [
              'Amount matches exactly',
              'Counterparty name similarity',
              'Multiple invoices with same amount - best guess',
            ],
          };
        }
      }
    }

    return {
      transaction,
      matchType: 'none',
      confidence: 0,
      reasons: invoices.length === 0 
        ? ['No invoices with matching amount and date range']
        : ['Multiple invoices with same amount - cannot determine match'],
    };
  }

  // Strategy 3: Match by counterparty name
  private async matchByCounterpartyName(
    transaction: BankTransaction,
    options: MatchingOptions
  ): Promise<MatchResult> {
    
    if (!transaction.counterpartyName || !options.fuzzyNameMatching) {
      return {
        transaction,
        matchType: 'none',
        confidence: 0,
        reasons: ['No counterparty name or fuzzy matching disabled'],
      };
    }

    // Get all unpaid invoices
    const invoices = await db.invoice.findMany({
      where: {
        status: { in: ['sent', 'overdue'] },
      },
      include: {
        clientMapping: true,
      },
    });

    // Find best name match
    let bestMatch: Invoice | null = null;
    let bestSimilarity = 0;

    for (const invoice of invoices) {
      if (!invoice.clientMapping?.omegaPartnerName) continue;
      
      const similarity = this.calculateNameSimilarity(
        transaction.counterpartyName,
        invoice.clientMapping.omegaPartnerName
      );

      if (similarity > bestSimilarity && similarity >= 0.7) {
        bestSimilarity = similarity;
        bestMatch = invoice;
      }
    }

    if (bestMatch) {
      // Check if amount is reasonable (within 20% difference)
      const amountDifference = Math.abs(bestMatch.totalAmount - transaction.amount);
      const amountTolerance = bestMatch.totalAmount * 0.2;
      
      if (amountDifference <= amountTolerance) {
        return {
          transaction,
          invoice: bestMatch,
          matchType: 'fuzzy',
          confidence: Math.round(bestSimilarity * 100),
          reasons: [
            `Counterparty name similarity: ${Math.round(bestSimilarity * 100)}%`,
            `Amount within acceptable range (${amountDifference.toFixed(2)} difference)`,
          ],
        };
      }
    }

    return {
      transaction,
      matchType: 'none',
      confidence: 0,
      reasons: ['No matching counterparty name found'],
    };
  }

  // Strategy 4: Match by reference or description
  private async matchByReference(transaction: BankTransaction): Promise<MatchResult> {
    const searchText = [
      transaction.reference,
      transaction.description,
    ].filter(Boolean).join(' ');

    if (!searchText) {
      return {
        transaction,
        matchType: 'none',
        confidence: 0,
        reasons: ['No reference or description'],
      };
    }

    // Look for invoice numbers in the text
    const invoiceNumberPattern = /(?:INV|FAK|F)[-\s]*(\d+)/gi;
    const matches = searchText.match(invoiceNumberPattern);

    if (matches) {
      for (const match of matches) {
        const invoiceNumber = match.replace(/[^\d]/g, '');
        
        const invoice = await db.invoice.findFirst({
          where: {
            invoiceNumber: {
              contains: invoiceNumber,
            },
            status: { in: ['sent', 'overdue'] },
          },
          include: {
            clientMapping: true,
          },
        });

        if (invoice) {
          return {
            transaction,
            invoice,
            matchType: 'fuzzy',
            confidence: 80,
            reasons: [
              `Invoice number found in reference: ${match}`,
            ],
          };
        }
      }
    }

    return {
      transaction,
      matchType: 'none',
      confidence: 0,
      reasons: ['No invoice number found in reference or description'],
    };
  }

  // Helper methods
  private async getTransactionsToMatch(options: MatchingOptions): Promise<BankTransaction[]> {
    const where: any = {
      debitCredit: 'CRDT', // Only incoming payments
    };

    if (options.transactionId) {
      where.id = options.transactionId;
    } else {
      if (options.statementId) {
        where.statementId = options.statementId;
      }

      if (!options.force) {
        where.matchedInvoiceId = null; // Only unmatched transactions
      }
    }

    return db.bankTransaction.findMany({
      where,
      orderBy: {
        transactionDate: 'desc',
      },
    });
  }

  private findBestCounterpartyMatch(invoices: Invoice[], counterpartyName: string): Invoice | null {
    let bestMatch: Invoice | null = null;
    let bestSimilarity = 0;

    for (const invoice of invoices) {
      if (!invoice.clientMapping?.omegaPartnerName) continue;

      const similarity = this.calculateNameSimilarity(
        counterpartyName,
        invoice.clientMapping.omegaPartnerName
      );

      if (similarity > bestSimilarity) {
        bestSimilarity = similarity;
        bestMatch = invoice;
      }
    }

    return bestSimilarity >= 0.6 ? bestMatch : null;
  }

  private calculateNameSimilarity(name1: string, name2: string): number {
    const normalized1 = normalizeString(name1);
    const normalized2 = normalizeString(name2);

    // Simple similarity calculation using Levenshtein distance
    const distance = this.levenshteinDistance(normalized1, normalized2);
    const maxLength = Math.max(normalized1.length, normalized2.length);

    if (maxLength === 0) return 1;

    return 1 - distance / maxLength;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private async createPaymentRecord(
    transaction: BankTransaction,
    invoice: Invoice,
    matchType: string
  ): Promise<Payment> {

    return db.$transaction(async (tx) => {
      // Create payment record
      const payment = await tx.payment.create({
        data: {
          invoiceId: invoice.id,
          bankTransactionId: transaction.id,
          amount: transaction.amount,
          paymentDate: transaction.transactionDate,
          paymentType: 'bank_transfer',
        },
      });

      // Update transaction with matched invoice
      await tx.bankTransaction.update({
        where: { id: transaction.id },
        data: { matchedInvoiceId: invoice.id },
      });

      // Update invoice status if fully paid
      const totalPaid = await tx.payment.aggregate({
        where: { invoiceId: invoice.id },
        _sum: { amount: true },
      });

      const paidAmount = totalPaid._sum.amount || 0;

      if (paidAmount >= invoice.totalAmount) {
        await tx.invoice.update({
          where: { id: invoice.id },
          data: {
            status: 'paid',
            paidDate: transaction.transactionDate,
          },
        });
      }

      logger.info('Payment record created', {
        paymentId: payment.id,
        transactionId: transaction.id,
        invoiceId: invoice.id,
        amount: transaction.amount,
        matchType,
      });

      return payment;
    });
  }

  // Manual matching
  async manualMatch(
    transactionId: number,
    invoiceId: number,
    amount?: number
  ): Promise<Payment> {

    const transaction = await db.bankTransaction.findUnique({
      where: { id: transactionId },
    });

    if (!transaction) {
      throw new NotFoundError('Bank transaction', transactionId);
    }

    const invoice = await db.invoice.findUnique({
      where: { id: invoiceId },
    });

    if (!invoice) {
      throw new NotFoundError('Invoice', invoiceId);
    }

    // Use specified amount or transaction amount
    const paymentAmount = amount || transaction.amount;

    // Validate amount
    if (paymentAmount <= 0 || paymentAmount > transaction.amount) {
      throw new PaymentMatchingError(
        `Invalid payment amount: ${paymentAmount}`,
        transactionId,
        invoiceId
      );
    }

    const payment = await this.createPaymentRecord(transaction, invoice, 'manual');

    logger.info('Manual payment match created', {
      transactionId,
      invoiceId,
      amount: paymentAmount,
    });

    return payment;
  }

  // Unmatch payment
  async unmatchPayment(transactionId: number): Promise<void> {
    const transaction = await db.bankTransaction.findUnique({
      where: { id: transactionId },
      include: { payments: true },
    });

    if (!transaction) {
      throw new NotFoundError('Bank transaction', transactionId);
    }

    if (!transaction.matchedInvoiceId) {
      throw new PaymentMatchingError('Transaction is not matched', transactionId);
    }

    await db.$transaction(async (tx) => {
      // Delete payment records
      await tx.payment.deleteMany({
        where: { bankTransactionId: transactionId },
      });

      // Update transaction
      await tx.bankTransaction.update({
        where: { id: transactionId },
        data: { matchedInvoiceId: null },
      });

      // Update invoice status back to unpaid if needed
      if (transaction.matchedInvoiceId) {
        const remainingPayments = await tx.payment.findMany({
          where: { invoiceId: transaction.matchedInvoiceId },
        });

        if (remainingPayments.length === 0) {
          await tx.invoice.update({
            where: { id: transaction.matchedInvoiceId },
            data: {
              status: 'sent',
              paidDate: null,
            },
          });
        }
      }
    });

    logger.info('Payment unmatched', {
      transactionId,
      invoiceId: transaction.matchedInvoiceId,
    });
  }

  // Get matching statistics
  async getMatchingStats(): Promise<MatchingStats> {
    const [
      totalTransactions,
      matchedTransactions,
      exactMatches,
      fuzzyMatches,
      manualMatches,
    ] = await Promise.all([
      db.bankTransaction.count({
        where: { debitCredit: 'CRDT' },
      }),
      db.bankTransaction.count({
        where: {
          debitCredit: 'CRDT',
          matchedInvoiceId: { not: null },
        },
      }),
      // These would need additional fields in the database to track match types
      // For now, we'll estimate based on available data
      db.payment.count({
        where: {
          bankTransaction: {
            variableSymbol: { not: null },
          },
        },
      }),
      db.payment.count({
        where: {
          bankTransaction: {
            variableSymbol: null,
          },
        },
      }),
      // Manual matches would need a separate tracking mechanism
      0,
    ]);

    const unmatchedTransactions = totalTransactions - matchedTransactions;
    const matchingRate = totalTransactions > 0 ? (matchedTransactions / totalTransactions) * 100 : 0;

    return {
      totalTransactions,
      matchedTransactions,
      exactMatches,
      fuzzyMatches,
      manualMatches,
      unmatchedTransactions,
      matchingRate,
    };
  }

  // Get unmatched transactions
  async getUnmatchedTransactions(): Promise<BankTransaction[]> {
    return db.bankTransaction.findMany({
      where: {
        debitCredit: 'CRDT',
        matchedInvoiceId: null,
      },
      orderBy: {
        transactionDate: 'desc',
      },
      take: 50, // Limit to recent transactions
    });
  }

  // Get matched payments
  async getMatchedPayments(): Promise<Array<Payment & {
    invoice: Invoice;
    bankTransaction: BankTransaction;
  }>> {
    return db.payment.findMany({
      include: {
        invoice: true,
        bankTransaction: true,
      },
      orderBy: {
        paymentDate: 'desc',
      },
      take: 50,
    });
  }
}

// Create singleton instance
export const paymentMatcherService = new PaymentMatcherService();

export default paymentMatcherService;
