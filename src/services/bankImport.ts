import fs from 'fs/promises';
import path from 'path';

import type { BankStatement, BankTransaction } from '@prisma/client';
import db from '@/database/client';
import type { BankFileType, BankStatementParseResult } from '@/types/bank';
import { BankParserFactory } from '@/parsers/bankParserFactory';
import { BankParsingError, ConflictError, NotFoundError } from '@/utils/errors';
import { logger, logOperation, logFileOperation } from '@/utils/logger';
import { ensureDirectoryExists, getFileSize } from '@/utils/helpers';

export interface ImportBankFileOptions {
  fileType?: BankFileType;
  force?: boolean;
  dryRun?: boolean;
}

export interface ImportResult {
  statement: BankStatement;
  transactions: BankTransaction[];
  created: boolean;
  errors?: string[];
  warnings?: string[];
}

export interface ImportStats {
  totalFiles: number;
  processedFiles: number;
  totalTransactions: number;
  errors: Array<{
    fileName: string;
    error: string;
  }>;
}

export class BankImportService {
  // Import bank statement from file path
  async importFromFile(
    filePath: string,
    options: ImportBankFileOptions = {}
  ): Promise<ImportResult> {
    
    const startTime = Date.now();
    const fileName = path.basename(filePath);
    
    logOperation('import_bank', 'start', {
      fileName,
      filePath,
      options,
    });

    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Read file content
      const content = await fs.readFile(filePath);
      const fileSize = await getFileSize(filePath);
      
      logFileOperation('read', filePath, fileSize);
      
      // Import from content
      const result = await this.importFromContent(fileName, content, options);
      
      const duration = Date.now() - startTime;
      
      logOperation('import_bank', 'success', {
        fileName,
        statementId: result.statement.id,
        transactionCount: result.transactions.length,
        created: result.created,
        duration,
      });

      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('import_bank', 'error', {
        fileName,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Import bank statement from content
  async importFromContent(
    fileName: string,
    content: string | Buffer,
    options: ImportBankFileOptions = {}
  ): Promise<ImportResult> {
    
    logger.info('Starting bank statement import', {
      fileName,
      contentSize: content.length,
      options,
    });

    try {
      // Validate file
      const validation = BankParserFactory.validateFile(fileName, content);
      if (!validation.isValid) {
        throw new BankParsingError(
          `File validation failed: ${validation.errors.join(', ')}`,
          fileName
        );
      }

      // Parse bank statement
      const parseResult = await BankParserFactory.parseFile(
        fileName,
        content,
        options.fileType
      );

      logger.info('Bank statement parsed successfully', {
        fileName,
        fileType: parseResult.statement.fileType,
        transactionCount: parseResult.transactions.length,
        hasErrors: !!parseResult.errors?.length,
      });

      // Check if statement already exists
      const existingStatement = await this.findExistingStatement(parseResult);
      
      if (existingStatement && !options.force) {
        throw new ConflictError(
          `Bank statement already exists: ${existingStatement.fileName} (${existingStatement.statementDate})`
        );
      }

      // Skip database operations in dry run
      if (options.dryRun) {
        logger.info('Dry run: would import bank statement', {
          fileName,
          transactionCount: parseResult.transactions.length,
          wouldCreate: !existingStatement,
        });

        return {
          statement: parseResult.statement as any, // Type assertion for dry run
          transactions: parseResult.transactions as any[],
          created: !existingStatement,
          errors: parseResult.errors,
          warnings: parseResult.warnings,
        };
      }

      // Import to database
      const result = await this.saveToDatabase(parseResult, existingStatement);
      
      logger.info('Bank statement imported successfully', {
        fileName,
        statementId: result.statement.id,
        transactionCount: result.transactions.length,
        created: result.created,
      });

      return result;
      
    } catch (error) {
      logger.error('Bank statement import failed', {
        fileName,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw error;
    }
  }

  // Import multiple files from directory
  async importFromDirectory(
    directoryPath: string,
    options: ImportBankFileOptions = {}
  ): Promise<ImportStats> {
    
    const startTime = Date.now();
    
    logOperation('import_bank_directory', 'start', {
      directoryPath,
      options,
    });

    try {
      // Ensure directory exists
      await fs.access(directoryPath);
      
      // Get all files in directory
      const files = await fs.readdir(directoryPath);
      const bankFiles = files.filter(file => 
        BankParserFactory.isFileTypeSupported(file)
      );

      logger.info(`Found ${bankFiles.length} bank files in directory`, {
        directoryPath,
        totalFiles: files.length,
        bankFiles: bankFiles.length,
      });

      const stats: ImportStats = {
        totalFiles: bankFiles.length,
        processedFiles: 0,
        totalTransactions: 0,
        errors: [],
      };

      // Process each file
      for (const fileName of bankFiles) {
        const filePath = path.join(directoryPath, fileName);
        
        try {
          const result = await this.importFromFile(filePath, options);
          
          stats.processedFiles++;
          stats.totalTransactions += result.transactions.length;
          
          logger.info(`Imported bank file: ${fileName}`, {
            transactionCount: result.transactions.length,
            created: result.created,
          });
          
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          stats.errors.push({
            fileName,
            error: errorMsg,
          });
          
          logger.error(`Failed to import bank file: ${fileName}`, {
            error: errorMsg,
          });
        }
      }

      const duration = Date.now() - startTime;
      
      logOperation('import_bank_directory', 'success', {
        directoryPath,
        ...stats,
        duration,
      });

      logger.info('Directory import completed', {
        directoryPath,
        ...stats,
        duration,
      });

      return stats;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logOperation('import_bank_directory', 'error', {
        directoryPath,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, error instanceof Error ? error : undefined);

      throw error;
    }
  }

  // Find existing statement in database
  private async findExistingStatement(
    parseResult: BankStatementParseResult
  ): Promise<BankStatement | null> {

    // Try to find by IBAN and statement date
    const existing = await db.bankStatement.findFirst({
      where: {
        accountIban: parseResult.statement.accountIban,
        statementDate: parseResult.statement.statementDate,
      },
    });

    if (existing) {
      return existing;
    }

    // Try to find by filename
    return db.bankStatement.findFirst({
      where: {
        fileName: parseResult.statement.fileName,
      },
    });
  }

  // Save parsed result to database
  private async saveToDatabase(
    parseResult: BankStatementParseResult,
    existingStatement?: BankStatement | null
  ): Promise<ImportResult> {

    return db.$transaction(async (tx) => {
      let statement: BankStatement;
      let created = false;

      if (existingStatement) {
        // Update existing statement
        statement = await tx.bankStatement.update({
          where: { id: existingStatement.id },
          data: {
            openingBalance: parseResult.statement.openingBalance,
            closingBalance: parseResult.statement.closingBalance,
            processed: true,
            updatedAt: new Date(),
          },
        });

        // Delete existing transactions
        await tx.bankTransaction.deleteMany({
          where: { statementId: existingStatement.id },
        });

      } else {
        // Create new statement
        statement = await tx.bankStatement.create({
          data: {
            accountIban: parseResult.statement.accountIban,
            statementDate: parseResult.statement.statementDate,
            openingBalance: parseResult.statement.openingBalance,
            closingBalance: parseResult.statement.closingBalance,
            fileName: parseResult.statement.fileName,
            fileType: parseResult.statement.fileType,
            processed: true,
          },
        });
        created = true;
      }

      // Create transactions
      const transactions: BankTransaction[] = [];

      for (const txData of parseResult.transactions) {
        const transaction = await tx.bankTransaction.create({
          data: {
            statementId: statement.id,
            transactionDate: txData.transactionDate,
            amount: txData.amount,
            currency: txData.currency,
            debitCredit: txData.debitCredit,
            variableSymbol: txData.variableSymbol,
            specificSymbol: txData.specificSymbol,
            constantSymbol: txData.constantSymbol,
            counterpartyName: txData.counterpartyName,
            counterpartyAccount: txData.counterpartyAccount,
            counterpartyBankCode: txData.counterpartyBankCode,
            reference: txData.reference,
            description: txData.description,
          },
        });

        transactions.push(transaction);
      }

      return {
        statement,
        transactions,
        created,
        errors: parseResult.errors,
        warnings: parseResult.warnings,
      };
    });
  }

  // Get import statistics
  async getImportStats(): Promise<{
    totalStatements: number;
    totalTransactions: number;
    processedToday: number;
    lastImportDate: Date | null;
    fileTypes: Array<{ type: string; count: number }>;
  }> {

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [
      totalStatements,
      totalTransactions,
      processedToday,
      lastImportLog,
      fileTypeStats,
    ] = await Promise.all([
      db.bankStatement.count(),
      db.bankTransaction.count(),
      db.bankStatement.count({
        where: {
          createdAt: {
            gte: today,
          },
        },
      }),
      db.operationLog.findFirst({
        where: {
          operationType: 'import_bank',
          status: 'success',
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      db.bankStatement.groupBy({
        by: ['fileType'],
        _count: {
          fileType: true,
        },
      }),
    ]);

    const fileTypes = fileTypeStats.map(stat => ({
      type: stat.fileType,
      count: stat._count.fileType,
    }));

    return {
      totalStatements,
      totalTransactions,
      processedToday,
      lastImportDate: lastImportLog?.createdAt || null,
      fileTypes,
    };
  }

  // Get file preview without importing
  async getFilePreview(
    fileName: string,
    content: string | Buffer,
    fileType?: BankFileType
  ): Promise<{
    fileType: BankFileType;
    accountIban?: string;
    statementDate?: Date;
    transactionCount?: number;
    fileSize: number;
    isValid: boolean;
    errors?: string[];
  }> {

    try {
      // Validate file
      const validation = BankParserFactory.validateFile(fileName, content);

      // Get preview
      const preview = await BankParserFactory.getFilePreview(fileName, content, fileType);

      return {
        ...preview,
        isValid: validation.isValid,
        errors: validation.errors.length > 0 ? validation.errors : undefined,
      };

    } catch (error) {
      return {
        fileType: fileType || 'XML',
        fileSize: content.length,
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  // Delete bank statement and its transactions
  async deleteStatement(statementId: number): Promise<void> {
    const statement = await db.bankStatement.findUnique({
      where: { id: statementId },
      include: { transactions: true },
    });

    if (!statement) {
      throw new NotFoundError('Bank statement', statementId);
    }

    await db.$transaction(async (tx) => {
      // Delete transactions first (due to foreign key constraints)
      await tx.bankTransaction.deleteMany({
        where: { statementId },
      });

      // Delete statement
      await tx.bankStatement.delete({
        where: { id: statementId },
      });
    });

    logger.info('Bank statement deleted', {
      statementId,
      fileName: statement.fileName,
      transactionCount: statement.transactions.length,
    });
  }

  // Get supported file types
  getSupportedFileTypes(): Array<{
    type: BankFileType;
    extensions: string[];
    description: string;
  }> {
    return BankParserFactory.getSupportedFileTypes();
  }

  // Validate file before import
  validateFile(fileName: string, content: string | Buffer): {
    isValid: boolean;
    errors: string[];
    detectedType?: BankFileType;
  } {
    return BankParserFactory.validateFile(fileName, content);
  }
}

// Create singleton instance
export const bankImportService = new BankImportService();

export default bankImportService;
