import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  TextField,
  InputAdornment,
  TablePagination,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

interface Client {
  id: number;
  uispId: number;
  firstName: string;
  lastName: string;
  companyName?: string;
  email: string;
  phone?: string;
  isActive: boolean;
  street1?: string;
  street2?: string;
  city?: string;
  zipCode?: string;
  uispCreatedDate: string;
  uispModifiedDate: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    invoices: number;
  };
}

interface ClientsResponse {
  success: boolean;
  data: {
    clients: Client[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export function ClientsList() {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);

  const fetchClients = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: rowsPerPage.toString(),
        search: search,
      });

      const response = await fetch(`/api/data/clients?${params}`);
      const result: ClientsResponse = await response.json();

      if (result.success) {
        setClients(result.data.clients);
        setTotalCount(result.data.pagination.total);
      } else {
        setError('Chyba pri načítavaní klientov');
      }
    } catch (err) {
      setError('Chyba pri komunikácii so serverom');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, [page, rowsPerPage, search]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0); // Reset to first page when searching
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const formatClientName = (client: Client) => {
    if (client.companyName) {
      return client.companyName;
    }
    return `${client.firstName} ${client.lastName}`.trim() || 'Bez názvu';
  };

  const formatAddress = (client: Client) => {
    const parts = [client.street1, client.street2, client.city, client.zipCode].filter(Boolean);
    return parts.join(', ') || 'Bez adresy';
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" component="h2">
            <BusinessIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Klienti ({totalCount})
          </Typography>
          
          <TextField
            size="small"
            placeholder="Hľadať klientov..."
            value={search}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
          />
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Klient</TableCell>
                    <TableCell>Kontakt</TableCell>
                    <TableCell>Adresa</TableCell>
                    <TableCell>Stav</TableCell>
                    <TableCell>Faktúry</TableCell>
                    <TableCell>UISP ID</TableCell>
                    <TableCell>Akcie</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {clients.map((client) => (
                    <TableRow key={client.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {client.companyName ? (
                            <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
                          ) : (
                            <PersonIcon sx={{ mr: 1, color: 'secondary.main' }} />
                          )}
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {formatClientName(client)}
                            </Typography>
                            {client.companyName && (client.firstName || client.lastName) && (
                              <Typography variant="caption" color="text.secondary">
                                {client.firstName} {client.lastName}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Box>
                          {client.email && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <EmailIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2">{client.email}</Typography>
                            </Box>
                          )}
                          {client.phone && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PhoneIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2">{client.phone}</Typography>
                            </Box>
                          )}
                          {!client.email && !client.phone && (
                            <Typography variant="body2" color="text.secondary">
                              Bez kontaktu
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LocationIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                          <Typography variant="body2">
                            {formatAddress(client)}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={client.isActive ? 'Aktívny' : 'Neaktívny'}
                          color={client.isActive ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={client._count.invoices}
                          color={client._count.invoices > 0 ? 'primary' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          #{client.uispId}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Tooltip title="Zobraziť detail">
                          <IconButton size="small" color="primary">
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[10, 25, 50, 100]}
              labelRowsPerPage="Riadkov na stránku:"
              labelDisplayedRows={({ from, to, count }) =>
                `${from}–${to} z ${count !== -1 ? count : `viac ako ${to}`}`
              }
            />
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default ClientsList;
