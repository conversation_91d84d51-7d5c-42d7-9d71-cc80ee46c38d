import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  TextField,
  InputAdornment,
  TablePagination,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Search as SearchIcon,
  Receipt as ReceiptIcon,
  Visibility as ViewIcon,
  Euro as EuroIcon,
  CalendarToday as CalendarIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';

interface Invoice {
  id: number;
  uispId: number;
  invoiceNumber: string;
  clientId: number;
  status: string;
  totalAmount: number;
  totalUntaxed: number;
  currency: string;
  createdDate: string;
  dueDate: string;
  emailSentDate?: string;
  omegaImported: boolean;
  omegaImportedAt?: string;
  createdAt: string;
  updatedAt: string;
  client: {
    id: number;
    firstName: string;
    lastName: string;
    companyName?: string;
    email: string;
  };
  _count: {
    payments: number;
  };
}

interface InvoicesResponse {
  success: boolean;
  data: {
    invoices: Invoice[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  DRAFT: 'default',
  UNPAID: 'warning',
  PARTIALLY_PAID: 'info',
  PAID: 'success',
  OVERDUE: 'error',
  VOID: 'default',
};

const statusLabels: Record<string, string> = {
  DRAFT: 'Koncept',
  UNPAID: 'Nezaplatená',
  PARTIALLY_PAID: 'Čiastočne zaplatená',
  PAID: 'Zaplatená',
  OVERDUE: 'Po splatnosti',
  VOID: 'Zrušená',
};

export function InvoicesList() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: (page + 1).toString(),
        limit: rowsPerPage.toString(),
        search: search,
      });

      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/data/invoices?${params}`);
      const result: InvoicesResponse = await response.json();

      if (result.success) {
        setInvoices(result.data.invoices);
        setTotalCount(result.data.pagination.total);
      } else {
        setError('Chyba pri načítavaní faktúr');
      }
    } catch (err) {
      setError('Chyba pri komunikácii so serverom');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvoices();
  }, [page, rowsPerPage, search, statusFilter]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleStatusFilterChange = (event: any) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const formatClientName = (client: Invoice['client']) => {
    if (client.companyName) {
      return client.companyName;
    }
    return `${client.firstName} ${client.lastName}`.trim() || 'Bez názvu';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sk-SK');
  };

  const formatCurrency = (amount: number, currency: string = 'EUR') => {
    return new Intl.NumberFormat('sk-SK', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" component="h2">
            <ReceiptIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Faktúry ({totalCount})
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Stav</InputLabel>
              <Select
                value={statusFilter}
                label="Stav"
                onChange={handleStatusFilterChange}
              >
                <MenuItem value="">Všetky</MenuItem>
                <MenuItem value="UNPAID">Nezaplatené</MenuItem>
                <MenuItem value="PAID">Zaplatené</MenuItem>
                <MenuItem value="OVERDUE">Po splatnosti</MenuItem>
                <MenuItem value="PARTIALLY_PAID">Čiastočne zaplatené</MenuItem>
                <MenuItem value="DRAFT">Koncepty</MenuItem>
                <MenuItem value="VOID">Zrušené</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              size="small"
              placeholder="Hľadať faktúry..."
              value={search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Číslo faktúry</TableCell>
                    <TableCell>Klient</TableCell>
                    <TableCell>Suma</TableCell>
                    <TableCell>Stav</TableCell>
                    <TableCell>Vytvorená</TableCell>
                    <TableCell>Splatnosť</TableCell>
                    <TableCell>Omega</TableCell>
                    <TableCell>Akcie</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invoices.map((invoice) => (
                    <TableRow key={invoice.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <ReceiptIcon sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {invoice.invoiceNumber}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              UISP #{invoice.uispId}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {invoice.client.companyName ? (
                            <BusinessIcon sx={{ mr: 1, color: 'primary.main', fontSize: 18 }} />
                          ) : (
                            <PersonIcon sx={{ mr: 1, color: 'secondary.main', fontSize: 18 }} />
                          )}
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {formatClientName(invoice.client)}
                            </Typography>
                            {invoice.client.email && (
                              <Typography variant="caption" color="text.secondary">
                                {invoice.client.email}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <EuroIcon sx={{ mr: 0.5, color: 'success.main', fontSize: 18 }} />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {formatCurrency(invoice.totalAmount, invoice.currency)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              bez DPH: {formatCurrency(invoice.totalUntaxed, invoice.currency)}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={statusLabels[invoice.status] || invoice.status}
                          color={statusColors[invoice.status] || 'default'}
                          size="small"
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <CalendarIcon sx={{ mr: 0.5, color: 'text.secondary', fontSize: 16 }} />
                          <Typography variant="body2">
                            {formatDate(invoice.createdDate)}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <CalendarIcon sx={{ mr: 0.5, color: 'text.secondary', fontSize: 16 }} />
                          <Typography variant="body2">
                            {formatDate(invoice.dueDate)}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={invoice.omegaImported ? 'Importované' : 'Nie'}
                          color={invoice.omegaImported ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Tooltip title="Zobraziť detail">
                          <IconButton size="small" color="primary">
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[10, 25, 50, 100]}
              labelRowsPerPage="Riadkov na stránku:"
              labelDisplayedRows={({ from, to, count }) =>
                `${from}–${to} z ${count !== -1 ? count : `viac ako ${to}`}`
              }
            />
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default InvoicesList;
