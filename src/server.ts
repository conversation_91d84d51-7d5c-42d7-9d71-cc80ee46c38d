import compression from 'compression';
import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';

import { config } from '@/config';
import db from '@/database/client';
import {
  error<PERSON><PERSON><PERSON>,
  not<PERSON><PERSON>nd<PERSON><PERSON><PERSON>,
  validation<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  databaseError<PERSON><PERSON><PERSON>,
  corsError<PERSON><PERSON><PERSON>,
  multerErrorHandler,
} from '@/middleware/errorHandler';
import { optionalAuthenticate } from '@/middleware/auth';
import indexRoutes from './routes/index';
import testBankRoutes from './routes/testBank';
import { logger, httpLogStream } from '@/utils/logger';
import { ensureDirectoryExists } from '@/utils/helpers';

// Create Express application
const app = express();

// Trust proxy (for proper IP detection behind reverse proxy)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: config.isDevelopment 
    ? ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173']
    : process.env.ALLOWED_ORIGINS?.split(',') || false,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    success: false,
    error: 'Too many requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks in development
    return config.isDevelopment && req.path === '/api/health';
  },
});

app.use('/api', limiter);

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
if (!config.isTest) {
  const morgan = require('morgan');
  app.use(morgan('combined', { stream: httpLogStream }));
}

// Optional authentication for all routes (adds user context if token is present)
app.use(optionalAuthenticate);

// API routes
console.log('Registering API routes...');
app.use('/api', indexRoutes);
console.log('API routes registered');

// Test bank routes
console.log('Registering test bank routes...');
app.use('/api/bank', testBankRoutes);
console.log('Test bank routes registered');

// Serve static files in production
if (config.isProduction) {
  const staticPath = path.join(__dirname, '../public');
  app.use(express.static(staticPath));
  
  // Serve React app for all non-API routes
  app.get('*', (req, res) => {
    res.sendFile(path.join(staticPath, 'index.html'));
  });
} else {
  // Development welcome page
  app.get('/', (req, res) => {
    res.json({
      success: true,
      message: 'UISP-Omega Bridge API',
      version: '1.0.0',
      environment: config.nodeEnv,
      documentation: '/api/info',
      health: '/api/health',
      status: '/api/status',
    });
  });
}

// Error handling middleware (order matters!)
app.use(corsErrorHandler);
app.use(multerErrorHandler);
app.use(validationErrorHandler);
app.use(databaseErrorHandler);
app.use(notFoundHandler);
app.use(errorHandler);

// Graceful shutdown handler
const gracefulShutdown = async (signal: string): Promise<void> => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  try {
    // Close database connection
    await db.disconnect();
    
    // Close server
    server.close(() => {
      logger.info('Server closed successfully');
      process.exit(0);
    });
    
    // Force close after 10 seconds
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 10000);
    
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Initialize application
const initializeApp = async (): Promise<void> => {
  try {
    // Ensure required directories exist
    await ensureDirectoryExists(config.omega.importPath);
    await ensureDirectoryExists(config.upload.uploadPath);
    await ensureDirectoryExists(path.dirname(config.logging.file));
    
    // Test database connection
    const isDbConnected = await db.checkConnection();
    if (!isDbConnected) {
      throw new Error('Database connection failed');
    }
    
    logger.info('Application initialized successfully', {
      environment: config.nodeEnv,
      port: config.port,
      database: 'connected',
    });
    
  } catch (error) {
    logger.error('Application initialization failed:', error);
    process.exit(1);
  }
};

// Start server
const server = app.listen(config.port, async () => {
  await initializeApp();
  
  logger.info(`🚀 Server running on port ${config.port}`, {
    environment: config.nodeEnv,
    port: config.port,
    pid: process.pid,
  });
  
  if (config.isDevelopment) {
    logger.info('📚 API Documentation available at: http://localhost:' + config.port + '/api/info');
    logger.info('🏥 Health check available at: http://localhost:' + config.port + '/api/health');
    logger.info('📊 Status dashboard available at: http://localhost:' + config.port + '/api/status');
  }
});

// Export app for testing
export default app;
