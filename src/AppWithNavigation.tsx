import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Business as BusinessIcon,
  Receipt as ReceiptIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Sync as SyncIcon,
  AccountBalance as BankIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';

// Import pages
import ClientsPage from './pages/ClientsPage';
import InvoicesPage from './pages/InvoicesPage';

// We'll create a simple dashboard component instead of importing the complex one

const drawerWidth = 240;

interface NavigationItem {
  text: string;
  icon: React.ReactElement;
  path: string;
}

const navigationItems: NavigationItem[] = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
  { text: 'Klienti', icon: <BusinessIcon />, path: '/clients' },
  { text: 'Faktúry', icon: <ReceiptIcon />, path: '/invoices' },
  { text: 'Synchronizácia', icon: <SyncIcon />, path: '/sync' },
  { text: 'Bankové výpisy', icon: <BankIcon />, path: '/bank' },
  { text: 'Platby', icon: <PaymentIcon />, path: '/payments' },
  { text: 'Nastavenia', icon: <SettingsIcon />, path: '/settings' },
];

function NavigationDrawer({ 
  open, 
  onClose, 
  variant = 'temporary' 
}: { 
  open: boolean; 
  onClose: () => void; 
  variant?: 'temporary' | 'permanent';
}) {
  const location = useLocation();

  const drawer = (
    <Box>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          UISP-Omega Bridge
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              component={Link}
              to={item.path}
              selected={location.pathname === item.path}
              onClick={variant === 'temporary' ? onClose : undefined}
            >
              <ListItemIcon>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Drawer
      variant={variant}
      open={open}
      onClose={onClose}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      {drawer}
    </Drawer>
  );
}

function DashboardPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" gutterBottom>
        Vitajte v UISP-Omega Bridge aplikácii!
      </Typography>
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Rýchle akcie:
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Link to="/clients" style={{ textDecoration: 'none' }}>
            <Box sx={{
              p: 2,
              border: 1,
              borderColor: 'primary.main',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': { bgcolor: 'primary.light', color: 'white' }
            }}>
              <BusinessIcon sx={{ mr: 1 }} />
              Zobraziť klientov
            </Box>
          </Link>
          <Link to="/invoices" style={{ textDecoration: 'none' }}>
            <Box sx={{
              p: 2,
              border: 1,
              borderColor: 'primary.main',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': { bgcolor: 'primary.light', color: 'white' }
            }}>
              <ReceiptIcon sx={{ mr: 1 }} />
              Zobraziť faktúry
            </Box>
          </Link>
        </Box>
      </Box>
    </Box>
  );
}

function SyncPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Synchronizácia
      </Typography>
      <Typography variant="body1">
        Stránka pre synchronizáciu dát z UISP do lokálnej databázy.
      </Typography>
    </Box>
  );
}

function BankPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Bankové výpisy
      </Typography>
      <Typography variant="body1">
        Stránka pre import a správu bankových výpisov.
      </Typography>
    </Box>
  );
}

function PaymentsPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Platby
      </Typography>
      <Typography variant="body1">
        Stránka pre párovanie platieb s faktúrami.
      </Typography>
    </Box>
  );
}

function SettingsPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Nastavenia
      </Typography>
      <Typography variant="body1">
        Stránka pre konfiguráciu aplikácie.
      </Typography>
    </Box>
  );
}

function AppWithNavigation() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Router>
      <Box sx={{ display: 'flex' }}>
        <AppBar
          position="fixed"
          sx={{
            width: { md: `calc(100% - ${drawerWidth}px)` },
            ml: { md: `${drawerWidth}px` },
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div">
              UISP-Omega Bridge
            </Typography>
          </Toolbar>
        </AppBar>

        {/* Mobile drawer */}
        <NavigationDrawer
          open={mobileOpen}
          onClose={handleDrawerToggle}
          variant="temporary"
        />

        {/* Desktop drawer */}
        <NavigationDrawer
          open={true}
          onClose={() => {}}
          variant="permanent"
        />

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { md: `calc(100% - ${drawerWidth}px)` },
          }}
        >
          <Toolbar />
          <Routes>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/clients" element={<ClientsPage />} />
            <Route path="/invoices" element={<InvoicesPage />} />
            <Route path="/sync" element={<SyncPage />} />
            <Route path="/bank" element={<BankPage />} />
            <Route path="/payments" element={<PaymentsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </Box>
      </Box>
    </Router>
  );
}

export default AppWithNavigation;
