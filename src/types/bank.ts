import type { BankFileType, TransactionType } from './common';

// Bank statement types
export interface BankStatement {
  id: number;
  accountIban: string;
  statementDate: Date;
  openingBalance: number;
  closingBalance: number;
  fileName: string;
  fileType: BankFileType;
  processed: boolean;
  omegaImported: boolean;
  createdAt: Date;
  transactions?: BankTransaction[];
}

export interface BankTransaction {
  id: number;
  statementId: number;
  transactionDate: Date;
  amount: number;
  currency: string;
  debitCredit: TransactionType;
  variableSymbol?: string;
  specificSymbol?: string;
  constantSymbol?: string;
  counterpartyName?: string;
  counterpartyAccount?: string;
  counterpartyBankCode?: string;
  reference?: string;
  description?: string;
  matchedInvoiceId?: number;
  omegaImported: boolean;
  createdAt: Date;
  statement?: BankStatement;
  matchedInvoice?: Invoice;
}

// XML SEPA format types (Tatra Banka XML)
export interface SepaDocument {
  Document: {
    BkToCstmrStmt: SepaBankToCustomerStatement[];
  };
}

export interface SepaBankToCustomerStatement {
  GrpHdr: SepaGroupHeader;
  Stmt: SepaStatement[];
}

export interface SepaGroupHeader {
  MsgId: string;
  CreDtTm: string;
  MsgRcpt?: SepaParty;
}

export interface SepaStatement {
  Id: string;
  ElctrncSeqNb?: string;
  LglSeqNb?: string;
  CreDtTm: string;
  FrToDt?: SepaDateTimePeriod;
  CpyDplctInd?: string;
  RptgSrc?: SepaReportingSource;
  Acct: SepaAccount;
  RltdAcct?: SepaAccount;
  Intrst?: SepaInterest[];
  Bal: SepaBalance[];
  TxsSummry?: SepaTransactionsSummary;
  Ntry?: SepaEntry[];
  AddtlStmtInf?: string;
}

export interface SepaEntry {
  NtryRef?: string;
  Amt: SepaAmount;
  CdtDbtInd: 'CRDT' | 'DBIT';
  RvslInd?: boolean;
  Sts: string;
  BookgDt?: SepaDateAndDateTimeChoice;
  ValDt?: SepaDateAndDateTimeChoice;
  AcctSvcrRef?: string;
  Avlbty?: SepaAvailability[];
  BkTxCd?: SepaBankTransactionCode;
  ComssnWvrInd?: boolean;
  AddtlInfInd?: SepaMessageIdentification;
  AmtDtls?: SepaAmountDetails;
  Chrgs?: SepaCharges;
  TechInptChanl?: SepaTechnicalInputChannel;
  Intrst?: SepaTransactionInterest;
  NtryDtls?: SepaEntryDetails[];
  AddtlNtryInf?: string;
}

export interface SepaAmount {
  _: string; // amount value
  $: {
    Ccy: string; // currency code
  };
}

export interface SepaEntryDetails {
  Btch?: SepaBatchInformation;
  TxDtls?: SepaTransactionDetails[];
}

export interface SepaTransactionDetails {
  Refs?: SepaTransactionReferences;
  AmtDtls?: SepaAmountDetails;
  Avlbty?: SepaAvailability;
  BkTxCd?: SepaBankTransactionCode;
  Chrgs?: SepaCharges;
  Intrst?: SepaTransactionInterest;
  RltdPties?: SepaTransactionParties;
  RltdAgts?: SepaTransactionAgents;
  Purp?: SepaPurpose;
  RltdRmtInf?: SepaRemittanceLocation[];
  RmtInf?: SepaRemittanceInformation;
  RltdDts?: SepaTransactionDates;
  RltdPric?: SepaTransactionPrice;
  RltdQties?: SepaTransactionQuantities;
  FinInstrmId?: SepaSecurityIdentification;
  Tax?: SepaTaxInformation;
  RtrInf?: SepaPaymentReturnInformation;
  CorpActn?: SepaCorporateActionInformation;
  SfkpgAcct?: SepaSecuritiesAccount;
  CshDpst?: SepaCashDeposit[];
  CardTx?: SepaCardTransaction;
  AddtlTxInf?: string;
}

export interface SepaTransactionReferences {
  MsgId?: string;
  AcctSvcrRef?: string;
  PmtInfId?: string;
  InstrId?: string;
  EndToEndId?: string;
  TxId?: string;
  MndtId?: string;
  ChqNb?: string;
  ClrSysRef?: string;
  AcctOwnrTxId?: string;
  AcctSvcrTxId?: string;
  MktInfrstrctrTxId?: string;
  PrcgId?: string;
  Prtry?: SepaProprietaryReference[];
}

export interface SepaRemittanceInformation {
  Ustrd?: string[];
  Strd?: SepaStructuredRemittanceInformation[];
}

export interface SepaStructuredRemittanceInformation {
  RfrdDocInf?: SepaReferredDocumentInformation[];
  RfrdDocAmt?: SepaRemittanceAmount;
  CdtrRefInf?: SepaCreditorReferenceInformation;
  Invcr?: SepaPartyIdentification;
  Invcee?: SepaPartyIdentification;
  TaxRmt?: SepaTaxInformation;
  GrnshmtRmt?: SepaGarnishmentRemittanceInformation;
  AddtlRmtInf?: string[];
}

// SWIFT MT940 format types
export interface SwiftMT940Statement {
  transactionReferenceNumber: string; // :20:
  accountIdentification: string; // :25:
  statementNumber: string; // :28C:
  openingBalance: SwiftBalance; // :60F: or :60M:
  statementLines: SwiftStatementLine[]; // :61:
  closingBalance: SwiftBalance; // :62F: or :62M:
  closingAvailableBalance?: SwiftBalance; // :64:
  informationToAccountOwner?: string[]; // :86:
}

export interface SwiftBalance {
  debitCreditMark: 'C' | 'D';
  date: string;
  currency: string;
  amount: number;
}

export interface SwiftStatementLine {
  valueDate: string;
  entryDate?: string;
  debitCreditMark: 'C' | 'D';
  fundsCode?: string;
  amount: number;
  transactionTypeCode: string;
  referenceForAccountOwner: string;
  referenceOfAccountServicingInstitution?: string;
  supplementaryDetails?: string;
  informationToAccountOwner?: string[];
}

// XLS format types (simplified structure)
export interface XlsBankStatement {
  accountNumber: string;
  statementDate: Date;
  transactions: XlsBankTransaction[];
}

export interface XlsBankTransaction {
  processingDate: Date;
  valueDate: Date;
  amount: number;
  currency: string;
  type: string;
  accountNumber: string;
  counterpartyName: string;
  counterpartyAccountNumber?: string;
  bankCode?: string;
  swiftCode?: string;
  variableSymbol?: string;
  specificSymbol?: string;
  constantSymbol?: string;
  payerReference?: string;
  payeeInformation?: string;
  paymentType?: string;
  transactionDescription?: string;
  paymentReference?: string;
  originalAmount?: number;
  originalCurrency?: string;
  merchantLocation?: string;
  cardNumber?: string;
  fee?: number;
  transactionFee?: number;
}

// Parser result types
export interface BankStatementParseResult {
  statement: Omit<BankStatement, 'id' | 'createdAt'>;
  transactions: Omit<BankTransaction, 'id' | 'statementId' | 'createdAt'>[];
  errors?: string[];
  warnings?: string[];
}

// Additional helper types
export interface SepaParty {
  Nm?: string;
  PstlAdr?: SepaPostalAddress;
  Id?: SepaPartyIdentification;
  CtryOfRes?: string;
  CtctDtls?: SepaContactDetails;
}

export interface SepaAccount {
  Id: SepaAccountIdentification;
  Tp?: SepaAccountType;
  Ccy?: string;
  Nm?: string;
  Prxy?: SepaProxyAccountIdentification;
}

export interface SepaAccountIdentification {
  IBAN?: string;
  Othr?: SepaGenericAccountIdentification;
}

export interface SepaBalance {
  Tp: SepaBalanceType;
  Amt: SepaAmount;
  CdtDbtInd: 'CRDT' | 'DBIT';
  Dt: SepaDateAndDateTimeChoice;
}

export interface SepaDateAndDateTimeChoice {
  Dt?: string;
  DtTm?: string;
}

// Additional interfaces for completeness
export interface SepaPostalAddress {
  AdrTp?: string;
  Dept?: string;
  SubDept?: string;
  StrtNm?: string;
  BldgNb?: string;
  BldgNm?: string;
  Flr?: string;
  PstBx?: string;
  Room?: string;
  PstCd?: string;
  TwnNm?: string;
  TwnLctnNm?: string;
  DstrctNm?: string;
  CtrySubDvsn?: string;
  Ctry?: string;
  AdrLine?: string[];
}

export interface SepaPartyIdentification {
  Nm?: string;
  PstlAdr?: SepaPostalAddress;
  Id?: SepaPartyIdentification2;
  CtryOfRes?: string;
  CtctDtls?: SepaContactDetails;
}

export interface SepaContactDetails {
  NmPrfx?: string;
  Nm?: string;
  PhneNb?: string;
  MobNb?: string;
  FaxNb?: string;
  EmailAdr?: string;
  EmailPurp?: string;
  JobTitl?: string;
  Rspnsblty?: string;
  Dept?: string;
  Othr?: SepaOtherContact[];
}

// Placeholder interfaces - would need to be expanded based on full SEPA specification
export interface SepaDateTimePeriod {
  FrDtTm?: string;
  ToDtTm?: string;
}

export interface SepaReportingSource {
  Prtry?: string;
}

export interface SepaInterest {
  // Implementation depends on specific requirements
}

export interface SepaTransactionsSummary {
  // Implementation depends on specific requirements
}

export interface SepaAvailability {
  // Implementation depends on specific requirements
}

export interface SepaBankTransactionCode {
  // Implementation depends on specific requirements
}

export interface SepaMessageIdentification {
  // Implementation depends on specific requirements
}

export interface SepaAmountDetails {
  // Implementation depends on specific requirements
}

export interface SepaCharges {
  // Implementation depends on specific requirements
}

export interface SepaTechnicalInputChannel {
  // Implementation depends on specific requirements
}

export interface SepaTransactionInterest {
  // Implementation depends on specific requirements
}

export interface SepaBatchInformation {
  // Implementation depends on specific requirements
}

export interface SepaTransactionParties {
  // Implementation depends on specific requirements
}

export interface SepaTransactionAgents {
  // Implementation depends on specific requirements
}

export interface SepaPurpose {
  // Implementation depends on specific requirements
}

export interface SepaRemittanceLocation {
  // Implementation depends on specific requirements
}

export interface SepaTransactionDates {
  // Implementation depends on specific requirements
}

export interface SepaTransactionPrice {
  // Implementation depends on specific requirements
}

export interface SepaTransactionQuantities {
  // Implementation depends on specific requirements
}

export interface SepaSecurityIdentification {
  // Implementation depends on specific requirements
}

export interface SepaTaxInformation {
  // Implementation depends on specific requirements
}

export interface SepaPaymentReturnInformation {
  // Implementation depends on specific requirements
}

export interface SepaCorporateActionInformation {
  // Implementation depends on specific requirements
}

export interface SepaSecuritiesAccount {
  // Implementation depends on specific requirements
}

export interface SepaCashDeposit {
  // Implementation depends on specific requirements
}

export interface SepaCardTransaction {
  // Implementation depends on specific requirements
}

export interface SepaProprietaryReference {
  // Implementation depends on specific requirements
}

export interface SepaReferredDocumentInformation {
  // Implementation depends on specific requirements
}

export interface SepaRemittanceAmount {
  // Implementation depends on specific requirements
}

export interface SepaCreditorReferenceInformation {
  // Implementation depends on specific requirements
}

export interface SepaGarnishmentRemittanceInformation {
  // Implementation depends on specific requirements
}

export interface SepaGenericAccountIdentification {
  // Implementation depends on specific requirements
}

export interface SepaAccountType {
  // Implementation depends on specific requirements
}

export interface SepaProxyAccountIdentification {
  // Implementation depends on specific requirements
}

export interface SepaBalanceType {
  // Implementation depends on specific requirements
}

export interface SepaPartyIdentification2 {
  // Implementation depends on specific requirements
}

export interface SepaOtherContact {
  // Implementation depends on specific requirements
}
