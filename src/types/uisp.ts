// UISP CRM API types based on the documentation

export interface UispClient {
  id: number;
  clientType: number;
  isLead: boolean;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  companyRegistrationNumber?: string;
  companyTaxId?: string;
  street1?: string;
  street2?: string;
  city?: string;
  countryId?: number;
  stateId?: number;
  zipCode?: string;
  invoiceStreet1?: string;
  invoiceStreet2?: string;
  invoiceCity?: string;
  invoiceCountryId?: number;
  invoiceStateId?: number;
  invoiceZipCode?: string;
  note?: string;
  sendInvoiceByPost: boolean;
  invoiceMaturityDays: number;
  stopServiceDue: boolean;
  stopServiceDueDelayed: boolean;
  accountStandingsCredit: number;
  accountStandingsRefundableCredit: number;
  currencyCode: string;
  organizationId: number;
  isActive: boolean;
  createdDate: string;
  avatarColor?: string;
}

export interface UispInvoiceItem {
  id?: number;
  label: string;
  price: number;
  quantity: number;
  unit?: string;
  tax1?: number;
  tax2?: number;
  tax3?: number;
  total: number;
  discountLabel?: string;
  discountPrice?: number;
  discountInvoiceLabel?: string;
  discountType?: string;
  discountValue?: number;
}

export interface UispInvoiceItemCollectionWritable {
  label: string;
  price: number;
  quantity: number;
  unit?: string;
  tax1?: number;
  tax2?: number;
  tax3?: number;
  discountLabel?: string;
  discountPrice?: number;
  discountInvoiceLabel?: string;
  discountType?: string;
  discountValue?: number;
}

export interface UispInvoiceNew {
  clientId: number;
  number?: string;
  createdDate?: string;
  dueDate?: string;
  taxableSupplyDate?: string;
  currencyCode?: string;
  exchangeRate?: number;
  organizationBankAccountId?: number;
  maturityDays?: number;
  notes?: string;
  adminNotes?: string;
  discount?: number;
  discountLabel?: string;
  items: UispInvoiceItemCollectionWritable[];
  attributes?: Record<string, unknown>;
  proformaInvoiceId?: number;
}

export interface UispInvoiceReadOnly {
  id: number;
  number: string;
  clientId: number;
  status: number;
  createdDate: string;
  dueDate: string;
  taxableSupplyDate?: string;
  paidDate?: string;
  currencyCode: string;
  exchangeRate: number;
  organizationBankAccountId?: number;
  maturityDays: number;
  notes?: string;
  adminNotes?: string;
  discount: number;
  discountLabel?: string;
  subtotal: number;
  taxes: number;
  total: number;
  amountPaid: number;
  amountDue: number;
  items: UispInvoiceItem[];
  attributes?: Record<string, unknown>;
  emailSentDate?: string;
  emailSentCount: number;
  isProforma: boolean;
  proformaInvoiceId?: number;
  organizationId: number;
  templateId?: number;
  generatedInvoiceId?: number;
  uncollectible: boolean;
  void: boolean;
  client?: UispClient;
}

// Invoice status enum
export enum UispInvoiceStatus {
  DRAFT = 0,
  UNPAID = 1,
  PARTIAL = 2,
  PAID = 3,
  OVERDUE = 4,
  VOID = 5,
  PROFORMA = 6,
}

// API request/response types
export interface UispApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface UispInvoiceListParams {
  clientId?: number;
  organizationId?: number;
  status?: UispInvoiceStatus;
  createdDateFrom?: string;
  createdDateTo?: string;
  dueDateFrom?: string;
  dueDateTo?: string;
  number?: string;
  limit?: number;
  offset?: number;
  order?: string;
  direction?: 'ASC' | 'DESC';
}

export interface UispClientListParams {
  organizationId?: number;
  isLead?: boolean;
  isActive?: boolean;
  limit?: number;
  offset?: number;
  order?: string;
  direction?: 'ASC' | 'DESC';
}

// Tax configuration
export interface UispTax {
  id: number;
  name: string;
  agency: string;
  rate: number;
  isCompound: boolean;
  isReplaced: boolean;
  replacedByTaxId?: number;
}

// Organization (company) info
export interface UispOrganization {
  id: number;
  name: string;
  registrationNumber?: string;
  taxId?: string;
  website?: string;
  email?: string;
  phone?: string;
  street1?: string;
  street2?: string;
  city?: string;
  countryId?: number;
  stateId?: number;
  zipCode?: string;
  currencyCode: string;
  locale: string;
  selected: boolean;
}

// Bank account info
export interface UispBankAccount {
  id: number;
  name: string;
  field1?: string;
  field2?: string;
  organizationId: number;
  isSelected: boolean;
}
