// Common types used across the application

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface DateRange {
  from?: Date;
  to?: Date;
}

export interface FilterParams extends PaginationParams {
  search?: string;
  dateRange?: DateRange;
  status?: string;
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

// File upload types
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer?: Buffer;
}

// Audit fields
export interface AuditFields {
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

// Status enums
export enum ProcessingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum SyncStatus {
  NOT_SYNCED = 'not_synced',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  SYNC_FAILED = 'sync_failed',
}

// Currency and amount types
export interface MonetaryAmount {
  amount: number;
  currency: string;
}

export interface VATAmount {
  base: number;
  rate: number;
  amount: number;
}

// VAT rates used in Slovakia
export enum VATRate {
  ZERO = '0',      // 0% - nulová sadzba
  LOWER = 'N',     // 10% - nižšia sadzba  
  HIGHER = 'V',    // 20% - vyššia sadzba
  EXEMPT = 'X',    // bez DPH
  LOWER2 = 'Y',    // 5% - znížená sadzba 2
}

// Document types for Omega
export enum OmegaDocumentType {
  CUSTOMER_INVOICE = '0',        // odberateľská faktúra
  ADVANCE_INVOICE = '1',         // preddavková faktúra
  DELIVERY_NOTE = '2',           // dodací list
  ORDER_SUBMITTED = '3',         // odoslaná objednávka
  CREDIT_NOTE = '4',             // dobropis
  CUSTOM_LIST = '5',             // vlastný zoznam
  WINDING_SHEET = '6',           // výdajka
  CLAIM = '7',                   // reklamácia
  REMINDER = '8',                // upomienka
  PENALTY_INVOICE = '9',         // penalizačná faktúra
  CANCELLATION_INVOICE = '10',   // storno faktúra
  INCOMING_ORDER = '11',         // došlá objednávka
  QUOTATION = '12',              // cenová ponuka
  SALES_RECEIPT = '13',          // predajka
  PURCHASE_INVOICE = '14',       // došlá faktúra
}

// Bank transaction types
export enum TransactionType {
  DEBIT = 'DBIT',
  CREDIT = 'CRDT',
}

export enum BankFileType {
  XML = 'XML',
  SWIFT = 'SWIFT',
  XLS = 'XLS',
}

// Operation types for logging
export enum OperationType {
  SYNC_INVOICES = 'sync_invoices',
  IMPORT_BANK = 'import_bank',
  MATCH_PAYMENTS = 'match_payments',
  EXPORT_OMEGA = 'export_omega',
  MANUAL_MATCH = 'manual_match',
  CONFIG_UPDATE = 'config_update',
}

export enum OperationStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
}
