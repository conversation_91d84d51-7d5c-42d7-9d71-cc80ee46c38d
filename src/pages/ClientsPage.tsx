import React from 'react';
import {
  Container,
  Typography,
  Box,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  Home as HomeIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import ClientsList from '../components/ClientsList';

export function ClientsPage() {
  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            sx={{ display: 'flex', alignItems: 'center' }}
            color="inherit"
            href="/"
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Dashboard
          </Link>
          <Typography
            sx={{ display: 'flex', alignItems: 'center' }}
            color="text.primary"
          >
            <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Klienti
          </Typography>
        </Breadcrumbs>
        
        <Typography variant="h4" component="h1" gutterBottom>
          S<PERSON><PERSON><PERSON><PERSON> klientov
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Prehľad všetkých klientov synchronizovaných z UISP CRM systému
        </Typography>
      </Box>

      <ClientsList />
    </Container>
  );
}

export default ClientsPage;
