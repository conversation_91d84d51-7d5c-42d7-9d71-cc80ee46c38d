import React from 'react';
import {
  Container,
  Typography,
  Box,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  Home as HomeIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import InvoicesList from '../components/InvoicesList';

export function InvoicesPage() {
  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            sx={{ display: 'flex', alignItems: 'center' }}
            color="inherit"
            href="/"
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Dashboard
          </Link>
          <Typography
            sx={{ display: 'flex', alignItems: 'center' }}
            color="text.primary"
          >
            <ReceiptIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Faktúry
          </Typography>
        </Breadcrumbs>
        
        <Typography variant="h4" component="h1" gutterBottom>
          Správa faktúr
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Prehľad všetkých faktúr synchronizovaných z UISP CRM systému
        </Typography>
      </Box>

      <InvoicesList />
    </Container>
  );
}

export default InvoicesPage;
